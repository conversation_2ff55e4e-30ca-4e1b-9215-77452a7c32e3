import React from 'react';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { MOCK_OUTWARD_REMITTANCE_DATA } from '../../../Mocks/MockData';
import { outwardRemittanceHeaders } from '../../../Utils/Constants/TableHeaders';
import withFilters from '../../../HOC/withFilters ';

const OutwardRemittanceTable = ({ pagination }) => {
  const tableData = MOCK_OUTWARD_REMITTANCE_DATA;
  const isLoading = false;
  const isError = false;
  return (
    <div>
      <CustomTable
        headers={outwardRemittanceHeaders}
        pagination={pagination}
        isLoading={isLoading}
        hasFilters={false}
        hideItemsPerPage
        hideSearch
      >
        {(tableData?.length || isError) && (
          <tbody>
            {isError && (
              <tr>
                <td colSpan={outwardRemittanceHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {tableData?.map((item) => (
              <tr key={item.id}>
                <td>{item.buy_fcy}</td>
                <td
                  style={{ textDecoration: 'underline' }}
                  className="cp"
                  onClick={() => {
                    console.log(item.id);
                    // navigate(`/transactions/outward-remittance/${item.id}`);
                  }}
                >
                  {item.fsn_no}
                </td>
                <td>{item.debit_ledger}</td>
                <td>{item.debit_account}</td>
                <td>{item.reference_no}</td>
                <td>{item.beneficiary}</td>
                <td>{item.sending_fc_amount}</td>
                <td>{item.against_amount}</td>
                <td>{item.charges}</td>
                <td>{item.vat}</td>
                <td>{item.net_total}</td>
                <td>{item.user_id}</td>
                <td>{item.time}</td>
                <td>{item.attachment}</td>
              </tr>
            ))}
          </tbody>
        )}
      </CustomTable>
    </div>
  );
};

export default withFilters(OutwardRemittanceTable);
