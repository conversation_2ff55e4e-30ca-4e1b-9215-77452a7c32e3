import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { exchangeProfitLossReportData } from '../../../../Mocks/MockData';
import { exchangeProfitLossReportHeaders } from '../../../../Utils/Constants/TableHeaders';
import CustomButton from '../../../../Components/CustomButton';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import withFilters from '../../../../HOC/withFilters ';

const ExchangeProfitLossReport = ({ filters, setFilters, pagination }) => {
  const tableData = exchangeProfitLossReportData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">
          Exchange Profit & Loss Report
        </h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={exchangeProfitLossReportHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              {
                title: 'Report Type',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'Currency',
                options: [{ value: 'All', label: 'All' }],
              },
            ]}
            dateFilters={[{ title: 'Period Range' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={exchangeProfitLossReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.currency}</td>
                    <td>{item.opening_bal}</td>
                    <td>{item.open_rate}</td>
                    <td>{item.open_in_lc}</td>
                    <td>{item.total_buy}</td>
                    <td>{item.avg_buy_rate}</td>
                    <td>{item.buy_in_lc}</td>
                    <td>{item.total_sell}</td>
                    <td>{item.avg_sell_rate}</td>
                    <td>{item.sell_in_lc}</td>
                    <td>{item.closing_bal}</td>
                    <td>{item.avg_close_lc}</td>
                    <td>{item.close_in_lc}</td>
                    <td>{item.cost_of_sale}</td>
                    <td>{item.profit_loss}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(ExchangeProfitLossReport);
