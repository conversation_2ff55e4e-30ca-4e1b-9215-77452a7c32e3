import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ErrorMessage, Form, Formik } from 'formik';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { showToast } from '../../../Components/Toast/Toast';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import withModal from '../../../HOC/withModal';
import useCurrencyRate from '../../../Hooks/useCurrencyRate';
import {
  createReceiptVoucher,
  getBenefeciariesByAccount,
} from '../../../Services/Transaction/ReceiptVoucher';
import useFormStore from '../../../Stores/FormStore';
import useSettingsStore from '../../../Stores/SettingsStore';
import { showErrorToast } from '../../../Utils/Utils';

const NewReceiptVoucher = ({
  date,
  state,
  showModal,
  getAccountsByTypeOptions,
  getCOAAccountsByModeOptions,
  currencyOptions,
  vatData,
  isDisabled = false,
  setIsDisabled,
  setPageState,
  setSearchTerm,
  setShowAddLedgerModal,
  setCurrencyToSelect,
  setShowMissingCurrencyRateModal,
  newlyCreatedAccount,
  newlyCreatedBeneficiary,
  lastVoucherNumbers,
  updatePrintSetting,
  onFormDataChange,
  restoreValuesFromStore,
}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const formikRef = useRef();

  // Access the form store
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'receipt-voucher'; // Unique identifier for this form

  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [selectedLedgerAccount, setSelectedLedgerAccount] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [hasShownModal, setHasShownModal] = useState(false);
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachments, setAddedAttachments] = useState(null);
  const [outOfScope, setOutOfScope] = useState('');
  const [specialCommissionValues, setSpecialCommissionValues] = useState({});
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);

  // load saved form if present
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);

    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);
      let specialCommissionData = {};

      specialCommissionData.commission_type = savedValues?.commission_type;
      specialCommissionData.amount = savedValues?.amount;
      specialCommissionData.ledger = [
        { label: 'PL', value: 'party' },
        { label: 'GL', value: 'general' },
        { label: 'WIC', value: 'walkin' },
      ].find((x) => savedValues.ledger == x.value);
      specialCommissionData.account_id = getAccountsByTypeOptions(
        specialCommissionData?.ledger?.value
      ).find((x) => x.value == savedValues?.account_id);

      specialCommissionData.currency = currencyOptions.find(
        (x) => x.value == savedValues?.currency_id
      );

      setSpecialCommissionValues(specialCommissionData);
      formikRef.current.setValues(savedValues);
      setIsDisabled(false);

      if (hasFormValues('special-commission')) {
        setAddedSpecialCommissionValues(getFormValues('special-commission'));
      }
      // Clear lastVisitedPage so it doesn't persist beyond one use
      clearLastVisitedPage(formId);
      clearFormValues(formId);
    }
  }, []);

  useEffect(() => {
    if (lastVoucherNumbers?.current) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        current: lastVoucherNumbers?.current,
      }));
    }
    if (date) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        date: date,
      }));
      // Set due_date to date if mode is Bank
      if (formikRef.current?.values.mode === 'Bank') {
        formikRef.current.setFieldValue('due_date', date);
      }
    }
  }, [lastVoucherNumbers?.current, date]);

  // Fetch currency rate for the selected Currency
  const { data: currencyRate, isLoading: isLoadingCurrencyRate } =
    useCurrencyRate(selectedCurrency, date);

  // Get Benefeciaries from selected Ledger+Account
  const {
    data: beneficiaryAccounts,
    isLoading: isLoadingBeneficiary,
    isError: isErrorBeneficiary,
    error: errorBeneficiary,
  } = useQuery({
    queryKey: ['beneficiaries', selectedLedgerAccount],
    queryFn: () => getBenefeciariesByAccount(selectedLedgerAccount),
    enabled: !!selectedLedgerAccount,
  });

  // Make options array from the benfeciary queries call
  const getBeneficiaryOptions = (account_id) => {
    if (!account_id) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const data = beneficiaryAccounts;
    const loading = isLoadingBeneficiary;
    const error = isErrorBeneficiary;
    const errorMessage = errorBeneficiary;

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch beneficiaries', errorMessage);
      return [{ label: 'Unable to fetch beneficiaries', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];

    options.push({
      label: `Add New Beneficiary`,
      value: null,
    });

    return options;
  };

  // Modify the useEffect to include hasShownModal in its dependencies
  useEffect(() => {
    if (
      selectedCurrency &&
      currencyRate &&
      !currencyRate?.rate &&
      !hasShownModal
    ) {
      formikRef.current.setFieldValue('currency_id', '');
      setCurrencyToSelect(selectedCurrency);
      setShowMissingCurrencyRateModal(true);
      setHasShownModal(true);
    }
  }, [selectedCurrency, currencyRate?.rate, hasShownModal]);

  const handleResetRows = () => {
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // Clear saved form values when resetting
    clearFormValues(formId);
    clearFormValues('special-commission');
    setAddedSpecialCommissionValues(null);
  };

  const createReceiptVoucherMutation = useMutation({
    mutationFn: createReceiptVoucher,
    onSuccess: (data) => {
      showToast('Receipt Voucher Created!', 'success');
      if (getPrintSettings('receipt_voucher')) {
        window.open(data.detail?.pdf_url, '_blank');
      }
      queryClient.invalidateQueries(['receiptVoucherListing']);
      handleResetRows();
    },
    onError: (error) => {
      console.error('Error creating Receipt Voucher', error);
      if (
        error.message.toLowerCase() ==
        'receipt voucher limit reached for this branch.'
      ) {
        showModal(
          'Cannot Create',
          'The maximum number of receipt vouchers has been reached. To create new transactions, please increase the transaction number count in the Transaction Number Register.',
          null,
          'error'
        );
      } else {
        showErrorToast(error);
      }
    },
  });

  // Restore form data from store for Rate of Exchange flow
  useEffect(() => {
    if (restoreValuesFromStore) {
      const savedFormData = getFormValues(formId);
      if (savedFormData && formikRef.current) {
        formikRef.current.setValues(savedFormData.values || {});
        setAddedAttachments(savedFormData.addedAttachments || null);
        setIsDisabled(false);
        clearFormValues(formId);
        clearLastVisitedPage(formId);
      }
    }
  }, [restoreValuesFromStore]);
  // ...existing code...

  // Notify parent of form data changes (for saving before navigation)
  useEffect(() => {
    if (onFormDataChange && formikRef.current) {
      onFormDataChange({
        values: formikRef.current.values,
        addedAttachments,
      });
    }
  }, [formikRef.current?.values, addedAttachments, onFormDataChange]);

  const handleSubmit = async () => {
    if (!formikRef.current) return;
    // Validate the form
    const errors = await formikRef.current.validateForm();
    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show errors
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return; // Do not submit if there are errors
    }

    const formValues = formikRef.current.values;
    let payload = {
      date,
      ...formValues,
      ...addedAttachments,
      ...(formValues.vat_terms.startsWith('A small popup') && {
        out_of_scope_reason: outOfScope,
      }),
      vat_amount: formValues.vat_amount,
      ...(addedSpecialCommissionValues
        ? { special_commission: addedSpecialCommissionValues }
        : {}),
    };

    createReceiptVoucherMutation.mutate(payload);
  };
  const handleVatOutOfScope = (values) => {
    setOutOfScope(values.out_of_scope);
    setShowVatOutOfScopeModal(false);
  };
  const handleNavigateToSpecialCommissionPage = () => {
    // Check if required fields are filled
    const requiredFields = [
      'ledger',
      'account_id',
      'amount',
      'currency_id',
      // 'commission_type',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    if (missingFields.length > 0) {
      // Set touched for all required fields to show errors
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      return;
    }

    // If all required fields are filled, proceed
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, formikRef.current.values);
      setLastVisitedPage(formId, 'special-commission');
    }

    // Check if we already have special commission data
    const hasExistingCommission = !!addedSpecialCommissionValues;
    console.log('specialCommissionValues', specialCommissionValues)
    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'rv',
        values: {
          rvValues: specialCommissionValues,
          isEdit: hasExistingCommission,
        },
      },
    });
  };

  const getVATTermsOptions = () => {
    if (vatData.isLoadingVatType)
      return [
        {
          label: 'Loading...',
          value: '',
        },
      ];
    if (vatData.isErrorVatType) {
      console.error('Unable to fetch VAT Terms', vatData.errorMessage);
      return [{ label: 'Unable to fetch VAT Terms', value: null }];
    }
    return vatData?.vatType?.vats?.map((item) => ({
      label: `${item.title}${
        !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
      }`,
      value: item.percentage,
    }));
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            ledger: '',
            account_id: '',
            received_from: '',
            mode: '',
            mode_account_id: '',
            party_bank: '',
            cheque_number: '',
            due_date: date,
            narration: '',

            // currency: '',
            amount: '',
            currency_id: '',
            commission_type: '',
            commission: '',
            commission_amount: '',
            vat_terms: '',
            vat_percentage: '',
            vat_amount: '',
            net_total: '',
            comment: '',
          }}
          validate={(values) => {
            const errors = {};

            // Required fields for special commission
            if (!values.ledger) errors.ledger = 'Ledger is required';
            if (!values.account_id) errors.account_id = 'Account is required';
            if (!values.amount) errors.amount = 'Amount is required';
            if (!values.currency_id)
              errors.currency_id = 'Currency is required';
            // if (!values.commission_type)
            //   errors.commission_type = 'Commission Type is required';

            return errors;
          }}
        >
          {({
            values,
            touched,
            errors,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // --- VAT Calculation useEffect inside Formik render ---
            React.useEffect(() => {
              let commissionAmount =
                addedSpecialCommissionValues?.total_commission ??
                values.commission_amount;

              let vatPercentage =
                vatData.vatType?.vat_percentage ||
                (!isNaN(values.vat_terms) ? values.vat_terms : 0);

              let vatAmount =
                commissionAmount && vatPercentage
                  ? (commissionAmount * vatPercentage) / 100
                  : '';

              setFieldValue('vat_amount', vatAmount);
            }, [
              addedSpecialCommissionValues?.total_commission,
              values.commission_amount,
              values.vat_terms,
              vatData.vatType?.vat_percentage,
              setFieldValue,
            ]);
            // --- End VAT Calculation useEffect ---
            return (
              <Form>
                <div className="row">
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                    <div className="row">
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Ledger"
                          type1="select"
                          type2="select"
                          name1="ledger"
                          name2="account_id"
                          value1={values.ledger}
                          value2={values.account_id || newlyCreatedAccount?.id}
                          options1={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          options2={getAccountsByTypeOptions(values.ledger)}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Select Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('ledger', selected.value);
                              setSpecialCommissionValues((prev) => ({
                                ...prev,
                                ledger: selected,
                                account_id: '',
                              }));
                            }
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('account_id', selected.value);
                              setSelectedLedgerAccount(selected.value);
                              setSpecialCommissionValues((prev) => ({
                                ...prev,
                                account_id: selected,
                              }));
                            }
                          }}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'received_from'}
                          label={'Received From'}
                          options={getBeneficiaryOptions(values.account_id)}
                          isDisabled={isDisabled}
                          placeholder={'Select Received From'}
                          value={
                            values.received_from || newlyCreatedBeneficiary?.id
                          }
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('received_from', selected.value);
                            }
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      {/* Combined Mode and Paid To Account Select */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Mode"
                          type1="select"
                          type2="select"
                          name1="mode"
                          name2="mode_account_id"
                          value1={values.mode}
                          value2={values.mode_account_id}
                          options1={[
                            {
                              label: 'Cash',
                              value: 'Cash',
                            },
                            {
                              label: 'Bank',
                              value: 'Bank',
                            },
                            {
                              label: 'PDC',
                              value: 'PDC',
                            },
                            {
                              label: 'Online',
                              value: 'Online',
                            },
                          ]}
                          options2={getCOAAccountsByModeOptions(values.mode)}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Select Mode"
                          placeholder2="Select Account"
                          className1="mode"
                          className2="account"
                          onChange1={(selected) => {
                            setFieldValue('mode', selected.value);
                            if (selected.value === 'Online') {
                              setFieldValue('cheque_no', '');
                            } else if (selected.value == 'Cash') {
                              setFieldValue('cheque_no', '');
                              setFieldValue('party_bank', '');
                            } else if (selected.value == 'Bank') {
                              setFieldValue('due_date', date);
                            } else if (selected.value == 'PDC') {
                              setFieldValue(
                                'due_date',
                                new Date(
                                  new Date(date).setDate(
                                    new Date(date).getDate() + 1
                                  )
                                )
                                  .toISOString()
                                  .split('T')[0]
                              );
                            }
                          }}
                          onChange2={(selected) => {
                            setFieldValue('mode_account_id', selected.value);
                            if (!values.narration) {
                              setFieldValue(
                                'narration',
                                'This is placeholder text for narration'
                              );
                            }
                          }}
                        />
                      </div>
                      {values.mode !== 'Cash' && (
                        <div className="col-12 col-sm-6 mb-3">
                          <CustomInput
                            name={'party_bank'}
                            label={'Party Bank'}
                            disabled={isDisabled}
                            placeholder={'Enter Party Bank'}
                            value={values.party_bank}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.party_bank && errors.party_bank}
                          />
                        </div>
                      )}
                      {(values.mode === 'Bank' || values.mode === 'PDC') && (
                        <div className="col-12 col-sm-6 mb-3">
                          <CustomInput
                            name={'cheque_number'}
                            label={'Cheque Number'}
                            disabled={isDisabled}
                            placeholder={'Enter Cheque Number'}
                            value={values.cheque_number}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                              touched.cheque_number && errors.cheque_number
                            }
                          />
                        </div>
                      )}
                      {!(
                        values.mode === 'Cash' || values.mode === 'Online'
                      ) && (
                        <div className="col-12 col-sm-6 mb-3">
                          {/* If Mode is PDC then due date should be greater than current date */}
                          {/* If Mode is Bank then the date should ONLY be the current (voucher) date */}
                          {/* Otherwise, the date is disabled */}
                          <CustomInput
                            name={'due_date'}
                            label={'Due Date'}
                            type={'date'}
                            min={
                              values.mode == 'PDC'
                                ? new Date(
                                    new Date(date).setDate(
                                      new Date(date).getDate() + 1
                                    )
                                  )
                                    .toISOString()
                                    .split('T')[0]
                                : new Date(date).toISOString().split('T')[0]
                            }
                            {...(values.mode == 'Bank' && {
                              max: new Date(date).toISOString().split('T')[0],
                            })}
                            disabled={isDisabled}
                            value={values.due_date}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.due_date && errors.due_date}
                          />
                        </div>
                      )}
                      <div className="col-12 mb-3">
                        <CustomInput
                          name={'narration'}
                          label={'Narration'}
                          type={'textarea'}
                          rows={1}
                          placeholder={'Enter Narration'}
                          disabled={isDisabled}
                          value={values.narration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.narration && errors.narration}
                        />
                      </div>
                      {/* Combined Currency and Amount Select */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Currency"
                          type1="select"
                          type2="input"
                          name1="currency_id"
                          name2="amount"
                          value1={values.currency_id}
                          value2={values.amount}
                          options1={currencyOptions}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Select Currency"
                          placeholder2="Enter Amount"
                          inputType2="number"
                          className1="currency"
                          className2="amount"
                          onChange1={(selected) => {
                            setSelectedCurrency(selected.value);
                            setHasShownModal(false);
                            setFieldValue('currency_id', selected.value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              currency: selected,
                            }));
                          }}
                          onChange2={(e) => {
                            handleChange(e);
                            let commission = parseFloat(values.commission || 0);
                            let amount = parseFloat(e.target.value || 0);
                            let commissionAmount = (commission / 100) * amount;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            let value =
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000;
                            setFieldValue('net_total', value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              amount,
                            }));
                          }}
                          additionalProps={{
                            isLoadingCurrencyRate: isLoadingCurrencyRate,
                          }}
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'commission_type'}
                          label={'Commission Type'}
                          options={[
                            {
                              label: 'Income',
                              value: 'Income',
                            },
                            {
                              label: 'Expense',
                              value: 'Expense',
                            },
                          ]}
                          isDisabled={isDisabled}
                          placeholder={'Select Commission Type'}
                          value={values.commission_type}
                          onChange={(selected) => {
                            setFieldValue('commission_type', selected.value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              commission_type: selected.value,
                            }));
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="commission_type"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                      {/* <div className="col-12 col-sm-6 mb-0 mb-sm-4" /> */}

                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Commission Percentage"
                          type1="input"
                          type2="input"
                          name1="commission"
                          name2="commission_amount"
                          value1={values.commission}
                          value2={values.commission_amount}
                          isDisabled={
                            isDisabled ||
                            addedSpecialCommissionValues?.total_commission
                          }
                          handleBlur={handleBlur}
                          placeholder1="Enter Commission %"
                          placeholder2="Commission Amount"
                          inputType1="number"
                          inputType2="text"
                          className1="commission"
                          className2="commission-amount"
                          inputProps1={{
                            min: 0,
                            max: 100,
                          }}
                          inputProps2={
                            {
                              // readOnly: true,
                            }
                          }
                          onChange1={(v) => {
                            handleChange(v);
                            if (v.target.value < 0) {
                              return;
                            }
                            let commission = parseFloat(v.target.value || 0);
                            let amount = parseFloat(values.amount || 0);
                            let commissionAmount = (commission / 100) * amount;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue(
                              'commission_amount',
                              commissionAmount
                            );
                          }}
                          onChange2={(e) => {
                            handleChange(e);
                            let commissionAmount = parseFloat(
                              e.target.value || 0
                            );
                            let amount = parseFloat(values.amount || 0);
                            let commission =
                              amount !== 0
                                ? (commissionAmount / amount) * 100
                                : 0;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue('commission', commission);
                          }}
                        />
                      </div>
                      {vatData?.vatType?.vat_type === 'variable' && (
                        <div className="col-12 col-sm-6 mb-45">
                          <SearchableSelect
                            name={'vat_terms'}
                            label={'VAT %'}
                            options={getVATTermsOptions()}
                            isDisabled={isDisabled}
                            placeholder={'Select VAT %'}
                            value={values.vat_terms}
                            onChange={(selected) => {
                              if (
                                selected.value.startsWith(
                                  'A small popup will appear'
                                )
                              ) {
                                setShowVatOutOfScopeModal(true);
                              } else {
                                let commission = parseFloat(
                                  values.commission || 0
                                );
                                let amount = parseFloat(
                                  values.amount ??
                                    getFormValues('special-commission')
                                      ?.total_commission ??
                                    0
                                );
                                let commissionAmount =
                                  (commission / 100) * amount;
                                let vat = vatData.vatType?.vat_percentage
                                  ? (commissionAmount *
                                      vatData.vatType?.vat_percentage) /
                                    100
                                  : !isNaN(selected.value)
                                  ? (commissionAmount * selected.value) / 100
                                  : '';

                                setFieldValue(
                                  'net_total',
                                  Math.round(
                                    (amount +
                                      commissionAmount +
                                      (vat || 0) +
                                      Number.EPSILON) *
                                      1000000
                                  ) / 1000000
                                );
                              }
                              setFieldValue('vat_terms', selected.value);
                            }}
                            onBlur={handleBlur}
                          />
                        </div>
                      )}
                      {vatData?.vatType?.vat_type === 'fixed' && (
                        <div className="col-12 col-sm-6 mb-3">
                          <CustomInput
                            name={'vat_percentage'}
                            label={'VAT %'}
                            type={'number'}
                            disabled={true}
                            placeholder={'Enter VAT Percentage'}
                            value={
                              vatData.vatType?.vat_percentage
                                ? vatData.vatType?.vat_percentage
                                : !isNaN(values.vat_terms)
                                ? values.vat_terms
                                : ''
                            }
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                      )}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'vat_amount'}
                          label={'VAT Amount'}
                          type={'text'}
                          disabled={true}
                          placeholder={'Enter VAT Amount'}
                          value={
                            vatData.isLoadingVatType
                              ? 'Loading...'
                              : (() => {
                                  const commissionAmount =
                                    addedSpecialCommissionValues?.total_commission ??
                                    values.commission_amount;
                                  const vatPercentage =
                                    vatData.vatType?.vat_percentage ||
                                    (!isNaN(values.vat_terms)
                                      ? values.vat_terms
                                      : 0);
                                  return commissionAmount && vatPercentage
                                    ? (commissionAmount * vatPercentage) / 100
                                    : '';
                                })()
                          }
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'net_total'}
                          label={'Net Total'}
                          type={'number'}
                          disabled={true}
                          placeholder={'Enter Net Total'}
                          value={values.net_total}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 mb-3">
                        <CustomInput
                          name={'comment'}
                          label={'Comment'}
                          type={'textarea'}
                          rows={4}
                          placeholder={'Enter Comment'}
                          disabled={isDisabled}
                          value={values.comment}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.comment && errors.comment}
                        />
                      </div>
                    </div>
                    <div className="d-flex mb-5">
                      <CustomButton
                        type={'button'}
                        onClick={handleNavigateToSpecialCommissionPage}
                        text={`${
                          !!addedSpecialCommissionValues ? 'Edit' : 'Add'
                        } Special Commission`}
                        disabled={!!values.commission || isDisabled}
                      />
                    </div>
                    {!!addedSpecialCommissionValues ? (
                      <p
                        className={`fs-5 ${
                          values.commission_type === 'Income'
                            ? 'text-success'
                            : 'text-danger'
                        }`}
                      >
                        {addedSpecialCommissionValues.commission}%{' '}
                        {values.commission_type === 'Income'
                          ? 'receivable'
                          : 'payable'}{' '}
                        commission of{' '}
                        {
                          currencyOptions.find(
                            (x) =>
                              x.value ==
                              addedSpecialCommissionValues.currency_id
                          )?.label
                        }{' '}
                        {addedSpecialCommissionValues.total_commission} on{' '}
                        {
                          currencyOptions.find(
                            (x) =>
                              x.value ==
                              addedSpecialCommissionValues.currency_id
                          )?.label
                        }{' '}
                        {addedSpecialCommissionValues.amount}
                      </p>
                    ) : null}
                  </div>
                  <div className="col-0  col-xxl-2" />
                  {!isDisabled && (
                    <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                      <div className="row">
                        {/* Right side cards */}
                        <div
                          className="col-12 mb-5"
                          style={{ maxWidth: '350px' }}
                        >
                          {/* <AccountBalanceCard />
                        <ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} /> */}
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="d-flex flex-wrap justify-content-start mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => {}}
                        readOnly={isDisabled}
                      />
                      <CustomCheckbox
                        label="Print"
                        checked={getPrintSettings('receipt_voucher')}
                        onChange={(e) => {
                          updatePrintSetting(
                            'receipt_voucher',
                            e.target.checked
                          );
                        }}
                        style={{ border: 'none', margin: 0 }}
                        readOnly={isDisabled}
                      />
                    </div>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleResetRows,
            variant: 'secondaryButton',
          },
        ]}
        loading={createReceiptVoucherMutation.isPending}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachments}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: '' }}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  <CustomButton type="submit" text={'Submit'} />
                  <CustomButton
                    variant={'secondaryButton'}
                    text={'Cancel'}
                    type={'button'}
                    onClick={() => {
                      setShowVatOutOfScopeModal(false);
                      formikRef.current.values.vat_terms = '';
                    }}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default withModal(NewReceiptVoucher);
