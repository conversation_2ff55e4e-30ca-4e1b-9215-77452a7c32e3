import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import { budgetingForecastingReportData } from '../../../Mocks/MockData';
import { budgetingForecastingReportHeaders } from '../../../Utils/Constants/TableHeaders';
import { Form, Formik } from 'formik';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';

const BudgetingForecastingReport = ({ filters, setFilters, pagination }) => {
  const [showAddProjectionModal, setShowAddProjectionModal] = useState(false);
  const tableData = budgetingForecastingReportData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">
          Budgeting & Forecasting Report
        </h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Add/Edit Projection'}
            variant={'secondaryButton'}
            onClick={() => {
              setShowAddProjectionModal(true);
              console.log('Add/Edit Projection');
            }}
          />
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            isLoading={isLoading}
            headers={budgetingForecastingReportHeaders}
            pagination={pagination}
            hideSearch
            selectOptions={[
              {
                title: 'Year',
                options: [{ value: 'All', label: 'All' }],
              },
            ]}
            hideItemsPerPage
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={budgetingForecastingReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item, i) => (
                  <tr key={item.id}>
                    <td>{item.metrics}</td>
                    <td>{item.projected}</td>
                    <td>{item.actual}</td>
                    <td>{item.variance}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
      {/* Add Projection Modal  */}
      <CustomModal
        show={showAddProjectionModal}
        close={() => setShowAddProjectionModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">New Projection</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              abc1: '',
              abc2: '',
              abc3: '',
            }}
            // validationSchema={addClassificationSchema}
            onSubmit={() => setShowAddProjectionModal(false)}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-3">
                  <CustomInput
                    label="ABC"
                    name="abc1"
                    required
                    id="abc1"
                    type="text"
                    placeholder="Enter ABC Projection"
                    value={values.abc1}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.abc1 && errors.abc1}
                  />
                </div>
                <div className="mb-3">
                  <CustomInput
                    label="ABC"
                    name="abc2"
                    required
                    id="abc2"
                    type="text"
                    placeholder="Enter ABC Projection"
                    value={values.abc2}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.abc1 && errors.abc1}
                  />
                </div>
                <div className="mb-45">
                  <CustomInput
                    label="ABC"
                    name="abc3"
                    required
                    id="abc3"
                    type="text"
                    placeholder="Enter ABC Projection"
                    value={values.abc3}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.abc1 && errors.abc1}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addClassificationMutation.isPending ? (
                    <> */}
                  <CustomButton type="submit" text={'Save'} />
                  <CustomButton
                    variant={'secondaryButton'}
                    text={'Cancel'}
                    type={'button'}
                    onClick={() => setShowAddProjectionModal(false)}
                  />
                  {/* </>
                  ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </section>
  );
};

export default withFilters(BudgetingForecastingReport);
