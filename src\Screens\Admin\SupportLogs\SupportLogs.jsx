import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiOutlineEye } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { supportLogsData } from '../../../Mocks/MockData';
import { supportLogsHeaders } from '../../../Utils/Constants/TableHeaders';
import { formatDate, serialNum, showErrorToast } from '../../../Utils/Utils';
import { useFetchTableData } from '../../../Hooks/useTable';
import {
  getSupportLogListing,
  getSupportTypes,
} from '../../../Services/Admin/Support';
import { useQuery } from '@tanstack/react-query';

const SupportLogs = ({ filters, setFilters, pagination, updatePagination }) => {
  usePageTitle('Support Logs');
  const navigate = useNavigate();

  //GET SUPPORT LOGS
  const {
    data: fetchSupportLogs, // Renamed to avoid confusion with the derived `userManagement`
    isLoading,
    isError,
    error,
    refetch,
  } = useFetchTableData(
    'getSupportLogListing',
    filters,
    updatePagination,
    getSupportLogListing
  );

  // Provide a default value for `userManagement`
  const supportLogs = fetchSupportLogs?.data ?? [];

  //GET SUPPORT TYPES
  const {
    data: SupportTypes,
    isLoading: isLoadingSupportTypes,
    isError: IsErrorSupportTypes,
    error: ErrorSupportTypes,
  } = useQuery({
    queryKey: ['SupportTypes'],
    queryFn: getSupportTypes,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const getTypesOptions = () => {
    if (!isLoadingSupportTypes && !IsErrorSupportTypes) {
      const allOption = { value: '', label: 'All' };

      return [
        allOption,
        ...SupportTypes?.map((x) => ({
          value: x.id,
          label: x.name,
        })),
      ];
    } else {
      if (IsErrorSupportTypes) {
        console.error('Unable to fetch Types', ErrorSupportTypes);
        return [
          {
            label: 'Unable to fetch Types',
            value: null,
          },
        ];
      } else {
        return [{ label: 'Loading...', value: null, isDisabled: true }];
      }
    }
  };

  if (isError || IsErrorSupportTypes) {
    showErrorToast(error);
  }

  return (
    <>
      <section>
        <div className="d-flex mb-3">
          <h2 className="screen-title mb-0">Support Logs</h2>
        </div>
        <Row>
          <Col xs={12}>
            <CustomTable
              filters={filters}
              setFilters={setFilters}
              headers={supportLogsHeaders}
              pagination={pagination}
              isLoading={isLoading}
              dateFilters={[
                { title: 'Registration Date', from: 'from', to: 'to' },
              ]}
              selectOptions={[
                {
                  title: 'type',
                  options: getTypesOptions(),
                },
              ]}
            >
              {(supportLogs?.length || isError) && (
                <tbody>
                  {isError && (
                    <tr>
                      <td colSpan={supportLogsHeaders.length}>
                        <p className="text-danger mb-0">
                          Unable to fetch data at this time
                        </p>
                      </td>
                    </tr>
                  )}
                  {supportLogs?.map((item, index) => (
                    <tr key={item.id}>
                      <td>
                        {serialNum(
                          (filters?.page - 1) * filters?.per_page + index + 1
                        )}
                      </td>
                      <td>{item?.name}</td>
                      <td>{item?.email}</td>
                      <td>{item?.type?.name}</td>
                      <td>{formatDate(item?.created_at)}</td>
                      <td>
                        <TableActionDropDown
                          actions={[
                            {
                              name: 'View',
                              icon: HiOutlineEye,
                              onClick: () => navigate(`${item.id}`),
                              className: 'view',
                            },
                          ]}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </CustomTable>
          </Col>
        </Row>
      </section>
    </>
  );
};

export default withFilters(SupportLogs);
