import { Form, Formik } from 'formik';
import React, { useCallback, useRef, useState } from 'react';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { inwardPaymentOrderNewHeaders, SUMMARY_TABLE_HEADERS } from '../../../Utils/Constants/TableHeaders';
import { currencyTransferValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import InwardPaymentOrderRow from './InwardPaymentOrderRow';
import CustomInput from '../../../Components/CustomInput';
import CustomButton from '../../../Components/CustomButton';
import { MOCK_SUMMARY_DATA } from '../../../Mocks/MockData';
import { useNavigate } from 'react-router-dom';
const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      refNo: '',
      payType: '',
      beneficiary: '',
      sender: '',
      idNumber: '',
      contactNo: '',
      currency: 'DHS',
      fcAmount: '',
      commission: '',
      payDate: '',
      bankName: '',
      bankAc: '',
      narration: ''
    };
  });
  return rows;
};
const INITIAL_STATE = generateInitialRows(3);

const NewInwardPaymentOrder = ({
  isDisabled = false,
  setIsDisabled,
  setShowAddOfficeLocationModal,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect
}) => {
  const navigate = useNavigate();
  const [rows, setRows] = useState(INITIAL_STATE);
  const [showOutOfScopeModal, setShowOutOfScopeModal] = useState(false);
  const formikRef = useRef();

  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    newRows[id] = {
      id,
      refNo: '',
      payType: '',
      beneficiary: '',
      sender: '',
      idNumber: '',
      contactNo: '',
      currency: 'DHS',
      fcAmount: '',
      commission: '',
      payDate: '',
      bankName: '',
      bankAc: '',
      narration: ''
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    console.log('formValues', formValues);
    console.log('selectedFiles', selectedFiles);
    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([_, v]) => {
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    console.log('submit payload:', payload);
  };

  const handleCancel = () => {
    setRows(generateInitialRows(3));
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            debitLedger: '',
            debitAccount: '',
            office: '',
            vatType: '',
          }}
          validationSchema={currencyTransferValidationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, handleChange, handleBlur, setFieldValue }) => (
            <Form>
              <div className="row mb-4">
                {/* Debit Account Section */}
                <div className="col-12 col-xl-6">
                  <div className="row">
                    <div className="col-12">
                      <label htmlFor="debitLedger">Debit Account</label>
                    </div>
                    <div className="col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitLedger'}
                        options={[
                          { label: 'Party Ledger', value: 'pl' },
                          { label: 'General Ledger', value: 'gl' },
                          { label: 'Walk-in Customer', value: 'wic' },
                        ]}
                        isDisabled={isDisabled}
                        placeholder={'Select Ledger'}
                        value={values.debitLedger}
                        onChange={(selected) => {
                          setFieldValue('debitLedger', selected.value);
                          setFieldValue('debitAccount', ''); // Reset account when ledger changes
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitAccount'}
                        options={[
                          ...(values.debitLedger === 'pl' ? [{ label: 'Add New PL', value: 'add_pl' }] : []),
                          ...(values.debitLedger === 'gl' ? [{ label: 'Add New GL', value: 'add_gl' }] : []),
                          ...(values.debitLedger === 'wic' ? [{ label: 'Add New WIC', value: 'add_wic' }] : []),
                        ]}
                        isDisabled={isDisabled || !values.debitLedger}
                        placeholder={'Select Debit Account'}
                        value={values.debitAccount}
                        onChange={(selected) => {
                          if (selected.value?.startsWith('add_')) {
                            setShowAddLedgerModal(selected.value.replace('add_', 'add new '));
                          } else {
                            setFieldValue('debitAccount', selected.value);
                          }
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                  </div>

                </div>

                {/* Credit Account Section replaced with Office and VAT Type */}
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'office'}
                    label={'Office'}
                    options={[
                      { label: 'Baku - Azerbaijan', value: 'baku_azerbaijan' },
                      { label: 'RAK - UAE', value: 'rak_uae' },
                      { label: 'Sharjah - UAE', value: 'sharjah_uae' },
                      { label: 'Syria', value: 'syria' },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Office'}
                    value={values.office}
                    onChange={(selected) => {
                      setFieldValue('office', selected.value);
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'vatType'}
                    label={'VAT Type'}
                    options={[
                      { label: 'Standard Rate (5.00%)', value: 'standard_rate' },
                      { label: 'Exempted (Nil)', value: 'exempted' },
                      { label: 'Zero Rate (0.00%)', value: 'zero_rate' },
                      { label: 'Out Of Scope', value: 'out_of_scope' },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select VAT Type'}
                    value={values.vatType}
                    onChange={(selected) => {
                      if (selected.value.toLowerCase() === 'out_of_scope') {
                        setShowOutOfScopeModal(true);
                      } else {
                        setFieldValue('vatType', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>

                <div className="col-12">
                  <CustomTable
                    displayCard={false}
                    headers={inwardPaymentOrderNewHeaders}
                    isPaginated={false}
                    className={'inputTable'}
                    hideSearch
                    hideItemsPerPage
                  >
                    <tbody>
                      {Object.values(rows).map((row, index) => (
                        <InwardPaymentOrderRow
                          key={row.id}
                          row={row}
                          index={index}
                          isDisabled={isDisabled}
                          handleDeleteRow={handleDeleteRow}
                          updateField={updateField}
                          setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
                          setCurrencyToSelect={setCurrencyToSelect}
                        />
                      ))}
                    </tbody>
                  </CustomTable>

                  {/* Add the summary table using CustomTable */}
                  <div className="mt-4">
                    <CustomTable
                      displayCard={false}
                      headers={SUMMARY_TABLE_HEADERS}
                      data={!isDisabled ? MOCK_SUMMARY_DATA : []}
                      isPaginated={false}
                      hideSearch
                      hideItemsPerPage
                    >
                      {!isDisabled && (
                        <tbody>
                          {MOCK_SUMMARY_DATA.map((row, index) => (
                            <tr key={index}>
                              <td>{row.currency}</td>
                              <td>{row.total}</td>
                              <td>{row.commission}</td>
                              <td>{row.vatAmount}</td>
                              <td>{row.netTotal}</td>
                            </tr>
                          ))}
                        </tbody>
                      )}
                    </CustomTable>
                  </div>
                  <div className="my-3">
                    <CustomButton
                      text="Add Special Commission"
                      variant="secondary"
                      disabled={isDisabled}
                      type="button"
                      className="w-auto px-5"
                      onClick={() => navigate('/transactions/special-comission')}
                    />
                  </div>
                  <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => { }}
                      />
                      <CustomCheckbox
                        label="Print"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherHeading="Last FSN Number"
        lastVoucherNumber={23}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setSelectedFiles}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
      <CustomModal
        show={showOutOfScopeModal}
        close={() => setShowOutOfScopeModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">Out of Scope Reason</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              reason: '',
            }}
            // validationSchema={outOfScopeSchema}
            // onSubmit={handleOutOfScopeReasonSubmit}
            onSubmit={() => setShowOutOfScopeModal(false)}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    label="Reason"
                    name="reason"
                    required
                    id="reason"
                    type="textarea"
                    rows={1}
                    placeholder="Enter reason"
                    value={values.reason}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.reason && errors.reason}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addClassificationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Save'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowOutOfScopeModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default NewInwardPaymentOrder;
