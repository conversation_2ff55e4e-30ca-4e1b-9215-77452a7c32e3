import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../Components/StatusChip/StatusChip';
import withFilters from '../../../HOC/withFilters ';
import withModal from '../../../HOC/withModal';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getSubscriptionLogListing } from '../../../Services/Admin/Subscription';
import { statusFiltersConfig } from '../../../Utils/Constants/TableFilter';
import { adminSubscriptionLogsHeaders } from '../../../Utils/Constants/TableHeaders';
import { formatDate, serialNum, showErrorToast } from '../../../Utils/Utils';

const AdminSubscriptionLogs = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Subscription Logs');

  //GET SUBSCRIPTIONS LOGS
  const {
    data: fetchSubscriptionLogs, // Renamed to avoid confusion with the derived `userManagement`
    isLoading,
    isError,
    error,
    refetch,
  } = useFetchTableData(
    'getSubscriptionLogListing',
    filters,
    updatePagination,
    getSubscriptionLogListing
  );

  // Provide a default value for `userManagement`
  const subscriptionLogs = fetchSubscriptionLogs?.data ?? [];

  if (isError) {
    showErrorToast(error);
  }

  return (
    <>
      <section>
        <div className="d-flex justify-content-between flex-wrap mb-3">
          <h2 className="screen-title mb-0">Subscription Logs</h2>
        </div>
        <Row>
          <Col xs={12}>
            <CustomTable
              filters={filters}
              setFilters={setFilters}
              headers={adminSubscriptionLogsHeaders}
              pagination={pagination}
              isLoading={isLoading}
              selectOptions={[
                {
                  title: 'status',
                  options: statusFiltersConfig,
                },
              ]}
              dateFilters={[
                { title: 'Registration Date', from: 'from', to: 'to' },
              ]}
            >
              {(subscriptionLogs?.length || isError) && (
                <tbody>
                  {isError && (
                    <tr>
                      <td colSpan={adminSubscriptionLogsHeaders.length}>
                        <p className="text-danger mb-0">
                          Unable to fetch data at this time
                        </p>
                      </td>
                    </tr>
                  )}
                  {subscriptionLogs?.map((item, index) => (
                    <tr key={item.id}>
                      <td>
                        {serialNum(
                          (filters?.page - 1) * filters?.per_page + index + 1
                        )}
                      </td>
                      <td>{item?.owner?.business_name}</td>
                      <td>{item?.package?.title}</td>
                      <td>{item?.subscription_amount}</td>
                      <td>{item?.type}</td>
                      <td>{formatDate(item?.created_at)}</td>
                      <td>
                        <StatusChip status={item.status} />
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </CustomTable>
          </Col>
        </Row>
      </section>
    </>
  );
};

export default withModal(withFilters(AdminSubscriptionLogs));
