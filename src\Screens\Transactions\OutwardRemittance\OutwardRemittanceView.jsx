import React, { useState } from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa6';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { isNullOrEmpty } from '../../../Utils/Utils';

const OutwardRemittanceView = ({ outwardRemittanceData, setPageState }) => {
  const [deleteModal, setDeleteModal] = useState(false);
  return (
    <div className="row">
      <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
        <div className="row mb-4">
          {[
            {
              label: 'Reference Number',
              value: outwardRemittanceData?.reference_number,
            },
            {
              label: 'Ledger',
              value: outwardRemittanceData?.ledger,
            },
            {
              label: 'Account',
              value: outwardRemittanceData?.account,
            },
            {
              label: 'Beneficiary',
              value: outwardRemittanceData?.beneficiary,
            },
            {
              label: 'Address',
              value: outwardRemittanceData?.address,
            },
            {
              label: 'Nationality',
              value: outwardRemittanceData?.nationality,
            },
            {
              label: 'Bank Name',
              value: outwardRemittanceData?.bank_details?.bank_name,
            },
            {
              label: 'Bank A/C',
              value: outwardRemittanceData?.bank_details?.bank_ac,
            },
            {
              label: 'SWIFT Code',
              value: outwardRemittanceData?.bank_details?.swift_code,
            },
            {
              label: 'Routing Number',
              value: outwardRemittanceData?.bank_details?.routing_number,
            },
            {
              label: 'City',
              value: outwardRemittanceData?.bank_details?.city,
            },
            {
              label: 'Country',
              value: outwardRemittanceData?.bank_details?.country,
            },
            {
              label: 'Corresponding Bank',
              value: outwardRemittanceData?.bank_details?.corresponding_bank,
            },
            {
              label: 'Bank Account Number',
              value: outwardRemittanceData?.bank_details?.bank_account_number,
            },
            {
              label: 'Purpose',
              value: outwardRemittanceData?.purpose,
            },
            {
              label: 'By Order',
              value: outwardRemittanceData?.by_order,
            },
            {
              label: 'Send FC',
              value: outwardRemittanceData?.send_fc,
            },
            {
              label: 'Send Amount',
              value: outwardRemittanceData?.send_amount?.amount,
            },
            {
              label: 'Rate',
              value: outwardRemittanceData?.rate,
            },
            {
              label: 'Currency Charges',
              value: outwardRemittanceData?.charges?.amount,
            },

            {
              label: 'Against',
              value: outwardRemittanceData?.against,
            },
            {
              label: 'VAT Term',
              value: outwardRemittanceData?.vat?.terms,
            },
            {
              label: 'VAT Amount',
              value: outwardRemittanceData?.vat?.amount?.value,
            },
            {
              label: 'Net Total',
              value: outwardRemittanceData?.net_total?.amount,
            },
            {
              label: 'Base Rate',
              value: outwardRemittanceData?.base_rate,
            },
            {
              label: 'LCy Amount',
              value: outwardRemittanceData?.lcy_amount?.amount,
            },
            {
              label: 'Settle Thru',
              value: outwardRemittanceData?.settle_thru,
            },
          ].map((x, i) => {
            if (isNullOrEmpty(x.value)) return null;
            return (
              <div key={i} className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">
                  {x.label}
                </p>
                <p className="detail-text wrapText mb-0">{x.value}</p>
              </div>
            );
          })}
        </div>
      </div>
      <div className="col-0  col-xxl-2" />
      <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
        <div className="row">
          {/* Right side cards */}
          <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
            {/* Account Balance Cards */}
            <AccountBalanceCard numberOfCards={1} />
          </div>
          <div className="col-12 col-sm-6 col-xxl-12 mb-3">
            <CustomInput
              name={'remitter'}
              type={'text'}
              disabled={true}
              label={'Remitter'}
              value={outwardRemittanceData?.remitter}
            />
          </div>
          <div className="col-12 col-sm-6 col-xxl-12 mb-3">
            <CustomInput
              name={'remitter_telephone_number'}
              type={'text'}
              disabled={true}
              label={'Remitter Telephone Number'}
              value={outwardRemittanceData?.remitter_telephone_number}
            />
          </div>
          <div className="col-12 col-sm-6 col-xxl-12 mb-3">
            <SearchableSelect
              label={'Nationality'}
              name={'remitter_nationality'}
              isDisabled={true}
              value={outwardRemittanceData?.remitter_nationality}
            />
          </div>
          <div className="col-12 col-sm-6 col-xxl-12 mb-3">
            <CustomInput
              name={'id_no'}
              type={'text'}
              disabled={true}
              label={'ID No'}
              value={outwardRemittanceData?.id_no}
            />
          </div>
          <div className="col-12 col-sm-6 col-xxl-12 mb-3">
            <CustomInput
              name={'valid_upto'}
              type={'date'}
              disabled={true}
              label={'Valid Upto'}
              value={outwardRemittanceData?.valid_upto}
            />
          </div>
          <div className="col-12 col-sm-6 col-xxl-12 mb-3">
            <CustomInput
              name={'company'}
              type={'text'}
              disabled={true}
              label={'Company'}
              value={outwardRemittanceData?.company}
            />
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-12 col-lg-6 d-flex justify-content-between mt-3 mb-5">
          <div>
            <div>Last FSN Number: 05</div>
          </div>
          <div className="d-flex gap-2 voucher-navigation-wrapper">
            <FaChevronLeft size={24} />
            <FaChevronRight size={24} />
          </div>
        </div>
      </div>
      <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
        <CustomButton
          type={'button'}
          text={'Edit'}
          onClick={() => {
            setPageState('edit');
          }}
        />
        <CustomButton
          type={'button'}
          text={'Delete'}
          //   variant={'danger'}
          variant={'secondaryButton'}
          onClick={() => {
            setDeleteModal(true);
            // setPageState('new'); //setPageState to new after deleting
            console.log('delete');
          }}
        />
        <CustomButton
          variant={'secondaryButton'}
          text={'Print'}
          type={'button'}
          onClick={() => {
            console.log('print');
          }}
        />
      </div>
      <CustomModal
        show={deleteModal}
        close={() => {
          setDeleteModal(false);
        }}
        // disableClick={deleteMutation.isPending}
        // action={deleteAdmin}
        title="Delete?"
        description="Are you sure you want to delete Outward Remittance?"
      />
    </div>
  );
};

export default OutwardRemittanceView;
