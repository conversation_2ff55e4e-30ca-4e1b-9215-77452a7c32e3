import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import HorizontalTabs from '../../../Components/HorizontalTabs/HorizontalTabs';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import TTRRegisterBankDetails from './TTRRegisterBankDetails';
import TTRRegisterAllocation from './TTRRegisterAllocation';
import TTRRegisterConfirmation from './TTRRegisterConfirmation';

const TTRRegister = () => {
  usePageTitle('TTR Register');
  const navigate = useNavigate();

  const [selectedTab, setSelectedTab] = useState('bank_details');
  const [tabsToShow, setTabsToShow] = useState([
    {
      label: 'Bank Details',
      value: 'bank_details',
    },
    {
      label: 'Allocation',
      value: 'allocation',
    },
    {
      label: 'Confirmation',
      value: 'confirmation',
    },
  ]);

  const renderTab = (tab) => {
    switch (tab) {
      case 'bank_details':
        return <TTRRegisterBankDetails />;
      case 'allocation':
        return <TTRRegisterAllocation />;
      case 'confirmation':
        return <TTRRegisterConfirmation />;
      default:
        return null;
    }
  };
  return (
    <>
      <section>
        <div className="d-flex gap-3 justify-content-between flex-wrap mb-5">
          <h2 className="screen-title mb-0">TTR Register</h2>
          <div className="d-flex gap-2">
            <CustomButton
              text={'New'}
              onClick={() => {
                if (selectedTab === 'confirmation') {
                  navigate('/transactions/ttr-register/confirmation/new');
                } else {
                  navigate('/transactions/ttr-register/bank-details/new');
                }
              }}
            />
          </div>
        </div>
        <Row>
          <Col xs={12}>
            <div className="beechMein">
              <HorizontalTabs
                tabs={tabsToShow}
                activeTab={selectedTab}
                style={{ width: 270 }}
                onTabChange={setSelectedTab}
              />
            </div>
            {renderTab(selectedTab)}
          </Col>
        </Row>
      </section>
    </>
  );
};

export default TTRRegister;
