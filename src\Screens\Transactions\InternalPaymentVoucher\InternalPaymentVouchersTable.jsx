import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import {
  MOCK_INTERNAL_PAYMENT_VOUCHER_TABLE_DATA
} from '../../../Mocks/MockData';
import {
  internalPaymentVoucherTableHeaders, journalVoucherListHeaders,
} from '../../../Utils/Constants/TableHeaders';
import { formatDate } from '../../../Utils/Utils';
import { useFetchTableData } from '../../../Hooks/useTable.js';
import { getInternalPaymentVoucherListing } from '../../../Services/Transaction/InternalPaymentVoucher.js';

const InternalPaymentVouchersTable = ({
  date,
  filters,
  pagination,
  updatePagination,
  setPageState,
  setSearchTerm,
}) => {
    const {
      data: { data: internalPaymentVoucherData = [] } = {},
      isLoading,
      isError,
      error,
    } = useFetchTableData(
      'internalPaymentVoucherListing',
      {
        ...filters,
        // Uncomment this line when development is done
         date: date
      },
      updatePagination,
      getInternalPaymentVoucherListing
    );

  // const tableData = MOCK_INTERNAL_PAYMENT_VOUCHER_TABLE_DATA;
  // const isLoading = false;
  // const isError = false;
  // const error = null;

  if (isError) {
    console.error(error);
  }
  if(!isLoading){
      console.log('data',internalPaymentVoucherData)
  }

  return (
    <Row>
      <Col xs={12}>
        <CustomTable
          headers={internalPaymentVoucherTableHeaders}
          pagination={pagination}
          updatePagination={updatePagination}
          isLoading={isLoading}
          hideItemsPerPage
          hideSearch
        >

          {(internalPaymentVoucherData?.length || isError) && (
            <tbody>
            {isError && (
              <tr>
                <td colSpan={internalPaymentVoucherTableHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {internalPaymentVoucherData?.map((item) => (
              <tr key={item.id}>
                <td>{formatDate(item?.voucher_date)}</td>
                <td
                  onClick={() => {
                    setSearchTerm(item.voucher_no);
                    setPageState('view');
                  }}
                >
                  <p className="hyper-link text-decoration-underline cp mb-0">
                    {item.voucher_no}
                  </p>
                </td>
                <td>{item.credit_ledger
                  ?.split(' ')
                  .map(word => word.charAt(0))
                  .join('')
                }</td>
                <td>{item.credit_account}</td>
                <td>{item.debit_ledger
                  ?.split(' ')
                  .map(word => word.charAt(0))
                  .join('')
                }</td>
                <td>{item.debit_account}</td>
                <td>{item.currency}</td>
                <td>{item.amount}</td>
                <td>{item.vat_detail?.vat_terms}</td>
                <td>{item.fc_net_total}</td>
                <td>{item.lc_net_total}</td>
                <td>{item.creator}</td>
                <td>
                  {formatDate(item.voucher_date, 'DD/MM/YYYY - HH:MM:SS')}</td>
              </tr>
            ))}
            </tbody>
          )}

          {/*{(tableData?.length || isError) && (*/}
          {/*  <tbody>*/}
          {/*    {isError && (*/}
          {/*      <tr>*/}
          {/*        <td colSpan={internalPaymentVoucherTableHeaders.length}>*/}
          {/*          <p className="text-danger mb-0">*/}
          {/*            Unable to fetch data at this time*/}
          {/*          </p>*/}
          {/*        </td>*/}
          {/*      </tr>*/}
          {/*    )}*/}
          {/*    {tableData?.map((item) => (*/}
          {/*      <tr key={item.id}>*/}
          {/*        <td>{formatDate(item.created_at, 'DD/MM/YYYY')}</td>*/}
          {/*        <td*/}
          {/*          onClick={() => {*/}
          {/*            setSearchTerm(item.ipv_no);*/}
          {/*            setPageState('view');*/}
          {/*          }}*/}
          {/*        >*/}
          {/*          <p className="text-link text-decoration-underline cp mb-0">*/}
          {/*            {item.ipv_no}*/}
          {/*          </p>*/}
          {/*        </td>*/}
          {/*        <td>{item.cr_ledger}</td>*/}
          {/*        <td>{item.credit_account}</td>*/}
          {/*        <td>{item.dt_ledger}</td>*/}
          {/*        <td>{item.debit_account}</td>*/}
          {/*        <td>{item.fcy}</td>*/}
          {/*        <td>{item.amount}</td>*/}
          {/*        <td>{item.vat}</td>*/}
          {/*        <td>{item.fcy_net_total}</td>*/}
          {/*        <td>{item.lc_net_total}</td>*/}
          {/*        <td>{item.user_id}</td>*/}
          {/*        <td>{formatDate(item.created_at, 'HH:MM')}</td>*/}
          {/*        <td>*/}
          {/*          {item.attachment?.charAt(0).toUpperCase() +*/}
          {/*            item?.attachment?.slice(1)}*/}
          {/*        </td>*/}
          {/*      </tr>*/}
          {/*    ))}*/}
          {/*  </tbody>*/}
          {/*)}*/}
        </CustomTable>
      </Col>
    </Row>
  );
};

export default withFilters(InternalPaymentVouchersTable);
