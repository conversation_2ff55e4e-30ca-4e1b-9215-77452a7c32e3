import React from 'react';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { formatDate } from '../../../Utils/Utils';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getTMNCurrencyDealListing } from '../../../Services/Transaction/TMNCurrencyDeal';
import withFilters from '../../../HOC/withFilters ';

const TmnCurrencyDealSearchTable = ({
  date,
  searchType,
  filters,
  pagination,
  updatePagination,
  setPageState,
  setSearchTerm,
  setWriteTerm,
}) => {
  const tMNCurrencyDealTableHeaders = [
    'Date',
    `${searchType === 'buy' ? 'TBN' : 'TSN'} No.`,
    'Ledger Name',
    'Account Name',
    'Beneficiary',
    'Bank',
    `${searchType === 'buy' ? 'Buy' : 'Sell'} FCy`,
    `${searchType === 'buy' ? 'Buy' : 'Sell'} FC Amount`,
    'Rate',
    'Against FCy',
    'Against FC Amount',
    ...(searchType === 'buy' ? ['Commission'] : []),
    ...(searchType === 'buy' ? ['VAT'] : []),
    'FC Net Total',
    'User ID',
    'Time',
    'Attachments',
  ];

  const {
    data: { data: tMNCurrencyDealData = [] } = {},
    isLoading,
    isError,
    error,
  } = useFetchTableData(
    'tMNCurrencyDealListing',
    {
      ...filters,
      date: date,
      type: searchType,
    },
    updatePagination,
    getTMNCurrencyDealListing
  );
  if (isError) {
    console.error(error);
  }

  return (
    <CustomTable
      hasFilters={false}
      headers={tMNCurrencyDealTableHeaders}
      isLoading={isLoading}
      pagination={pagination}
      hideItemsPerPage
      hideSearch
    >
      <tbody>
        {tMNCurrencyDealData?.map((row) => (
          <tr key={row.id}>
            <td>{formatDate(row.voucher?.date, 'DD/MM/YYYY')}</td>
            <td
              onClick={() => {
                setWriteTerm(row.voucher.voucher_no);
                setSearchTerm(row.voucher.voucher_no);
                setPageState('view');
              }}
            >
              <p className="text-link text-decoration-underline cp mb-0">
                {row.voucher.voucher_no}
              </p>
            </td>
            <td>{row.new_ledger}</td>
            <td>{row.account_details?.title}</td>
            <td>{row.beneficiary?.name}</td>
            <td>{row.bank_name}</td>
            <td>{row.fcy?.currency_code}</td>
            <td>{row.fc_amount}</td>
            <td>{row.rate}</td>
            <td>{row.against_f_cy?.currency_code}</td>
            <td>{row.ag_amount}</td>
            {searchType === 'buy' && (
              <>
                <td>{row.commission_amount}</td>
                <td>{row.vat_amount}</td>
              </>
            )}
            <td>{row.total_amount}</td>
            <td>{row.creator?.user_id}</td>
            <td>{formatDate(row.created_at, 'HH:MM')}</td>
            <td>
              {row.attachments?.charAt(0).toUpperCase() +
                row?.attachments?.slice(1)}
            </td>
          </tr>
        ))}
      </tbody>
    </CustomTable>
  );
};

export default withFilters(TmnCurrencyDealSearchTable);
