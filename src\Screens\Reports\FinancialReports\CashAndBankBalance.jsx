import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { cashAndBankBalanceHeaders } from '../../../Utils/Constants/TableHeaders';
import {
  bankBalanceReportData,
  cashBalanceReportData,
} from '../../../Mocks/MockData';

const CashAndBankBalance = () => {
  const isLoading = false;
  const isError = false;
  return (
    <>
      <div className="d-flex justify-content-between flex-wrap mb-45">
        <h2 className="screen-title m-0 d-inline">Cash and Bank Balance</h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row className="g-1">
        <Col xs={12} lg={6}>
          <div
            style={{ backgroundColor: '#1F4047' }}
            className="py-2 d-flex justify-content-center align-items-center m-0 rounded-3"
          >
            <h3 className="m-0 fw-normal text-white fs-5">Cash Balance</h3>
          </div>
          <CustomTable
            headers={cashAndBankBalanceHeaders}
            hasFilters={false}
            isLoading={isLoading}
            hideSearch
            isPaginated={false}
          >
            {(cashBalanceReportData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={cashAndBankBalanceHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {cashBalanceReportData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.account}</td>
                    <td>{item.fcy}</td>
                    <td>{item.balance}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
        <Col xs={12} lg={6}>
        <div
            style={{ backgroundColor: '#000058' }}
            className="py-2 d-flex justify-content-center align-items-center m-0 rounded-3"
          >
            <h3 className="m-0 fw-normal text-white fs-5">Bank Balance</h3>
          </div>
          <CustomTable
            headers={cashAndBankBalanceHeaders}
            hasFilters={false}
            isLoading={isLoading}
            hideSearch
            isPaginated={false}
          >
            {(bankBalanceReportData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={cashAndBankBalanceHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {bankBalanceReportData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.account}</td>
                    <td>{item.fcy}</td>
                    <td>{item.balance}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </>
  );
};

export default CashAndBankBalance;
