import React, { useEffect, useRef, useState } from 'react';
import {
  FaChevronLeft,
  FaChevronRight,
  FaMagnifyingGlass,
  FaPaperclip,
} from 'react-icons/fa6';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import withModal from '../../../HOC/withModal';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import {
  MOCK_CURRENT_ACCOUNT,
  MOCK_PDCR_DATA,
  MOCK_SEARCH_RESULT,
  MOCK_SEARCH_TABLE_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { useNavigate } from 'react-router-dom';
import {
  pdcrTableHeaders,
  searchTableHeaders, searchTableHeadersForPdcr,
} from '../../../Utils/Constants/TableHeaders';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import BackButton from '../../../Components/BackButton';
import withFilters from '../../../HOC/withFilters ';
import { useMutation, useQuery } from '@tanstack/react-query';
import { getAccountsbyType } from '../../../Services/Transaction/InternalPaymentVoucher.js';
import { Form, Formik } from 'formik';
import { getCurrencyOptions } from '../../../Utils/Utils.jsx';
import { useFetchTableData } from '../../../Hooks/useTable.js';
import {
  getReceiptVoucherDataForPdcr,
  getReceiptVoucherListing,
} from '../../../Services/Transaction/ReceiptVoucher.js';
import { createPaymentVoucher } from '../../../Services/Transaction/PaymentVoucher.js';
import { showToast } from '../../../Components/Toast/Toast.jsx';
import { createPdcrVoucher, getPdcrListing } from '../../../Services/Transaction/PdcrVoucher.js';
import { PulseLoader } from 'react-spinners';
import { getBankTransactionListing } from '../../../Services/Transaction/BankTransactions.js';
// Add this mock data at the top of the file, after imports

// Add this new constant for inward TT table headers

// Add these constants after the imports

const PdcrIssueAsPdcp = ({
                           showModal,
                           closeModal,
                           filters,
                           pagination,
                           updatePagination,
                         }) => {
  const navigate = useNavigate();
  usePageTitle('PDCR Issue as PDCP');
  const formikRef = useRef();
  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [date, setDate] = useState('');
  const [ledger, setLedger] = useState('');
  const [selectedAccount, setSelectedAccount] = useState('');
  const [narration, setNarration] = useState('');
  const [chequeNumber, setChequeNumber] = useState('');
  const [filterDate, setFilterDate] = useState('');
  const [filterLedger, setFilterLedger] = useState('');
  const [filterAccount, setFilterAccount] = useState('');
  const [currency, setCurrency] = useState('');
  const [fcAmount, setFcAmount] = useState('');
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);

  const [transactionDetail, setTransactionDetail] = useState(null);
  // Add state for table data if needed
  const [tableData, setTableData] = useState([]);

  // Add state for selected PDCR
  const [selectedPdcr, setSelectedPdcr] = useState(null);

  // Add validation state
  const [errors, setErrors] = useState({});

  // Add this check to determine if we should show the search result
  const isSearchResult = searchTerm === '05';

  // Add this constant after the existing mock data
  const [showSearchTable, setShowSearchTable] = useState(false);

  const currencyOptions = getCurrencyOptions();

  // Update validation function
  const validateForm = () => {
    const newErrors = {};

    if (!ledger) newErrors.ledger = 'Ledger is required';
    if (!selectedAccount) newErrors.account = 'Account is required';
    if (!narration.trim()) newErrors.narration = 'Narration is required';
    if (!selectedPdcr) newErrors.pdcr = 'Please select a PDCR';


    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Add handleSave function
  const handleSave = () => {
    if (validateForm()) {

    //  console.log('selectedPdcr',selectedPdcr.id)
      setLoading(true);

      let payload = {
        'date' : date,
        'ledger' : ledger,
        'account_id' : selectedAccount,
        'narration' : narration,
        'selected_voucher_ids' : [selectedPdcr.id]
      };

      console.log('values', payload);

       createPdcrMutation.mutate(payload);

      // Proceed with save logic
      console.log('Form is valid, saving...');
   }
  };

  const createPdcrMutation = useMutation({
    mutationFn: createPdcrVoucher,
    onSuccess: (data) => {
      showToast('Pdcr Voucher Created!', 'success');
      setLoading(false);
      handleCancel();

    },
    onError: (error) => {
      console.error('Error creating Pdcr Voucher', error);
      setLoading(false);
    },
  });


  // Add handleCancel function
  const handleCancel = () => {
    setIsDisabled(true);
    // Reset form fields
    setDate('');
    setLedger('');
    setSelectedAccount('');
    setNarration('');
    setChequeNumber('');
    setFilterDate('');
    setFilterLedger('');
    setFilterAccount('');
    setCurrency('');
    setFcAmount('');
    setSelectedPdcr(null);
    setErrors({});
  };

  const handleNewClick = () => {
    setIsDisabled(false);
    setShowSearchTable(false); // Reset search table view
    // Reset all form fields
    setDate('');
    setLedger('');
    setSelectedAccount('');
    setNarration('');
    setChequeNumber('');
    setFilterDate('');
    setFilterLedger('');
    setFilterAccount('');
    setCurrency('');
    setFcAmount('');
    setSelectedPdcr(null);
    setErrors({});
    setSearchTerm(''); // Clear search term
  };

  const handleLedgerChange = (selected) => {
    setLedger(selected.value);
  };

  const handleAccountChange = (selected) => {
    if (selected.value === 'add_new_gl') {
      setShowAddLedgerModal('add new gl');
    } else if (selected.value === 'add_new_pl') {
      setShowAddLedgerModal('add new pl');
    } else if (selected.value === 'add_new_wic') {
      setShowAddLedgerModal('add new wic');
    } else {
      setSelectedAccount(selected.value);
    }
  };

  const handleDelete = (item) => {
    showModal(
      'Delete',
      `Are you sure you want to delete PPV Number: 05?`,
      () => {
        console.log(item);
        // Close the first modal before showing the second one
        closeModal();
        // Show success modal after a small delay to ensure smooth transition
        setTimeout(() => {
          showModal(
            'Deleted',
            `PPV Number: 05 deleted successfully`,
            false,
            'success',
            () => {
              setIsDisabled(true);
              setShowSearchTable(false);
              setSearchTerm('');
            }
          );
        }, 100);
      }
    );
  };

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newAccount) => {
              setSelectedAccount(newAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newAccount) => {
              setSelectedAccount(newAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newAccount) => {
              setSelectedAccount(newAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        return null;
    }
  };

  const getAccountOptions = () => {
    switch (ledger) {
      case 'pl':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add New PL', value: 'add_new_pl' },
        ];
      case 'gl':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add New GL', value: 'add_new_gl' },
        ];
      case 'wic':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add New WIC', value: 'add_new_wic' },
        ];
      default:
        return [];
    }
  };



  const getAccountsByTypeOptions = (accountType) => {

    console.log(accountType);

    if (!accountType) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }



    const { data, loading, error, errorMessage } =
    accountData[accountType] || {};

    console.log(accountData[accountType]);


    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Accounts', errorMessage);
      return [{ label: 'Unable to fetch Accounts', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];
    switch (accountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: 'add new pl',
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: 'add new gl',
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: 'add new wic',
        });
        break;
      default:
        break;
    }
    return options;
  };


  // Get account options //
  const {
    data: partyAccounts,
    isLoading: isLoadingParty,
    isError: isErrorParty,
    error: errorParty,
  } = useQuery({
    queryKey: ['accounts', 'party'],
    queryFn: () => getAccountsbyType('party'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: generalAccounts,
    isLoading: isLoadingGeneral,
    isError: isErrorGeneral,
    error: errorGeneral,
  } = useQuery({
    queryKey: ['accounts', 'general'],
    queryFn: () => getAccountsbyType('general'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: walkinAccounts,
    isLoading: isLoadingWalkin,
    isError: isErrorWalkin,
    error: errorWalkin,
  } = useQuery({
    queryKey: ['accounts', 'walkin'],
    queryFn: () => getAccountsbyType('walkin'),
    staleTime: 1000 * 60 * 5,
  });

  const accountData = {
    party: {
      data: partyAccounts,
      loading: isLoadingParty,
      error: isErrorParty,
      errorMessage: errorParty,
    },
    general: {
      data: generalAccounts,
      loading: isLoadingGeneral,
      error: isErrorGeneral,
      errorMessage: errorGeneral,
    },
    walkin: {
      data: walkinAccounts,
      loading: isLoadingWalkin,
      error: isErrorWalkin,
      errorMessage: errorWalkin,
    },
  };


  const [receiptVoucherData, setReceiptVoucherData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);


  const fetchReceiptVoucherData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getReceiptVoucherDataForPdcr({
        date : date,
        search: chequeNumber,
        currency,
        fc_amount: fcAmount,
        ledger: filterLedger,
        account: filterAccount,
      });

      setReceiptVoucherData(response);
      console.log('ReceiptVoucherData',receiptVoucherData)
    } catch (err) {
      console.error(err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    fetchReceiptVoucherData();
  },[]);

  const handleSearchReceiptVouchers = () => {
    fetchReceiptVoucherData();
  };


  const {
    data: pdcrData,
    isLoading,
    isFetching,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['searchTerm', searchTerm],
    queryFn: () =>
      getPdcrListing({
        search: searchTerm,
        date: date
      }),
    enabled: false,
    staleTime: 1000 * 60 * 5,
  });


  console.log('pdcrData', pdcrData);

  const handleSearchButtonClick = () => {

    if (!searchTerm) {
      refetch({
        queryKey: ['searchTerm',searchTerm, date]
      });

      setShowSearchTable(true);
    }


  };




  // Add this function to handle edit button click
  const handleEdit = () => {
    setIsDisabled(false);
    // Pre-fill form with MOCK_SEARCH_RESULT data
    setLedger(MOCK_SEARCH_RESULT.issuedTo.type.toLowerCase());
    setSelectedAccount(MOCK_SEARCH_RESULT.issuedTo.account);
    setNarration(MOCK_SEARCH_RESULT.narration);

    // Pre-fill PDCR details
    const pdcrDetails = MOCK_SEARCH_RESULT.pdcrDetails;
    setChequeNumber(pdcrDetails.cheque_number);
    setCurrency(pdcrDetails.currency);
    setFcAmount(pdcrDetails.fc_amount);
    setSelectedPdcr(pdcrDetails);
  };

  return (
    <>
      <section className="position-relative">
        <div className="d-flex gap-3 justify-content-between flex-wrap">
          <div>
            {(!isDisabled || isSearchResult || showSearchTable) && (
              <BackButton
                handleBack={() => {
                  setIsDisabled(true);
                  setShowSearchTable(false);
                  setSearchTerm('');
                }}
              />
            )}
            <h2 className="screen-title mb-0">PDCR Issue as PDCP</h2>
          </div>
          {isDisabled && !showSearchTable && !isSearchResult && (
            <CustomButton text="New" onClick={handleNewClick} />
          )}
        </div>

        <div className="d-flex justify-content-between align-items-end mt-3 gap-3">
          <CustomInput
            type="text"
            placeholder="Search PPV Number"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            rightIcon={FaMagnifyingGlass}
            onButtonClick={handleSearchButtonClick}
            // onButtonClick={() => {
            //   if (!searchTerm) {
            //     setShowSearchTable(true);
            //   }
            // }}
          />
          {(!isSearchResult || !isDisabled) && !showSearchTable && (
            <CustomInput
              type="date"
              label="Date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              disabled={isDisabled}
              error={errors.date}
            />
          )}
        </div>

        {showSearchTable ? (
          <div className="mt-3">
            <CustomTable
              hasFilters={false}
              headers={searchTableHeaders}
              pagination={pagination}
              updatePagination={updatePagination}
              isLoading={false}
              sortKey={false}
              sortOrder={false}
              handleSort={false}
            >
              <tbody>

              {pdcrData && pdcrData?.data.map((item, index) => (
                <tr key={index}>
                  <td>{item.due_date}</td>
                  <td
                    onClick={() => {
                      setSearchTerm(item.voucher_no);
                      setShowSearchTable(false);

                      setTransactionDetail(item);

                      console.log('item',item)


                    }}
                  >
                    <p className="text-link text-decoration-underline cp mb-0">
                      {item.voucher_no}
                    </p>
                  </td>
                  <td>{item.ledger}</td>
                  <td>{item.pdcr_party}</td>
                  <td>{item.party_bank}</td>
                  <td>{item.cheque_number}</td>
                  <td>{item.due_date}</td>
                  <td>{item.currency}</td>
                  <td>{item.fc_net_total}</td>
                  <td>{item.lc_net_total}</td>
                  <td>{item.user_id}</td>
                  <td>{item.time}</td>
                  <td>{item.has_attachment && <FaPaperclip />}</td>
                </tr>
              ))}

              {/*{MOCK_SEARCH_TABLE_DATA.map((item, index) => (*/}
              {/*  <tr key={index}>*/}
              {/*    <td>{item.date}</td>*/}
              {/*    <td*/}
              {/*      onClick={() => {*/}
              {/*        setSearchTerm('05');*/}
              {/*        setShowSearchTable(false);*/}
              {/*      }}*/}
              {/*    >*/}
              {/*      <p className="text-link text-decoration-underline cp mb-0">*/}
              {/*        {item.ppv_no}*/}
              {/*      </p>*/}
              {/*    </td>*/}
              {/*    <td>{item.ledger}</td>*/}
              {/*    <td>{item.pdcr_party}</td>*/}
              {/*    <td>{item.pdcr_bank}</td>*/}
              {/*    <td>{item.cheque_number}</td>*/}
              {/*    <td>{item.due_date}</td>*/}
              {/*    <td>{item.fcy}</td>*/}
              {/*    <td>{item.fc_amount}</td>*/}
              {/*    <td>{item.lc_amount}</td>*/}
              {/*    <td>{item.user_id}</td>*/}
              {/*    <td>{item.time}</td>*/}
              {/*    <td>{item.has_attachment && <FaPaperclip />}</td>*/}
              {/*  </tr>*/}
              {/*))}*/}


              </tbody>
            </CustomTable>
          </div>
        ) : (
          <div className="d-card">
            {isSearchResult && isDisabled ? (
              // Search Result View
              <div className="d-flex flex-column gap-4">
                <div className="d-flex gap-3 justify-content-between">
                  <div style={{ maxWidth: '500px' }}>
                    <div>

                      <label>Issued To</label>
                      <div className="d-flex gap-2">
                        <span>{MOCK_SEARCH_RESULT.issuedTo.type}</span>
                        <span>{MOCK_SEARCH_RESULT.issuedTo.account}</span>
                      </div>
                    </div>
                    <div className="mt-4">
                      <label>Narration</label>
                      <p>{MOCK_SEARCH_RESULT.narration}</p>
                    </div>
                  </div>

                  <div style={{ width: '350px' }}>
                    <AccountBalanceCard />
                  </div>
                </div>

                <div className="d-flex justify-content-between">
                  <div className="flex-grow-1">
                    <table className="w-100">
                      <thead>
                      <tr>
                        <th>PDCR Party</th>
                        <th>Cheque Number</th>
                        <th>Due Date</th>
                        <th>Currency</th>
                        <th>FC Amount</th>
                        <th>PDCR Bank</th>
                      </tr>
                      </thead>
                      <tbody>


                      <tr>
                        <td>{MOCK_SEARCH_RESULT.pdcrDetails.pdcr_party}</td>
                        <td>
                          {MOCK_SEARCH_RESULT.pdcrDetails.cheque_number}
                        </td>
                        <td>{MOCK_SEARCH_RESULT.pdcrDetails.due_date}</td>
                        <td>{MOCK_SEARCH_RESULT.pdcrDetails.currency}</td>
                        <td>{MOCK_SEARCH_RESULT.pdcrDetails.fc_amount}</td>
                        <td>{MOCK_SEARCH_RESULT.pdcrDetails.pdcr_bank}</td>
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="d-flex justify-content-between align-items-center mt-4">
                  <div className="d-flex gap-3">
                    <CustomButton text="Edit" onClick={handleEdit} />
                    <CustomButton
                      text="Delete"
                      onClick={handleDelete}
                      className="secondaryButton"
                    />
                    <CustomButton text="Print" className="secondaryButton" />
                  </div>
                  <div className="d-flex gap-2">
                    <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
                    <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
                    <FaPaperclip
                      size={20}
                      style={{ cursor: 'pointer' }}
                      onClick={() => setUploadAttachmentsModal(true)}
                    />
                  </div>
                </div>

                <div>Last PPV Number: 05</div>
              </div>
            ) : (

              <Formik
                innerRef={formikRef}
                onSubmit={handleSave}
                initialValues={{
                  ledger: transactionDetail?.ledger,
                  account_id:  transactionDetail?.account_id  || '',
                  narration: transactionDetail?.narration || '',
                }}
                validate={(values) => {
                  const errors = {};

                  // Required fields for special commission
                  if (!values.ledger) errors.ledger = 'Ledger is required';
                  if (!values.account_id) errors.account_id = 'Account is required';

                  return errors;
                }}

              >
                {({ values, setFieldValue,
                    handleChange,
                    errors,
                    touched }) => (
                  <Form>

                    <div className="d-flex flex-column gap-4">
                      {/* Search and Date */}

                      {/* Issued To */}
                      <div className="d-flex gap-3 flex-wrap justify-content-between">
                        <div className="flex-grow-1" style={{ maxWidth: '523px' }}>
                          <div className="combined-select-container">
                            <label>Issued To</label>
                            <div
                              className={`combined-select-input ${
                                isDisabled ? 'disabled-combined-select' : ''
                              }`}
                            >
                              <div className="combined-select-left">
                                <SearchableSelect
                                  name={'ledger'}
                                  options={[
                                    { label: 'PL', value: 'party' },
                                    { label: 'GL', value: 'general' },
                                    { label: 'WIC', value: 'walkin' },
                                  ]}
                                  value={values.ledger}
                                  // onChange={handleLedgerChange}
                                  onChange={(selected) => {
                                    setLedger(selected.value)
                                    setFieldValue('ledger', selected.value);
                                  }}
                                  placeholder="Ledger"
                                  className={'ledger-select__control'}
                                  isDisabled={isDisabled}
                                  error = {errors.ledger}

                                />
                              </div>
                              <div className="separator-between-selects">|</div>
                              <div className="combined-select-right">
                                {/*<SearchableSelect*/}
                                {/*  options={getAccountOptions()}*/}
                                {/*  className={'ledger-select__control'}*/}
                                {/*  value={selectedAccount}*/}
                                {/*  onChange={handleAccountChange}*/}
                                {/*  placeholder="Select Account"*/}
                                {/*  isDisabled={isDisabled || !ledger}*/}
                                {/*/>*/}
                                <SearchableSelect
                                  name="account_id"
                                  className="ledger-select__control"
                                  //  options={getFromAccountOptions(values.ledger)}
                                  options={getAccountsByTypeOptions(values.ledger)}
                                  value={values.account_id}
                                  onChange={(selected) => {

                                    if (typeof selected?.value === 'string' && selected.value.includes('add new')) {
                                      setShowAddLedgerModal(selected.value);
                                    } else {
                                      setFieldValue('account_id', selected.value);
                                      setSelectedAccount(selected.value);
                                    }


                                    // setFieldValue('fromAccount', selected.value);
                                    // setFromAccount(selected.value);
                                    // setSelectedBank(selected.value)
                                  }}

                                  placeholder="Select From Accounts"
                                  isDisabled={isDisabled || !values.ledger}
                                  error={touched.account && errors.account}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="mt-3">
                            <label>Narration</label>
                            <CustomInput
                              type="textarea"
                              placeholder="Enter Narration"
                              value={narration}
                              rows={5}
                              onChange={(e) => setNarration(e.target.value)}
                              disabled={isDisabled}
                              style={{ height: '100px' }}
                              error = {errors.narration}
                            />
                          </div>
                        </div>
                        {!isDisabled && (
                          <div style={{ width: '350px' }} className="mt-3">
                            {/* Account Balance Cards */}
                            <div>
                              {/* Current Account */}
                              <div>
                                <AccountBalanceCard />
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Narration */}

                      {/* Filter Row */}
                      <div className="d-flex mt-3 gap-3 align-items-end flex-wrap">
                        <CustomInput
                          type="text"
                          placeholder="Cheque Number"
                          value={chequeNumber}
                          onChange={(e) => setChequeNumber(e.target.value)}
                          rightIcon={FaMagnifyingGlass}
                          onButtonClick={() => {
                            console.log('Cheque Number Search');
                          }}
                          disabled={isDisabled}
                        />
                        <CustomInput
                          type="date"
                          label="Date"
                          value={filterDate}
                          onChange={(e) => setFilterDate(e.target.value)}
                          disabled={isDisabled}
                        />
                        <div className="combined-select-container mb-3">
                          <label htmlFor="">Ledger</label>
                          <div
                            className={`combined-select-input ${
                              isDisabled ? 'disabled-combined-select' : ''
                            }`}
                          >
                            <div className="combined-select-left">

                              <SearchableSelect
                                options={[
                                  { label: 'PL', value: 'party' },
                                  { label: 'GL', value: 'general' },
                                  { label: 'WIC', value: 'walkin' },
                                ]}
                                className={'ledger-select__control'}
                                value={filterLedger}
                                onChange={(selected) =>
                                  setFilterLedger(selected.value)
                                }
                                isDisabled={isDisabled}
                              />
                            </div>
                            <div className="separator-between-selects">|</div>
                            <div className="combined-select-right">

                              <SearchableSelect
                                className="ledger-select__control"
                                options={getAccountsByTypeOptions(filterLedger)}
                                value={filterAccount}
                                onChange={(selected) => {
                                  if (typeof selected?.value === 'string' && selected.value.includes('add new')) {
                                    setShowAddLedgerModal(selected.value);
                                  } else{
                                    setFilterAccount( selected.value);
                                  }

                                }}
                                placeholder="Select From Accounts"
                                error={touched.fromAccount && errors.fromAccount}
                              />

                              {/*<SearchableSelect*/}
                              {/*  options={[{ label: 'All', value: 'all' }]}*/}
                              {/*  value={filterAccount}*/}
                              {/*  onChange={(selected) =>*/}
                              {/*    setFilterAccount(selected.value)*/}
                              {/*  }*/}
                              {/*  isDisabled={isDisabled}*/}
                              {/*  className={'ledger-select__control'}*/}
                              {/*/>*/}
                            </div>
                          </div>
                        </div>
                        {/* <div className="mb-3">
                  </div>
                  <div className="mb-3">
                  </div> */}
                        <div className="mb-3">
                          <SearchableSelect
                            label="Currency"
                            options={currencyOptions}
                            value={currency}
                            onChange={(selected) => {
                              setCurrency(selected.value)
                              setFieldValue('currency', selected.value);
                            }}
                            // onChange={(selected) => {
                            //   if (selected.label?.toLowerCase()?.startsWith('eth')) {
                            //     setShowMissingCurrencyRateModal(true);
                            //   } else {
                            //     setCurrency(selected.value);
                            //     setErrors({ ...errors, currency: '' });
                            //   }
                            // }}
                            isDisabled={isDisabled}
                          />
                        </div>
                        <CustomInput
                          type="text"
                          label="FC Amount"
                          placeholder="Enter FC Amount"
                          value={fcAmount}
                          onChange={(e) => setFcAmount(e.target.value)}
                          disabled={isDisabled}
                        />
                        <CustomButton className={'mb-3'} text="Apply" onClick={handleSearchReceiptVouchers} />
                      </div>
                      {/* Replace the table section with this */}
                      <CustomTable
                        hasFilters={false}
                        setFilters={false}
                        headers={pdcrTableHeaders}
                        pagination={pagination}
                        updatePagination={updatePagination}
                        isLoading={false}
                        sortKey={false}
                        sortOrder={false}
                        handleSort={false}
                      >
                        {!isDisabled && (
                          <tbody>

                          { receiptVoucherData.map((item) => (

                            <tr key={item.id}>
                              <td>
                                <label>
                                  <input
                                    type="radio"
                                    name="pdcr"
                                    onChange={() => setSelectedPdcr(item)}
                                  />
                                  <span>{/* Empty span for styling */}</span>
                                </label>
                              </td>
                              <td>{item.account}</td>
                              <td>
                                {item.cheque_number}
                              </td>
                              <td>{item.due_date}</td>
                              <td>{item.currency}</td>
                              <td>{item.fc_amount}</td>
                              <td>{item.bank}</td>
                            </tr>
                          ))}
                          {/*{MOCK_PDCR_DATA.map((item) => (*/}
                          {/*  <tr key={item.id}>*/}
                          {/*    <td>*/}
                          {/*      <label>*/}
                          {/*        <input*/}
                          {/*          type="radio"*/}
                          {/*          name="pdcr"*/}
                          {/*          onChange={() => setSelectedPdcr(item)}*/}
                          {/*        />*/}
                          {/*        <span>/!* Empty span for styling *!/</span>*/}
                          {/*      </label>*/}
                          {/*    </td>*/}
                          {/*    <td>{item.pdcr_party}</td>*/}
                          {/*    <td>{item.cheque_number}</td>*/}
                          {/*    <td>{item.due_date}</td>*/}
                          {/*    <td>{item.currency}</td>*/}
                          {/*    <td>{item.fc_amount}</td>*/}
                          {/*    <td>{item.pdcr_bank}</td>*/}
                          {/*  </tr>*/}
                          {/*))}*/}
                          </tbody>
                        )}
                      </CustomTable>

                      {/* Footer */}
                      <div className="mt-4">
                        <div className="d-flex align-items-start gap-3">
                          <div className="flex-grow-1">
                            {!isDisabled && (
                              <div className="d-flex gap-3 mt-3 flex-wrap">
                                <CustomButton text="Submit" type="submit" loading={loading}/>
                                <CustomButton
                                  text="Cancel"
                                  className="secondaryButton"
                                  onClick={handleCancel}
                                />
                              </div>
                            )}
                            <div className="mt-3">Last PPV Number: 05</div>
                          </div>
                          <div className="ms-auto d-flex mt-3 flex-grow-1 me-5 gap-2">
                            <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
                            <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
                            <FaPaperclip
                              onClick={() => setUploadAttachmentsModal(true)}
                              size={20}
                              style={{ cursor: 'pointer' }}
                            />
                          </div>
                        </div>
                        <div className="d-flex gap-3 align-items-center mt-2">
                          <div className="inline-block">
                            <CustomCheckbox
                              label="Account Balance"
                              style={{ border: 'none' }}
                              disabled={isDisabled}
                            />
                            <CustomCheckbox
                              label="Print"
                              style={{ border: 'none' }}
                              disabled={isDisabled}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                  </Form>
                )}
              </Formik>



            )}
          </div>
        )}
      </section>

      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          item={supportLogsData[0]}
          // uploadOnly
          getUploadedFiles={setAddedAttachment}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          console.log('Goto rate update screen');
          navigate('/transactions/remittance-rate-of-exchange', {
            state: { currencyToSelect },
          });
        }}
      />
    </>
  );
};

export default withFilters(withModal(PdcrIssueAsPdcp));
