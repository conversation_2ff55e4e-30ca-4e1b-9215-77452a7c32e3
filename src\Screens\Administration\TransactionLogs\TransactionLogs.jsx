import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getTransactionLogs } from '../../../Services/Administration/TransactionLogs';
import {
  statusClassMap,
  transactionLogsActionOptions,
  transactionTypeOptions,
} from '../../../Utils/Constants/SelectOptions';
import { transactionLogsHeaders } from '../../../Utils/Constants/TableHeaders';
import {
  convertUTCToLocalTime,
  downloadFile,
  formatDate,
  getUsersOptions,
  serialNum,
  showErrorToast,
} from '../../../Utils/Utils';

const TransactionLogs = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Transaction Logs');
  const [selectedItem, setSelectedItem] = useState(null);

  const {
    data: { data: transactionLogs = [] } = {},
    isLoading,
    isError,
    error,
  } = useFetchTableData(
    'transactionLogs',
    filters,
    updatePagination,
    getTransactionLogs
  );

  const handleEdit = (item) => {
    setSelectedItem(item);
  };

  if (isError) {
    showErrorToast(error);
  }

  function getPastTense(action) {
    const irregulars = {
      delete: 'Deleted',
      edit: 'Edited',
      create: 'created',
      approve: 'approved',
      reject: 'rejected',
      // Add more as needed
    };

    if (!action) return '';

    return (
      irregulars[action] ||
      action.charAt(0).toUpperCase() + action.slice(1) + 'ed'
    );
  }

  return (
    <>
      <section>
        <div className="d-flex justify-content-between flex-wrap gap-3 mb-3">
          <h2 className="screen-title mb-0">Transaction Logs</h2>
          <div className="d-flex gap-2">
            <CustomButton
              variant="secondaryButton"
              text={'Export to Excel'}
              onClick={() => downloadFile('transaction-log', 'xlsx')}
            />
            <CustomButton
              variant="secondaryButton"
              text={'Export to PDF'}
              onClick={() => downloadFile('transaction-log', 'pdf')}
            />
          </div>
        </div>
        <Row>
          <Col xs={12} className="mb-4">
            <CustomTable
              filters={filters}
              setFilters={setFilters}
              headers={transactionLogsHeaders}
              pagination={pagination}
              isLoading={isLoading}
              dateFilters={[{ title: 'Editing Period' }]}
              rangeFilters={[{ title: 'Range of No.' }]}
              selectOptions={[
                {
                  title: 'Transaction Type',
                  options: transactionTypeOptions,
                },
                {
                  title: 'User',
                  options: [{ label: 'All', value: '' }, ...getUsersOptions()],
                },
                {
                  title: 'Action Type',
                  options: transactionLogsActionOptions,
                },
              ]}
            >
              {(transactionLogs.length || isError) && (
                <tbody>
                  {isError && (
                    <tr>
                      <td colSpan={transactionLogsHeaders.length}>
                        <p className="text-danger mb-0">
                          Unable to fetch data at this time
                        </p>
                      </td>
                    </tr>
                  )}
                  {transactionLogs?.map((item, index) => (
                    <tr key={item.id}>
                      <td>
                        {serialNum(
                          (filters?.page - 1) * filters?.per_page + index + 1
                        )}
                      </td>
                      <td>{item?.transaction_type}</td>
                      <td>{item?.number}</td>

                      <td>{item?.transaction_date}</td>
                      <td>{item?.modification_date}</td>
                      <td>{convertUTCToLocalTime(item?.modification_time)}</td>
                      <td>{item?.voucher?.creator?.user_name}</td>
                      <td>
                        <p
                          onClick={() => handleEdit(item)}
                          className={`mb-0 underlineOnHover cp ${
                            statusClassMap[item?.action_type?.toLowerCase()]
                          }`}
                        >
                          {getPastTense(item?.action_type)}
                        </p>
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </CustomTable>
          </Col>
          <Col xs={6} className="mb-5">
            <CustomInput
              type="textarea"
              rows={10}
              borderRadius={10}
              disabled={true}
              label={'Old Transaction Details'}
              value={selectedItem?.old_data}
            />
          </Col>
          <Col xs={6} className="mb-5">
            <CustomInput
              type="textarea"
              rows={10}
              borderRadius={10}
              disabled={true}
              label={'New Transaction Details'}
              value={selectedItem?.new_data}
            />
          </Col>
        </Row>
      </section>
    </>
  );
};

export default withFilters(TransactionLogs);
