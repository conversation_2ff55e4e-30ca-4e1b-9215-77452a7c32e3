import { Form, Formik } from 'formik';
import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa6';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';

const OutwardRemittanceAdd = ({
  isDisabled = false,
  setIsDisabled,
  setShowAddChequeDetailModal,
  setShowAddLedgerModal,
  setShowMissingCurrencyRateModal,
  setShowOutOfScopeModal,
  setCurrencyToSelect,
  setPageState,
}) => {
  const handleSubmit = (values) => {
    console.log(values);
    if (values.back_to_back === 'yes') {
      setPageState('allocation');
    }
  };

  return (
    <Formik
      initialValues={{
        reference_number: '',
        ledger: '',
        account: '',
        beneficiary: '',
        address: '',
        nationality: '',
        bank_name: '',
        bank_account: '',
        swift_code: '',
        routing_number: '',
        city: '',
        country: '',
        corresponding_bank: '',
        bank_account_number: '',
        purpose: '',
        by_order: '',
        send_fc: '',
        against: '',
        send_amount: '',
        rate: '',
        currency_charges: '',
        vat_term: '',
        vat_amount: '',
        net_total: '',
        base_rate: '',
        lcy_amount: '',
        settle_thru: '',
        remitter: '',
        remitter_telephone_number: '',
        remitter_nationality: '',
        id_no: '',
        valid_upto: '',
        company: '',
        back_to_back: '',
      }}
      onSubmit={handleSubmit}
    >
      {({ values, handleChange, handleBlur, setFieldValue }) => (
        <Form>
          <div className="row">
            <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
              <div className="row mb-4">
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'reference_number'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Reference Number'}
                    placeholder={'Enter Reference Number'}
                    value={values.reference_number}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'ledger'}
                    label={'Ledger'}
                    options={[]}
                    isDisabled={isDisabled}
                    placeholder={'Select Ledger'}
                    value={values.ledger}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'account'}
                    label={'Account'}
                    options={[
                      {
                        label: 'Add New PL',
                        value: null,
                      },
                      {
                        label: 'Add New WIC',
                        value: null,
                      },
                      {
                        label: 'Add New GL',
                        value: null,
                      },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Account'}
                    value={values.account}
                    onChange={(selected) => {
                      if (
                        selected.label?.toLowerCase()?.startsWith('add new')
                      ) {
                        setShowAddLedgerModal(selected.label?.toLowerCase());
                      } else {
                        setFieldValue('account', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'beneficiary'}
                    label={'Beneficiary'}
                    options={[
                      {
                        label: 'Beneficiary 1',
                        value: 'b1',
                      },
                      {
                        label: 'Beneficiary 2',
                        value: 'b2',
                      },
                      {
                        label: 'Add New Beneficiary',
                        value: null,
                      },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Beneficiary'}
                    value={values.beneficiary}
                    onChange={(selected) => {
                      if (
                        selected.label?.toLowerCase()?.startsWith('add new')
                      ) {
                        setShowAddLedgerModal(selected.label?.toLowerCase());
                      } else {
                        setFieldValue('beneficiary', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'address'}
                    type={'text'}
                    disabled={true}
                    label={'Address'}
                    placeholder={'Enter Address'}
                    value={values.address}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'nationality'}
                    type={'text'}
                    disabled={true}
                    label={'Nationality'}
                    placeholder={'Enter Nationality'}
                    value={values.nationality}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'bank_name'}
                    type={'text'}
                    disabled={true}
                    label={'Bank Name'}
                    placeholder={'Enter Bank Name'}
                    value={values.bank_name}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'bank_account'}
                    type={'text'}
                    disabled={true}
                    label={'Bank A/C'}
                    placeholder={'Enter Bank A/C'}
                    value={values.bank_account}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'swift_code'}
                    type={'text'}
                    disabled={true}
                    label={'SWIFT Code'}
                    placeholder={'Enter SWIFT Code'}
                    value={values.swift_code}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'routing_number'}
                    type={'text'}
                    disabled={true}
                    label={'Routing Number'}
                    placeholder={'Enter Routing Number'}
                    value={values.routing_number}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'city'}
                    type={'text'}
                    disabled={true}
                    label={'City'}
                    placeholder={'Enter City'}
                    value={values.city}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'country'}
                    type={'text'}
                    disabled={true}
                    label={'Country'}
                    placeholder={'Enter Country'}
                    value={values.country}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'corresponding_bank'}
                    type={'text'}
                    disabled={true}
                    label={'Corresponding Bank'}
                    placeholder={'Enter Corresponding Bank'}
                    value={values.corresponding_bank}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'bank_account_number'}
                    type={'text'}
                    disabled={true}
                    label={'Bank Account Number'}
                    placeholder={'Enter Bank Account Number'}
                    value={values.bank_account_number}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'purpose'}
                    type={'text'}
                    disabled={true}
                    label={'Purpose'}
                    placeholder={'Enter Purpose'}
                    value={values.purpose}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'by_order'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'By Order'}
                    placeholder={'Enter By Order'}
                    value={values.by_order}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'send_fc'}
                    label={'Send FC'}
                    options={[
                      {
                        label: 'USD',
                        value: 'usd',
                      },
                      {
                        label: 'EUR',
                        value: 'eur',
                      },
                      {
                        label: 'GBP',
                        value: 'gbp',
                      },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Currency'}
                    value={values.send_fc}
                    onChange={(selected) => {
                      setFieldValue('send_fc', selected.value);
                      if (selected.value === 'usd') {
                        setShowMissingCurrencyRateModal(true);
                        setCurrencyToSelect('usd');
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'send_amount'}
                    label={'Send Amount'}
                    isDisabled={isDisabled}
                    placeholder={'Currency | Enter Amount'}
                    value={values.send_amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'rate'}
                    label={'Rate'}
                    isDisabled={isDisabled}
                    placeholder={'x | 00000000'}
                    value={values.rate}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'currency_charges'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Currency A Charges'}
                    placeholder={'Enter Charges | Currency A | 0.00'}
                    value={values.currency_charges}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'against'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Against'}
                    placeholder={'Currency | 0.00'}
                    value={values.against}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <SearchableSelect
                    name={'vat_term'}
                    type={'text'}
                    options={[
                      {
                        label: 'VAT 1',
                        value: 'vat1',
                      },
                      {
                        label: 'Out of Scope',
                        value: 'out_of_scope',
                      },
                    ]}
                    isDisabled={isDisabled}
                    label={'VAT Term'}
                    placeholder={'Select VAT Term'}
                    value={values.vat_term}
                    onChange={(selected) => {
                      if (selected.value === 'out_of_scope') {
                        setShowOutOfScopeModal(true);
                      } else {
                        setFieldValue('vat_term', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'vat_amount'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'VAT Amount'}
                    placeholder={'Currency A | 0.00'}
                    value={values.vat_amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'net_total'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Net Total'}
                    placeholder={'Currency A | 0.00'}
                    value={values.net_total}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'base_rate'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Base Rate'}
                    placeholder={'0.00'}
                    value={values.base_rate}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'lcy_amount'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'LCy Amount'}
                    placeholder={'Currency A | 0.00'}
                    value={values.lcy_amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <SearchableSelect
                    name={'settle_thru'}
                    label={'Settle Thru'}
                    isDisabled={isDisabled}
                    placeholder={'On A/C'}
                    value={values.settle_thru}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
              </div>
            </div>
            <div className="col-0  col-xxl-2" />
            <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
              <div className="row">
                {/* Right side cards */}
                {!isDisabled && (
                  <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
                    {/* Account Balance Cards */}
                    <AccountBalanceCard />
                  </div>
                )}
                <div className="col-12 col-sm-6 col-xxl-12 mb-3">
                  <CustomInput
                    name={'remitter'}
                    type={'text'}
                    disabled={true}
                    label={'Remitter'}
                    placeholder={'Enter Remitter'}
                    value={values.remitter}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 col-xxl-12 mb-3">
                  <CustomInput
                    name={'remitter_telephone_number'}
                    type={'text'}
                    disabled={true}
                    label={'Remitter Telephone Number'}
                    placeholder={'Enter Remitter Telephone Number'}
                    value={values.remitter_telephone_number}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 col-xxl-12 mb-3">
                  <SearchableSelect
                    label={'Nationality'}
                    name={'remitter_nationality'}
                    isDisabled={true}
                    placeholder={'Select Nationality'}
                    value={values.remitter_nationality}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 col-xxl-12 mb-3">
                  <CustomInput
                    name={'id_no'}
                    type={'text'}
                    disabled={true}
                    label={'ID No'}
                    placeholder={'Enter ID No'}
                    value={values.id_no}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 col-xxl-12 mb-3">
                  <CustomInput
                    name={'valid_upto'}
                    type={'date'}
                    disabled={true}
                    label={'Valid Upto'}
                    placeholder={'Enter Valid Upto'}
                    value={values.valid_upto}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 col-xxl-12 mb-3">
                  <CustomInput
                    name={'company'}
                    type={'text'}
                    disabled={true}
                    label={'Company'}
                    placeholder={'Enter Company'}
                    value={values.company}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
              </div>
            </div>
            <div className="row">
              <div className="col-12 col-lg-6 d-flex justify-content-between mt-3 mb-5">
                <div>
                  <div>Last FSN Number: 05</div>
                  <div className="d-inline-block mt-3">
                    <CustomCheckbox
                      label="Account Balance"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setShowAddChequeDetailModal(true);
                        }
                      }}
                    />
                    <CustomCheckbox
                      label="Back to Back Entry"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFieldValue('back_to_back', 'yes');
                        } else {
                          setFieldValue('back_to_back', '');
                        }
                      }}
                    />
                    <CustomCheckbox
                      label="Print"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                    />
                  </div>
                </div>
                <div className="d-flex gap-2 voucher-navigation-wrapper">
                  <FaChevronLeft size={24} />
                  <FaChevronRight size={24} />
                </div>
              </div>
            </div>
            {!isDisabled && (
              <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                <CustomButton
                  type={'button'}
                  text={'Submit'}
                  onClick={() => {
                    handleSubmit(values);
                  }}
                />
                <CustomButton
                  variant={'secondaryButton'}
                  text={'Cancel'}
                  type={'button'}
                  onClick={() => {
                    setIsDisabled(true);
                  }}
                />
              </div>
            )}
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default OutwardRemittanceAdd;
