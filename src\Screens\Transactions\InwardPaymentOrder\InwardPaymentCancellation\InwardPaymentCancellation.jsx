import { useQuery } from '@tanstack/react-query';
import { Col, Row } from 'react-bootstrap';
import { HiOutlineTrash } from 'react-icons/hi2';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import TableActionDropDown from '../../../../Components/TableActionDropDown/TableActionDropDown';
import withFiltersWithoutPagination from '../../../../HOC/withFiltersWithoutPagination ';
import withModal from '../../../../HOC/withModal';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import { getInwardPaymentCancelListing } from '../../../../Services/Transaction/InwardPayment';
import { inwardPaymentCancellationHeaders } from '../../../../Utils/Constants/TableHeaders';

const InwardPaymentCancellation = ({
  filters,
  setFilters,
  showModal,
  closeModal,
}) => {
  usePageTitle('Inward Payment Cancellation');

  const paymentTypeOptions = [
    { label: 'DPV - Payment Voucher', value: 'dpv' },
    { label: 'CA - Credit Adjustment', value: 'ca' },
  ];
  const handleDelete = (id) => {
    showModal(
      'Delete',
      'Are you sure you want to delete this inward payment?',
      () => {
        console.log(id);
        // Close the first modal before showing the second one
        closeModal();
        // Show success modal after a small delay to ensure smooth transition
        setTimeout(() => {
          showModal(
            'Deleted',
            `Inward Payment Deleted successfully`,
            false,
            'success'
          );
        }, 100);
      }
    );
  };

  // Data fetching
  const {
    data: inwardPaymentCancelData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['getInwardPaymentCancelListing', filters],
    queryFn: () => getInwardPaymentCancelListing(filters),
    refetchOnWindowFocus: false,
    retry: 1,
  });

  return (
    <section>
      <h2 className="screen-title mb-3">Inward Payment Cancellation</h2>
      <Row>
        <Col xs={12}>
          <CustomTable
            hideItemsPerPage
            headers={inwardPaymentCancellationHeaders}
            data={inwardPaymentCancelData}
            filters={filters}
            setFilters={setFilters}
            hideSearch
            dateFilters={[{ title: 'Paid', from: 'from', to: 'to' }]}
            selectOptions={[
              {
                title: 'type',
                options: paymentTypeOptions,
              },
            ]}
            isLoading={isLoading}
            isPaginated={false}
            useClearButton
          >
            {(inwardPaymentCancelData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={inwardPaymentCancellationHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {inwardPaymentCancelData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item?.debit_note_number}</td>
                    <td>{item?.settlement_no}</td>
                    <td>{item?.pay_date}</td>
                    <td>{item?.account}</td>
                    <td>{item?.beneficiary}</td>
                    <td>{item?.mode}</td>
                    <td>{item?.currency}</td>
                    <td>{item?.fc_amount}</td>
                    <td>{item?.paid_by}</td>
                    <td>
                      <TableActionDropDown
                        actions={[
                          {
                            name: 'Delete',
                            icon: HiOutlineTrash,
                            onClick: () => handleDelete(item.id),
                            className: 'delete',
                          },
                        ]}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};
export default withModal(
  withFiltersWithoutPagination(InwardPaymentCancellation)
);
