/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import {
  MOCK_EXCHANGE_RATES,
  MOCK_INTERNAL_PAYMENT_VOUCHER_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import { internalPaymentVoucherHeaders } from '../../../Utils/Constants/TableHeaders';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import ExchangeRatesCard from '../../../Components/ExchangeRatesCard/ExchangeRatesCard';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { deletePaymentVoucher, getPaymentVoucherListing } from '../../../Services/Transaction/PaymentVoucher.js';
import { showToast } from '../../../Components/Toast/Toast.jsx';
import {
  deleteInternalPaymentVoucher,
  getInternalPaymentVoucherListing,
} from '../../../Services/Transaction/InternalPaymentVoucher.js';
import Skeleton from 'react-loading-skeleton';

const ViewInternalPaymentVoucher = ({
  setPageState,

                                      searchTerm,
                                      setDate,
                                      setWriteTerm,
                                      setSearchTerm,
     lastVoucherNumbers,
}) => {
  const [tableData, setTableData] = useState(
    MOCK_INTERNAL_PAYMENT_VOUCHER_DATA?.tableData
  );
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '';
    return typeof value === 'number' ? value.toFixed(2) : value;
  };

  const queryClient = useQueryClient();
  const {
    data: { data: [paymentVoucherData] = [] } = {},
    isLoading,
    isFetching,
    isError,
    error,
  } = useQuery({
    queryKey: ['internalPaymentVoucher', searchTerm],
    queryFn: () => getInternalPaymentVoucherListing({ search: searchTerm }),
    staleTime: 1000 * 60 * 5,
  });

  const paymentVoucher = paymentVoucherData?.internal_payment_vouchers;
 const ipvVatDetails = paymentVoucherData?.internal_payment_vouchers?.vat_details;


 // console.log('paymentVoucher',paymentVoucher);
   // console.log('voucher data',paymentVoucherData);
  // console.log('vat details',paymentVoucherData?.internal_payment_vouchers?.vat_details);


  useEffect(() => {
    if (paymentVoucherData?.voucher_no) {
      setDate(paymentVoucherData.date);
      setWriteTerm(paymentVoucherData.voucher_no);
    }
  }, [paymentVoucherData?.voucher_no]);

  // Mutation for delete
  const deleteInternalPaymentVoucherMutation = useMutation({
    mutationFn: (id) => deleteInternalPaymentVoucher(id),
    onSuccess: () => {
      showToast('Internal Payment Voucher deleted successfully!', 'success');
      queryClient.invalidateQueries(['internalPaymentVoucher', searchTerm]);
      setShowDeleteModal(false);
      setPageState('new');
      setWriteTerm('');
      setSearchTerm('');
      setDate(new Date().toISOString().split('T')[0]);
    },
    onError: (error) => {
      setShowDeleteModal(false);
      showErrorToast(error);
    },
  });

  // Navigation Actions
  const handleEdit = () => {
    setPageState('edit');
  };
  const handleDelete = () => {
    setShowDeleteModal(true);
  };
  const handlePrint = () => {
    if (paymentVoucherData?.pdf_url) {
      window.open(paymentVoucherData?.pdf_url, '_blank');
    }
  };


    if (isLoading) {
      return (
        <div className="d-card ">
          <div className="row">
            <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
              <div className="row mb-4">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div
                    key={i}
                    className="col-12 col-sm-6 mb-3 align-items-center"
                    style={{ height: 56 }}
                  >
                    <Skeleton
                      style={{ marginTop: 28 }}
                      duration={1}
                      width={'50%'}
                      baseColor="#ddd"
                      height={22}
                    />
                  </div>
                ))}
                <div
                  className="col-12 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'100%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
                {Array.from({ length: 6 }).map((_, i) => (
                  <div
                    key={i}
                    className="col-12 col-sm-6 mb-3 align-items-center"
                    style={{ height: 56 }}
                  >
                    <Skeleton
                      style={{ marginTop: 28 }}
                      duration={1}
                      width={'50%'}
                      baseColor="#ddd"
                      height={22}
                    />
                  </div>
                ))}
                <div
                  className="col-12 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'100%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
  
    if (isError) {
      return (
        <>
          <div className="d-card">
            <p className="text-danger">{error.message}</p>
          </div>
        </>
      );
    }
    if (isNullOrEmpty(paymentVoucher)) {
      return (
        <>
          <div className="d-card">
            <p className="text-danger">
              No Internal Payment Voucher found for ID {searchTerm}
            </p>
          </div>
        </>
      );
    }
  
  

  return (
    <>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7 mb-4">
            <div className="row">
              {[
                {
                  label: 'Ledger',
                  value: paymentVoucher?.ledger,
                },
                {
                  label: 'Credit Account',
                  value: paymentVoucher?.credit_account_details?.title,
                },
                {
                  label: 'Credit Ledger',
                  value: paymentVoucher?.credit_ledger,
                },
                {
                  label: 'Cost Center',
                  value: paymentVoucher?.cost_center?.code,
                },
                {
                  label: 'Mode',
                  value: paymentVoucher?.mode,
                },
                {
                  label: 'Paid From Account',
                  value:
                  paymentVoucher?.mode_account_id?.account_name,
                },
                {
                  label: 'Cheque Number',
                  value:
                  paymentVoucher?.cheque?.cheque_number,
                },
                {
                  label: 'Due Date',
                  value: paymentVoucher?.due_date,
                },
                {
                  label: 'Currency',
                  value: paymentVoucher?.currency?.currency_code,
                },
                {
                  label: 'Amount',
                  value: paymentVoucher?.amount,
                },
                {
                  label: 'Narration',
                  value: paymentVoucher?.narration,
                },

              ].map((x, i) => {
                if (isNullOrEmpty(x.value)) return null;
                return (
                  <div key={i} className="col-12 col-sm-6 mb-4">
                    <p className="detail-title detail-label-color mb-1">
                      {x.label}
                    </p>
                    <p className="detail-text wrapText mb-0">{x.value}</p>
                  </div>
                );
              })}
            </div>
          </div>
          <div className="col-0  col-xxl-2" />
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
            <div className="row">
              {/* Right side cards */}
              <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
                {/*<AccountBalanceCard />*/}
                {/*<ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} />*/}
              </div>
            </div>
          </div>
        </div>
        <CustomTable
          displayCard={false}
          headers={internalPaymentVoucherHeaders}
          isPaginated={false}
          hideSearch
          hideItemsPerPage
        >
          <tbody>
          {ipvVatDetails && ipvVatDetails.length > 0 ? (
            ipvVatDetails.map((x, i) => (
              <tr key={i}>
                <td>{i + 1}</td>
                <td>{x.ledger}</td>
                <td>{x.debit_account_details?.title}</td>
                <td>{x.narration}</td>
                <td>{x.currency?.currency_code}</td>
                <td>{x.amount}</td>
                <td>{x.vat_terms}</td>
                <td>{x.vat_percentage}</td>
                <td>{x.vat_amount}</td>
                <td>{parseFloat(x.vat_amount) + parseFloat(x.amount)}</td>
              </tr>
            ))
          ) : (
            <tr>
            <td colSpan="8">No VAT details available</td>
            </tr>
          )}
          </tbody>

          {/*<tbody>*/}
          {/*  {tableData?.map((x, i) => (*/}
          {/*    <tr key={i}>*/}
          {/*      <td>{i + 1}</td>*/}
          {/*      <td>{x.ledger}</td>*/}
          {/*      <td>{x.debit_account}</td>*/}
          {/*      <td>{x.narration}</td>*/}
          {/*      <td>{x.currency}</td>*/}
          {/*      <td>{x.amount}</td>*/}
          {/*      <td>{x.vat_percentage}</td>*/}
          {/*      <td>{x.vat_amount}</td>*/}
          {/*    </tr>*/}
          {/*  ))}*/}
          {/*</tbody>*/}
        </CustomTable>
      </div>

      <VoucherNavigationBar
        // actionButtons={[
        //   {
        //     text: 'Edit',
        //     onClick: () => {
        //       setPageState('edit');
        //     },
        //   },
        //   {
        //     text: 'Delete',
        //     onClick: () => {
        //       setShowDeleteModal(true);
        //     },
        //     variant: 'secondaryButton',
        //   },
        //   ...(paymentVoucherData?.pdf_url
        //     ? [
        //         {
        //           text: 'Print',
        //           onClick: handlePrint,
        //           variant: 'secondaryButton',
        //         },
        //       ]
        //     : []),
        // ]}
           actionButtons={[
          { text: 'Edit', onClick: handleEdit },
          { text: 'Delete', onClick: handleDelete, variant: 'secondaryButton' },
          ...(paymentVoucherData?.pdf_url
            ? [
                {
                  text: 'Print',
                  onClick: handlePrint,
                  variant: 'secondaryButton',
                },
              ]
            : []),
        ]}
        loading={isLoading || isFetching}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
        lastVoucherHeading="Last IPV Number"
        lastVoucherNumbers={lastVoucherNumbers}
      />
      {/* Upload Attachements Modal */}
      {/* <CustomModal
        show={showAttachmentsModal}
        
         onAttachmentClick={() => setShowAttachmentsModal(true)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          item={paymentVoucherData}
          closeUploader={() => setShowAttachmentsModal(false)}
        />
      </CustomModal> */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          items={paymentVoucherData}
          closeUploader={() => setShowAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Delete Modal */}
      <CustomModal
        show={showDeleteModal}
        close={() => {
          setShowDeleteModal(false); // Close the modal on cancel
        }}
        action={() => {
          if (paymentVoucherData) {
            deleteInternalPaymentVoucherMutation.mutate(paymentVoucherData.voucher_no);
          }
        }}
        title="Delete"
        description={`Are you sure you want to delete Payment Voucher ${searchTerm}?`}
        // disableClick={deletePackageMutation.isLoading} // Disable modal actions while loading
      />
    </>
  );
};

export default ViewInternalPaymentVoucher;
