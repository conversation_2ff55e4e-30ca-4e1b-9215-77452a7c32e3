import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import { financialReportData } from '../../../Mocks/MockData';
import { financialReportHeaders } from '../../../Utils/Constants/TableHeaders';

const BalanceSheet = ({ filters, setFilters, pagination }) => {
  const tableData = financialReportData.detail;
  const isLoading = false;
  const isError = false;

  const getBackgroundColor = (level) => {
    if (level === 1) return '#FFF4E0';
    if (level === 2) return '#81FFEB';
    if (level === 3) return '#FFBCBC';
    if (level === 4) return '#F7C8FF';
    return 'unset';
  };
  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">Balance Sheet</h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            isLoading={isLoading}
            headers={financialReportHeaders}
            isPaginated={false}
            hideSearch
            additionalFilters={[{ title: 'Date', type: 'date' }]}
            hideItemsPerPage
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={financialReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item, i) => (
                  <tr
                    key={item.id}
                    style={{ backgroundColor: getBackgroundColor(item.level) }}
                  >
                    <td>{item.account}</td>
                    <td>{item.fcy ?? '---------'}</td>
                    <td>{item.fc_debit ?? '---------'}</td>
                    <td>{item.fc_credit ?? '---------'}</td>
                    <td>{item.lc_debit ?? '---------'}</td>
                    <td>{item.lc_credit ?? '---------'}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(BalanceSheet);
