import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { MOCK_DEAL_REGISTRY_DATA, MOCK_SUMMARY_TABLE_DATA } from '../../../Mocks/MockData';
import { dealRegistryHeaders, summaryTableHeaders } from '../../../Utils/Constants/TableHeaders';
import { useNavigate } from 'react-router-dom';

const DealRegistry = ({ filters, setFilters, pagination }) => {
  usePageTitle('Deal Registry');
  const tableData = MOCK_DEAL_REGISTRY_DATA;
  const isLoading = false;
  const isError = false;
  const navigate = useNavigate();
  return (
    <section>
      <div className="d-flex flex-wrap align-items-center gap-3 justify-content-between mb-3">
        <h2 className="screen-title m-0 d-inline">Deal Registry</h2>
        <div className="d-flex align-items-center flex-wrap gap-3">
          <CustomButton className={'secondaryButton'} text={'Refresh'} />
          <CustomButton className={'secondaryButton'} text={'Print'} />
          <CustomButton className={'secondaryButton'} text={'Position Summary'} onClick={() => navigate('/transactions/position-summary')} />
          <CustomButton text={'Generate Average Rate'} />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            hideItemsPerPage
            filters={filters}
            setFilters={setFilters}
            headers={dealRegistryHeaders}
            isLoading={isLoading}
            isPaginated={false}
            hideSearch
            selectOptions={[
              {
                title: 'Currency',
                options: [{ value: 'DHS', label: 'DHS' }, { value: 'USD', label: 'USD' }],
              },
            ]}
            additionalFilters={[
              {
                title: 'Date',
                type: 'date',
              },
            ]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={dealRegistryHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item?.account}</td>
                    <td>{item?.buy}</td>
                    <td>{item?.sell}</td>
                    <td>{item?.ag_fcy}</td>
                    <td>{item?.ag_fc_amt}</td>
                    <td>{item?.rate}</td>
                    <td>{item?.user_id}</td>
                    <td>{item?.convert_rate}</td>
                    <td>{item?.trans_no}</td>
                    <td>{item?.value_date}</td>
                    <td>{item?.description}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>

          {/* Add Summary Table */}
          <div className="mt-4">
            <CustomTable
              hideItemsPerPage
              hideSearch
              isPaginated={false}
              headers={summaryTableHeaders}
              isLoading={isLoading}
            >
              <tbody>
                {MOCK_SUMMARY_TABLE_DATA.map((item) => (
                  <tr key={item.id}>
                    <th>{item.type}</th>
                    <td>{item.fcAmount}</td>
                    <td>{item.rate}</td>
                    <td>{item.baseValue}</td>
                  </tr>
                ))}
              </tbody>
            </CustomTable>
          </div>
        </Col>
      </Row>
    </section>
  );
};
export default withFilters(DealRegistry);
