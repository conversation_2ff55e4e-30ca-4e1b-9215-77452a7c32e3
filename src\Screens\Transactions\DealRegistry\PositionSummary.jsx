import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { MOCK_POSITION_SUMMARY_DATA } from '../../../Mocks/MockData';
import { positionSummaryHeaders } from '../../../Utils/Constants/TableHeaders';

const PositionSummary = () => {
  usePageTitle('Position Summary');
  const tableData = MOCK_POSITION_SUMMARY_DATA;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <h2 className="screen-title mb-3">Position Summary</h2>
      <Row>
        <Col xs={12}>
          <CustomTable
            hideItemsPerPage
            hasFilters={false}
            headers={positionSummaryHeaders}
            isLoading={isLoading}
            isPaginated={false}
            hideSearch
            selectOptions={[
              {
                title: 'Currency',
                options: [{ value: 'DHS', label: 'DHS' }, { value: 'USD', label: 'USD' }],
              },
            ]}
            additionalFilters={[
              {
                title: 'Date',
                type: 'date',
              },
            ]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={positionSummaryHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.currency}</td>
                    <td>{item.currencyName}</td>
                    <td>{item.fcOpening}</td>
                    <td>{item.fcBuy}</td>
                    <td>{item.fcSell}</td>
                    <td>{item.fcClosing}</td>
                    <td>{item.avgClosingRate}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};
export default PositionSummary;
