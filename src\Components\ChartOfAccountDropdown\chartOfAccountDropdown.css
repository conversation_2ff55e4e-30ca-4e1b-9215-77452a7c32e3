.coa-dropdown-container {
  width: 100%;
  border-radius: 5px;
  overflow: hidden;
}

.coa-dropdown-container > .coa-menu-item {
  margin-bottom: 12px;
}

.coa-menu-item {
  width: 100%;
}

.coa-menu-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.2s;
}

.coa-menu-item-content p {
  color: var(--input-label-color);
  cursor: pointer;
}

.coa-menu-item-content:hover {
  transition: none;
  background-color: var(--body-bg-color);
}

.coa-menu-item-content.level-1-account {
  padding-block: 0.5rem;
  background-color: var(--primary-color);
  color: var(--contrast-text-color);
}

.coa-menu-item-content.level-1-account p {
  color: var(--contrast-text-color);
}

.coa-menu-item-children {
  width: 100%;
}

.coa-account-checkbox {
  cursor: pointer;
}

.coa-menu-item-content-icon {
  path {
    fill: var(--table-td-color);
  }
}

.coa-menu-item-content:hover .coa-menu-item-content-icon,
.coa-menu-item-content.selected .coa-menu-item-content-icon {
  path {
    fill: var(--contrast-text-color);
  }
}
