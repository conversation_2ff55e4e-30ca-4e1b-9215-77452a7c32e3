import axiosInstance from '../../Config/axiosConfig';
import { buildFormData } from '../../Utils/Utils';

// GET VOUCHER NUMBER
export const getRVVoucherNumber = async (voucher_no = '') => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/receipt-voucher/voucher-number?voucher_no=${voucher_no}`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// GET VOUCHER LISTING AND DETAILS
export const getReceiptVoucherListing = async (params) => {
  try {
    const { data } = await axiosInstance.get(`/user-api/receipt-voucher`, {
      params,
    });
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// CREATE
export const createReceiptVoucher = async (formData) => {
  // const payload = new FormData();
  // buildFormData(payload, formData);
  try {
    const response = await axiosInstance.post(
      '/user-api/receipt-voucher',
      formData
    );
    const {
      data: { message, status, detail },
    } = response;
    return { message, status, detail }; // Assume this returns the success object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// UPDATE
export const updateReceiptVoucher = async (id, formData) => {
  try {
    const response = await axiosInstance.post(
      `/user-api/receipt-voucher/${id}`,
      formData
    );
    const {
      data: { message, status, detail },
    } = response;
    return { message, status, detail }; // Assume this returns the success object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// DELETE RECEIPT VOUCHER
export const deleteReceiptVoucher = async (id) => {
  try {
    const { data: { message, status } = {} } = await axiosInstance.delete(
      `/user-api/receipt-voucher/${id}`
    );
    return { message, status };
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// ATTACHMENTS
// ADD
export const addReceiptVoucherAttachment = async (id, formData) => {
  try {
    const payload = new FormData();
    buildFormData(payload, formData);
    const response = await axiosInstance.post(
      `/user-api/receipt-voucher/attachments/${id}`,
      payload
    );
    const {
      data: { message, status },
    } = response;
    return { message, status }; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// DELETE RECEIPT VOUCHER ATTACHMENT
export const deleteReceiptVoucherAttachment = async (id) => {
  try {
    const { data: { message, status } = {} } = await axiosInstance.delete(
      `/user-api/receipt-voucher/attachments/${id}`
    );
    return { message, status };
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// GET VAT TYPE
export const getVATType = async () => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/receipt-voucher/vat-type`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
// GET CURRENCIES
export const getCurrencies = async () => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/receipt-voucher/currency`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// GET ACCOUNTS BY TYPE -- party,walkin,general
export const getAccountsbyType = async (AccountType) => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/receipt-voucher/account?type=${AccountType}`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
// GET beneficiary ACCOUNTS BY TYPE AND ACCOUNT -- party/walkin, account_id
export const getBenefeciariesByAccount = async (account_id) => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/receipt-voucher/account?type=beneficiary&account=${account_id}`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
// GET COA ACCOUNTS BY MODE -- cash, bank, online, pdc
export const getCOAAccountsbyMode = async (modeType) => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/receipt-voucher/modes?type=${modeType}`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};



export const getReceiptVoucherDataForPdcr = async (params) => {
  try {
    const { data } = await axiosInstance.get(`/user-api/pdcp-issues/receipt-vouchers`, {
      params,
    });
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};