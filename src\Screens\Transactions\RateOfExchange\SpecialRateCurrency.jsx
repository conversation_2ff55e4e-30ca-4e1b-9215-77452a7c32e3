import React, { useState } from 'react';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { getCurrencyOptions } from '../../../Utils/Utils';
import { getSpecialRateCurrency } from '../../../Services/Transaction/SpecialRateCurrency';
import CustomButton from '../../../Components/CustomButton';
import { useQuery } from '@tanstack/react-query';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { specialRateCurrencyHeaders } from '../../../Utils/Constants/TableHeaders';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import CustomInput from '../../../Components/CustomInput';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { FaLock, FaLockOpen } from 'react-icons/fa6';
import { HiOutlineTrash } from 'react-icons/hi2';
import BackButton from '../../../Components/BackButton';

const SpecialRateCurrency = () => {
  usePageTitle('Special Rate Currency');
  const [searchTerm, setSearchTerm] = useState('');
  const [editableRows, setEditableRows] = useState({});
  const [editedData, setEditedData] = useState({});
  const [newRows, setNewRows] = useState([]);
  const currencyOptions = getCurrencyOptions();

  const {
    data: { data: specialRateCurrency = [] } = {},
    isLoading: isLoadingSpecialRateCurrency,
    isError: isErrorSpecialRateCurrency,
    error: errorSpecialRateCurrency,
  } = useQuery({
    queryKey: ['specialRateCurrency'],
    queryFn: getSpecialRateCurrency,
  });

  const handleDelete = (item) => {
    console.log('Delete', item);
  };

  const handleEdit = (item) => {
    setEditableRows((prev) => ({
      ...prev,
      [item.id]: !prev[item.id],
    }));

    // Initialize edited data for this row if not exists
    if (!editedData[item.id]) {
      setEditedData((prev) => ({
        ...prev,
        [item.id]: {
          ...item,
          action: item.action === 'lock' ? 'unlock' : 'lock',
          buy_rate: item.buy_rate,
          buy_from: item.buy_from,
          buy_upto: item.buy_upto,
          sell_rate: item.sell_rate,
          sell_from: item.sell_from,
          sell_upto: item.sell_upto,
        },
      }));
    } else {
      setEditedData((prev) => ({
        ...prev,
        [item.id]: {
          ...editedData[item.id],
          action: editedData[item.id].action === 'lock' ? 'unlock' : 'lock',
        },
      }));
    }
  };

  const handleInputChange = (itemId, field, value) => {
    setEditedData((prev) => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [field]: value,
      },
    }));
  };

  const handleAddRows = () => {
    const newRows = Array.from({ length: 10 }, (_, index) => {
      return {
        id: `new-${index}-${Date.now()}`, // Generate unique id for new row
        currency: '',
        ag_fcy: null,
        buy_rate: '',
        buy_from: '',
        buy_upto: '',
        sell_rate: '',
        sell_from: '',
        sell_upto: '',
        isNew: true, // Flag to identify new rows
      };
    });

    setNewRows((prev) => [...prev, ...newRows]);
    // Make the new row editable by default
    setEditableRows((prev) => ({
      ...prev,
      ...Object.fromEntries(newRows.map((row) => [row.id, true])),
    }));
    // Initialize edited data for the new row
    setEditedData((prev) => ({
      ...prev,
      ...Object.fromEntries(
        newRows.map((row) => [
          row.id,
          {
            currency: '',
            action: 'unlock',
            buy_rate: '',
            buy_from: '',
            buy_upto: '',
            sell_rate: '',
            sell_from: '',
            sell_upto: '',
          },
        ])
      ),
    }));
  };

  const handleCurrencyChange = (itemId, field, selected) => {
    setEditedData((prev) => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [field]: selected,
      },
    }));
  };

  const handleCancel = () => {
    setEditedData({});
    setNewRows([]);
    setEditableRows({});
  };

  return (
    <>
      <div className="d-flex gap-3 justify-content-start flex-wrap mb-4">
        <div>
          <BackButton />
          <h2 className="screen-title mb-0">Special Rate Currency</h2>
        </div>
      </div>

      <CustomTable
        headers={specialRateCurrencyHeaders}
        isLoading={isLoadingSpecialRateCurrency}
        isPaginated={false}
        className={'inputTable'}
        hideSearch
        hideItemsPerPage
      >
        {(specialRateCurrency?.length ||
          newRows.length ||
          isErrorSpecialRateCurrency) && (
          <tbody>
            {isErrorSpecialRateCurrency && !newRows.length && (
              <tr>
                <td colSpan={specialRateCurrencyHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {[...(specialRateCurrency || []), ...newRows].map((item) => {
              const editedItem = editedData[item.id];
              const isLocked =
                (!editableRows[item.id] && item.action === 'lock') ||
                editedData[item.id]?.action === 'lock';
              return (
                <tr key={item.id}>
                  <td>
                    <SearchableSelect
                      name="currency"
                      isDisabled={isLocked}
                      options={currencyOptions}
                      value={editedItem?.currency ?? item.currency?.id ?? ''}
                      onChange={(selected) =>
                        handleCurrencyChange(
                          item.id,
                          'currency',
                          selected.value
                        )
                      }
                      borderRadius={10}
                    />
                  </td>
                  <td>
                    <SearchableSelect
                      name="ag_fcy"
                      isDisabled={isLocked}
                      options={currencyOptions}
                      value={editedItem?.ag_fcy ?? item.ag_fcy?.id}
                      onChange={(selected) =>
                        handleCurrencyChange(item.id, 'ag_fcy', selected.value)
                      }
                      borderRadius={10}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'buy_rate'}
                      type={'number'}
                      value={editedItem?.buy_rate ?? item.buy_rate}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'buy_rate', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'buy_from'}
                      type={'number'}
                      value={editedItem?.buy_from ?? item.buy_from}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'buy_from', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'buy_upto'}
                      type={'number'}
                      value={editedItem?.buy_upto ?? item.buy_upto}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'buy_upto', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'sell_rate'}
                      type={'number'}
                      value={editedItem?.sell_rate ?? item.sell_rate}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'sell_rate', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'sell_from'}
                      type={'number'}
                      value={editedItem?.sell_from ?? item.sell_from}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'sell_from', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'sell_upto'}
                      type={'number'}
                      value={editedItem?.sell_upto ?? item.sell_upto}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'sell_upto', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <TableActionDropDown
                      actions={[
                        {
                          name: editableRows[item.id] ? 'Lock' : 'Unlock',
                          icon: editedItem
                            ? editedItem.action === 'lock'
                              ? FaLock
                              : FaLockOpen
                            : item.action === 'lock'
                            ? FaLock
                            : FaLockOpen,
                          onClick: () => handleEdit(item),
                          className: editableRows[item.id] ? 'view' : 'delete',
                        },
                        {
                          name: 'Delete',
                          icon: HiOutlineTrash,
                          onClick: () => handleDelete(item),
                          className: 'delete',
                        },
                      ]}
                    />
                  </td>
                </tr>
              );
            })}
          </tbody>
        )}
      </CustomTable>

      <div className="d-flex gap-3 flex-wrap mb-5 pb-5">
        <CustomButton text={'Add Rows'} onClick={handleAddRows} />
        <CustomButton
          variant={'secondaryButton'}
          text={'Save'}
          onClick={() => null}
        />
        <CustomButton
          variant={'secondaryButton'}
          text={'Cancel'}
          onClick={handleCancel}
        />
      </div>
    </>
  );
};

export default SpecialRateCurrency;
