import React, { useCallback, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import {
  FaChevronLeft,
  FaChevronRight,
  FaMagnifyingGlass,
  FaPaperclip,
} from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import AttachmentsView from '../../../../Components/AttachmentsView/AttachmentsView';
import BackButton from '../../../../Components/BackButton';
import CustomButton from '../../../../Components/CustomButton';
import CustomCheckbox from '../../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../../Components/CustomInput';
import CustomModal from '../../../../Components/CustomModal';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import { newTTRConfirmationHeaders } from '../../../../Utils/Constants/TableHeaders';

const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      doc_type: '12',
      doc_no: '111',
      narration: 'Lorem ipsum',
      tmn_amount: '100000',
    };
  });
  return rows;
};

const INITIAL_STATE = generateInitialRows(4);

const TTRConfirmationRow = ({ row, index, isDisabled, updateField }) => {
  return (
    <tr>
      <td>
        <SearchableSelect
          isDisabled={isDisabled}
          options={[]}
          placeholder="Select Doc Type"
          value={row.doc_type}
          onChange={(selected) => {
            updateField(row.id, 'doc_type', selected.value);
          }}
          borderRadius={10}
        />
      </td>

      <td>
        <CustomInput
          type={'text'}
          value={row.doc_no}
          placeholder="Enter Doc No."
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'doc_no', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.narration}
          placeholder="Enter Narration"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.tmn_amount}
          placeholder="Enter TMN Amount"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'tmn_amount', e.target.value)}
          borderRadius={10}
        />
      </td>
    </tr>
  );
};

const EditTTRConfirmation = () => {
  usePageTitle('Edit TTR Confirmation');
  const navigate = useNavigate();
  const [isDisabled, setIsDisabled] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [pageState, setPageState] = useState('edit');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [debitAccount, setDebitAccount] = useState('');
  const [creditAccount, setCreditAccount] = useState('');
  // const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [rows, setRows] = useState(INITIAL_STATE);
  const [unConfirmed, setUnConfirmed] = useState(0);
  const [totalConfirmed, setTotalConfirmed] = useState(0);
  const [tmnBalance, setTmnBalance] = useState(0);
  const [showError, setShowError] = useState(false);

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      // Calculate total debit whenever lcAmount or sign changes
      if (field === 'lcAmount' || field === 'sign') {
        const totalDebit = Object.values(newRows).reduce((sum, row) => {
          const amount = parseFloat(row.lcAmount) || 0;
          return row.sign === 'debit' ? sum + amount : sum;
        }, 0);
        setTotalDebit(totalDebit);
        const totalCredit = Object.values(newRows).reduce((sum, row) => {
          const amount = parseFloat(row.lcAmount) || 0;
          return row.sign === 'credit' ? sum + amount : sum;
        }, 0);
        setTotalCredit(totalCredit);
      }

      return newRows;
    });
  }, []);

  const addRows = (count) => {
    const newRows = {};
    Array.from({ length: count }).forEach(() => {
      const id = crypto.randomUUID();
      newRows[id] = {
        id,
        doc_type: '',
        doc_no: '',
        narration: '',
        tmn_amount: '',
      };
    });
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };
  const deleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };
  const resetRows = () => {
    setRows(generateInitialRows(4));
  };

  // Table Row Actions
  const handleAddRows = () => {
    addRows(6);
  };
  const handleDeleteRow = (id) => {
    deleteRow(id);
  };
  const handleResetRows = () => {
    resetRows();
    setIsDisabled(true);
  };

  const handleSubmit = () => {
    if (totalDebit - totalCredit !== 0) {
      setShowError(true);
      return;
    }
    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    // All inputs must be filled
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([_, v]) => {
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );
    console.log('submit', payload);
  };

  return (
    <>
      <section className="position-relative">
        <div className="d-flex gap-3 justify-content-between flex-wrap mb-4">
          <div className="d-flex flex-column gap-2">
            <BackButton />
            <h2 className="screen-title mb-0">Edit TTR Confirmation</h2>
          </div>
          <div className="d-flex gap-2">
            {isDisabled ? (
              <CustomButton text={'New'} onClick={() => setIsDisabled(false)} />
            ) : null}
          </div>
        </div>
        <Row>
          <Col xs={12}>
            <div className="d-flex align-items-start justify-content-between flex-wrap-reverse">
              <div className="d-flex gap-3 align-items-end">
                <CustomInput
                  style={{ width: '180px' }}
                  type="text"
                  placeholder="Search"
                  error={false}
                  showBorders={false}
                  borderRadius={10}
                  name="search"
                  rightIcon={FaMagnifyingGlass}
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                  }}
                  onButtonClick={() => {
                    console.log('search for:', searchTerm);
                    navigate(
                      `/transactions/ttr-register/confirmation/${searchTerm}/view`
                    );
                  }}
                />

                <SearchableSelect
                  label="Debit Account"
                  name="debitAccount"
                  options={[]}
                  borderRadius={10}
                  value={debitAccount}
                  onChange={(e) => {
                    setDebitAccount(e.target.value);
                  }}
                />
                <SearchableSelect
                  label="Credit Account"
                  name="creditAccount"
                  options={[]}
                  borderRadius={10}
                  value={creditAccount}
                  onChange={(e) => {
                    setCreditAccount(e.target.value);
                  }}
                />
              </div>
              <div>
                <CustomInput
                  name="Date"
                  label={'Date'}
                  type="date"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={date}
                  onChange={(e) => {
                    setDate(e.target.value);
                  }}
                />
              </div>
            </div>

            <CustomTable
              headers={newTTRConfirmationHeaders}
              isPaginated={false}
              className={'inputTable noActions'}
              hideSearch
              hideItemsPerPage
            >
              <tbody>
                {Object.values(rows).map((row, index) => (
                  <TTRConfirmationRow
                    key={row.id}
                    row={row}
                    index={index}
                    isDisabled={isDisabled}
                    updateField={updateField}
                  />
                ))}
              </tbody>
            </CustomTable>
            <div className="d-flex justify-content-end gap-3 mt-45 mb-5">
              <div className="d-flex flex-column gap-2 mt-1">
                <CustomInput
                  name="unConfirmed"
                  label={'Un-Confirmed'}
                  labelClass={'fw-medium'}
                  type="number"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={unConfirmed}
                  readOnly
                />
                <CustomInput
                  name="totalConfirmed"
                  label={'Total Confirmed'}
                  labelClass={'fw-medium'}
                  type="number"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={totalConfirmed}
                  readOnly
                />
                <CustomInput
                  name="tmnBalance"
                  label={'TMN Balance'}
                  labelClass={'fw-medium'}
                  type="number"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={tmnBalance}
                  readOnly
                  onChange={() => {
                    setTmnBalance(totalDebit - totalCredit);
                  }}
                />
              </div>
            </div>
          </Col>
        </Row>
        <div
          style={{ bottom: 20, minHeight: 77 }}
          className="d-card w-100 position-sticky mb-3 p-3 d-flex justify-content-between align-items-center"
        >
          {/* Button */}
          <div className="d-flex flex-column justify-content-center gap-4 ">
            {!isDisabled ? (
              <div className="d-flex gap-3 flex-wrap">
                {/* <CustomButton text={'Add Row'} onClick={handleAddRow} /> */}
                <CustomButton text={'Add Rows'} onClick={handleAddRows} />
                <CustomButton text={'Update'} onClick={handleSubmit} />
                <CustomButton
                  variant={'secondaryButton'}
                  text={'Cancel'}
                  onClick={handleResetRows}
                />
              </div>
            ) : null}
          </div>
          {/* navigation */}
          <div className="d-flex gap-2 voucher-navigation-wrapper position-absolute start-50 translate-middle-x">
            <FaChevronLeft size={24} />
            <FaChevronRight size={24} />
            <FaPaperclip
              size={24}
              onClick={() => setUploadAttachmentsModal(true)}
            />
          </div>
          {/* Print */}
          <div className="d-flex align-items-center gap-3 ms-auto">
            <p style={{ fontWeight: 500 }} className=" m-0">
              Last FSN Number: <span className="fw-medium">05</span>
            </p>
            <CustomCheckbox
              style={{ border: 'none', margin: 0 }}
              label={'Print'}
            />
          </div>
        </div>
      </section>

      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachment}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default EditTTRConfirmation;
