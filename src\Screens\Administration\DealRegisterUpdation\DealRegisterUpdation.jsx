import { useQueryClient } from '@tanstack/react-query';
import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { dealRegisterUpdationHeaders } from '../../../Utils/Constants/TableHeaders';
import { formatDate, showErrorToast } from '../../../Utils/Utils';
import { dealRegisterUpdationData } from '../../../Mocks/MockData';

const DealRegisterUpdation = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Deal Register Updation');
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data, isLoading, isError, error } = {};
  //   const { data, isLoading, isError, error } = useFetchTableData(
  //     'dealRegisterUpdationListing',
  //     filters,
  //     updatePagination
  //     getDealRegisterUpdationListing
  //   );

  //   const dealRegisterUpdation = data?.data || [];
  const dealRegisterUpdation = dealRegisterUpdationData;

  //  --- MUTATIONS ---

  if (isError) {
    showErrorToast(error);
  }
  if (isLoading) {
    return <>loading...</>;
  }
  return (
    <>
      <section>
        <div className="d-flex gap-3 justify-content-between flex-wrap mb-3">
          <h2 className="screen-title mb-0">Deal Register Updation</h2>
          <div className="d-flex gap-2">
            <CustomButton
              text={'Update Deal Register'}
              onClick={() => console.log('Update Deal Register clicked')}
            />
            <CustomButton
              text={'Recalculate Closing Rates'}
              onClick={() => console.log('Recalculate Closing Rates clicked')}
            />
          </div>
        </div>
        <Row>
          <Col xs={12}>
            <CustomTable
              filters={filters}
              setFilters={setFilters}
              headers={dealRegisterUpdationHeaders}
              pagination={pagination}
              isLoading={isLoading}
              selectOptions={[
                {
                  title: 'FCy',
                  options: [
                    { label: 'USD', value: 'USD' },
                    { label: 'EUR', value: 'EUR' },
                  ],
                },
              ]}
              dateFilters={[
                {
                  title: 'Period',
                },
                {
                  title: 'Date Range',
                },
              ]}
            >
              {(dealRegisterUpdation.length || isError) && (
                <tbody>
                  {isError && (
                    <tr>
                      <td colSpan={dealRegisterUpdationHeaders.length}>
                        <p className="text-danger mb-0">
                          Unable to fetch data at this time
                        </p>
                      </td>
                    </tr>
                  )}
                  {dealRegisterUpdation?.map((item) => (
                    <tr key={item.id}>
                      <td>{formatDate(item.date)}</td>
                      <td>{item.fcy}</td>
                      <td>{item.account}</td>
                      <td>{item.deal_register}</td>
                      <td>{item.counter}</td>
                      <td>{item.difference}</td>
                    </tr>
                  ))}
                </tbody>
              )}
            </CustomTable>
          </Col>
        </Row>
      </section>
    </>
  );
};

export default withFilters(DealRegisterUpdation);
