import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ErrorMessage, Form, Formik } from 'formik';
import React, { useEffect, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import MultiLevelDropdown from '../../../Components/MultiLevelDropdown/MultiLevelDropdown';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { showToast } from '../../../Components/Toast/Toast';
import withModal from '../../../HOC/withModal';
import useDataMutations from '../../../Hooks/useDataMutations';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import {
  addChartOfAccount,
  deleteChartOfAccount,
  editChartOfAccount,
  getChartOfAccountListing,
  getLevel1and2Dropdowns,
  getLevel3and4Dropdowns,
  viewChartOfAccount,
} from '../../../Services/Masters/ChartOfAccount';
import { convertCOAAccountsToDropdownOptions } from '../../../Utils/Helpers';
import {
  downloadFile,
  formatDate,
  isNullOrEmpty,
  showErrorToast,
} from '../../../Utils/Utils';
import { addCOAValidationSchema } from '../../../Utils/Validations/ValidationSchemas';

// { showModal, closeModal }
const ChartOfAccount = ({ showModal, closeModal }) => {
  //   const queryClient = useQueryClient();
  usePageTitle('Chart of Account');
  const [rightSectionType, setRightSectionType] = useState('');
  const [selectedAccountType, setSelectedAccountType] = useState(null);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const queryClient = useQueryClient();

  const topSection = () => (
    <>
      <div className="d-flex justify-content-end flex-wrap mb-3">
        <h2 className="screen-title mb-0 flex-grow-1">Chart Of Account</h2>
        <div className="d-flex gap-2 flex-wrap">
          <CustomButton
            variant="secondaryButton"
            text={'Export to Excel'}
            onClick={() => downloadFile('chart-of-accounts', 'xlsx')}
          />
          <CustomButton
            variant="secondaryButton"
            text={'Export to PDF'}
            onClick={() => downloadFile('chart-of-accounts', 'pdf')}
          />
          <CustomButton
            text={'New'}
            onClick={() => setRightSectionType('new')}
          />
        </div>
      </div>
    </>
  );
  const {
    data: chartOfAccounts,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['chartOfAccount'],
    queryFn: getChartOfAccountListing,
    refetchOnWindowFocus: false,
    retry: 1,
  });
  const {
    data: accountTypesData,
    isLoading: isLoadingAccountTypes,
    isError: isErrorAccountTypes,
    error: errorAccountTypes,
  } = useQuery({
    queryKey: ['chartOfAccountAccountTypes'],
    queryFn: getLevel1and2Dropdowns,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const {
    data: parentAccountTypesData,
    isLoading: isLoadingParentAccountTypes,
    isError: isErrorParentAccountTypes,
    error: errorParentAccountTypes,
  } = useQuery({
    queryKey: ['chartOfAccountParentAccountTypes', selectedAccountType],
    queryFn: () => getLevel3and4Dropdowns(selectedAccountType),
    enabled: !!selectedAccountType,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const {
    data: chartOfAccountDetails,
    isLoading: isLoadingAccountDetails,
    isError: isErrorAccountDetails,
    error: errorAccountDetails,
  } = useQuery({
    queryKey: ['chartOfAccountDetails', selectedAccount],
    queryFn: () => viewChartOfAccount(selectedAccount),
    enabled: !!selectedAccount,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  useEffect(() => {
    if (selectedAccount) {
      const accountType = chartOfAccountDetails?.account_type_id;
      setSelectedAccountType(accountType);
    }
  }, [selectedAccount, chartOfAccountDetails]);

  const addChartOfAccountMutation = useMutation({
    mutationFn: addChartOfAccount,
    onError: (error) => {
      console.error('Error adding account', error);
      showErrorToast(error);
      if (!isNullOrEmpty(error.errors)) {
        for (const [key, value] of Object.entries(error.errors)) {
          value.map((err) => showToast(err, 'error'));
        }
      }
    },
  });

  const editChartOfAccountMutation = useMutation({
    mutationFn: (formData) => editChartOfAccount(selectedAccount, formData),
    onSuccess: () => {
      showToast('Account Updated!', 'success');
      queryClient.invalidateQueries(['chartOfAccountDetails', selectedAccount]);
      queryClient.invalidateQueries(['chartOfAccount']);
      setRightSectionType('view');
    },
    onError: (error) => {
      console.error('Error updating account', error);
      showErrorToast(error);
    },
  });

  const { deleteMutation } = useDataMutations({
    onDeleteSuccessCallback: () => {
      closeModal();
      showToast('Account Deleted Successfully', 'success');
      queryClient.invalidateQueries(['chartOfAccount']);
      setRightSectionType('');
      setSelectedAccount(null);
    },
    onDeleteErrorCallback: (error) => {
      if (
        error.message.toLowerCase() ==
        'the chart of account cannot be deleted as it is currently in use.'
      ) {
        showModal('Cannot be Deleted', error.message, null, 'error');
      } else {
        showErrorToast(error);
      }
    },
  });

  //add new
  const handleAccountSubmit = (values, resetForm) => {
    rightSectionType === 'new'
      ? addChartOfAccountMutation.mutate(values, {
          onSuccess: (data) => {
            setSelectedAccount(data?.detail?.id);
            setRightSectionType('view');
            showToast('Account Added!', 'success');
            queryClient.invalidateQueries(['chartOfAccount']);
            queryClient.invalidateQueries([
              'chartOfAccountDetails',
              selectedAccount,
            ]);

            resetForm({
              values: {
                account_name: '',
                account_type: 1,
                description: '',
                is_sub_account: 0,
                parent_account_id: null,
              },
            });
          },
        })
      : editChartOfAccountMutation.mutate(values);
  };

  // Function to handle Delete action
  const handleDelete = (item) => {
    showModal(
      'Delete',
      `Are you sure you want to delete Account ${item?.account_name}?`,
      () => {
        deleteMutation.mutate({
          serviceFunction: deleteChartOfAccount,
          id: item.id,
        });
      }
    );
  };

  if (isLoading || isLoadingAccountTypes) {
    return (
      <>
        {topSection()}
        <div className="d-card p-0">
          <div className="d-flex flex-column flex-lg-row">
            <div className="coa-box">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton
                  key={i}
                  style={{ marginBottom: 12 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={46}
                />
              ))}
            </div>
          </div>
        </div>
      </>
    );
  }
  if (isError) {
    console.error(error);
    return (
      <>
        {topSection()}
        <div className="d-card p-0">{error}</div>;
      </>
    );
  }
  const rightSection = (type = '') => {
    switch (type) {
      case 'new':
        return (
          <>
            <h2 className="screen-title">New Account</h2>
            <Formik
              initialValues={{
                account_type: '',
                account_name: '',
                parent_account_id: '',
                is_sub_account: 0 || '',
                description: '',
              }}
              validationSchema={addCOAValidationSchema}
              onSubmit={(values, { resetForm }) => {
                handleAccountSubmit(values, resetForm);
              }}
            >
              {({
                values,
                touched,
                errors,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-lg-6 mb-3">
                      <SearchableSelect
                        label={'Account Type'}
                        name="account_type"
                        required
                        options={
                          isErrorAccountTypes
                            ? [
                                {
                                  label: 'Unable to fetch Account Types',
                                  value: null,
                                  isDisabled: true,
                                },
                              ]
                            : convertCOAAccountsToDropdownOptions(
                                accountTypesData
                              )
                        }
                        onChange={(v) => {
                          setFieldValue('account_type', v.value);
                          setSelectedAccountType(v.value);
                          setFieldValue('is_sub_account', 0);
                        }}
                        value={values.account_type}
                        placeholder={'Select Account Type Type'}
                      />
                      <ErrorMessage
                        name="account_type"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    <div className="col-12 col-lg-6 mb-3">
                      <CustomInput
                        name={'account_name'}
                        label={'Account Name'}
                        type={'text'}
                        required
                        value={values.account_name}
                        placeholder={'Enter Account Name'}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.account_name && errors.account_name}
                      />
                    </div>
                    <div className="col-12 mb-4">
                      <div className="checkbox-wrapper">
                        <label className="checkbox-container">
                          <input
                            onChange={(v) =>
                              setFieldValue(
                                'is_sub_account',
                                v.target.checked ? 1 : 0
                              )
                            }
                            type="checkbox"
                            name="is_sub_account"
                            checked={values.is_sub_account}
                          />
                          <span className="custom-checkbox"></span>
                          Make this a sub-account
                        </label>
                      </div>
                    </div>
                    {values.is_sub_account ? (
                      <div className="col-12 mb-4">
                        <SearchableSelect
                          label={'Parent Account*'}
                          name="parent_account_id"
                          options={
                            !selectedAccountType
                              ? [
                                  {
                                    label: 'Select Account Type',
                                    value: null,
                                    isDisabled: true,
                                  },
                                ]
                              : isLoadingParentAccountTypes
                              ? [
                                  {
                                    label: 'Loading...',
                                    value: null,
                                    isDisabled: true,
                                  },
                                ]
                              : convertCOAAccountsToDropdownOptions(
                                  parentAccountTypesData
                                )
                          }
                          onChange={(v) => {
                            setFieldValue('parent_account_id', v.value);
                          }}
                          value={values.parent_account_id}
                          placeholder={'Select Parent Account'}
                        />
                        <ErrorMessage
                          name="parent_account_id"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                    ) : null}
                    <div className="col-12">
                      <CustomInput
                        name={'description'}
                        label={'Description'}
                        type={'textarea'}
                        rows={1}
                        required
                        placeholder={'Enter Description'}
                        value={values.description}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.description && errors.description}
                      />
                    </div>
                  </div>
                  <div className="d-flex gap-3">
                    <CustomButton
                      text={'Save'}
                      type={'submit'}
                      disabled={addChartOfAccountMutation.isPending}
                      loading={addChartOfAccountMutation.isPending}
                    />
                    <CustomButton
                      text={'Cancel'}
                      variant={'secondaryButton'}
                      type={'button'}
                      disabled={addChartOfAccountMutation.isPending}
                      onClick={() =>
                        setRightSectionType(
                          !isNullOrEmpty(chartOfAccountDetails) ? 'view' : ''
                        )
                      }
                    />
                  </div>
                </Form>
              )}
            </Formik>
          </>
        );
      case 'edit':
        if (isLoadingAccountDetails) {
          return (
            <>
              <div className="d-flex justify-content-between">
                <h2 className="screen-title mb-0">Loading...</h2>
              </div>
            </>
          );
        } else if (isErrorAccountDetails) {
          return (
            <>
              <div className="d-flex justify-content-between">
                <p>Unable to fetch details</p>
              </div>
            </>
          );
        }
        return (
          <>
            <h2 className="screen-title">Edit Account</h2>
            <Formik
              initialValues={{
                parent_account_id:
                  chartOfAccountDetails?.parent_account?.id || '',
                account_type: chartOfAccountDetails?.account_type_id || '',
                account_name: chartOfAccountDetails?.account_name || '',
                description: chartOfAccountDetails?.description || '',
                is_sub_account: !isNullOrEmpty(
                  chartOfAccountDetails?.parent_account
                )
                  ? chartOfAccountDetails?.level > 3
                    ? 1
                    : 0
                  : 0,
              }}
              // validationSchema={addCOAValidationSchema}
              onSubmit={handleAccountSubmit}
              enableReinitialize
            >
              {({
                values,
                touched,
                errors,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-lg-6 mb-3">
                      <SearchableSelect
                        label={'Account Type'}
                        name="account_type"
                        required
                        options={
                          isErrorAccountTypes
                            ? [
                                {
                                  label: 'Unable to fetch Account Types',
                                  value: null,
                                  isDisabled: true,
                                },
                              ]
                            : convertCOAAccountsToDropdownOptions(
                                accountTypesData
                              )
                        }
                        onChange={(v) => {
                          setFieldValue('account_type', v.value);
                          setSelectedAccountType(v.value);
                        }}
                        value={values.account_type}
                        placeholder={'Select Account Type'}
                      />

                      <ErrorMessage
                        name="account_type"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    <div className="col-12 col-lg-6 mb-3">
                      <CustomInput
                        name={'account_name'}
                        label={'Account Name'}
                        type={'text'}
                        required
                        value={values.account_name}
                        placeholder={'Enter Account Name'}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.account_name && errors.account_name}
                      />
                    </div>
                    {chartOfAccountDetails?.level > 3 ? (
                      <div className="col-12 mb-4">
                        <div className="checkbox-wrapper">
                          <label className="checkbox-container">
                            <input
                              onChange={(v) =>
                                setFieldValue(
                                  'is_sub_account',
                                  v.target.checked ? 1 : 0
                                )
                              }
                              type="checkbox"
                              name="is_sub_account"
                              checked={values?.is_sub_account}
                              value={values?.is_sub_account}
                            />
                            <span className="custom-checkbox"></span>
                            Make this a sub-account
                          </label>
                        </div>
                      </div>
                    ) : null}
                    {values?.is_sub_account &&
                    chartOfAccountDetails?.level > 3 ? (
                      <div className="col-12 mb-4">
                        <SearchableSelect
                          label={'Parent Account*'}
                          name="parent_account_id"
                          options={
                            !selectedAccountType
                              ? [
                                  {
                                    label: 'Select Account Type',
                                    value: null,
                                    isDisabled: true,
                                  },
                                ]
                              : isLoadingParentAccountTypes
                              ? [
                                  {
                                    label: 'Loading...',
                                    value: null,
                                    isDisabled: true,
                                  },
                                ]
                              : convertCOAAccountsToDropdownOptions(
                                  parentAccountTypesData
                                )
                          }
                          onChange={(v) => {
                            setFieldValue('parent_account_id', v.value);
                          }}
                          value={values.parent_account_id}
                          placeholder={'Select Parent Account'}
                        />
                        <ErrorMessage
                          name="parent_account_id"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                    ) : null}
                    <div className="col-12">
                      <CustomInput
                        name={'description'}
                        label={'Description'}
                        type={'textarea'}
                        rows={1}
                        required
                        placeholder={'Enter Description'}
                        value={values.description}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.description && errors.description}
                      />
                    </div>
                  </div>
                  <div className="d-flex gap-3">
                    <CustomButton
                      text={'Update'}
                      type={'submit'}
                      disabled={editChartOfAccountMutation.isPending}
                      loading={editChartOfAccountMutation.isPending}
                    />
                    <CustomButton
                      text={'Cancel'}
                      variant={'secondaryButton'}
                      type={'button'}
                      disabled={editChartOfAccountMutation.isPending}
                      onClick={() => setRightSectionType('view')}
                    />
                  </div>
                </Form>
              )}
            </Formik>
          </>
        );
      case 'view':
        if (isLoadingAccountDetails) {
          return (
            <>
              <div className="d-flex justify-content-between">
                <h2 className="screen-title mb-0">Loading...</h2>
              </div>
            </>
          );
        } else if (isErrorAccountDetails) {
          return (
            <>
              <div className="d-flex justify-content-between">
                <p>Unable to fetch details</p>
              </div>
            </>
          );
        } else if (isNullOrEmpty(chartOfAccountDetails)) return null;
        return (
          <div className="d-flex flex-column flex-grow-1 justify-content-start">
            <div>
              <div className="d-flex justify-content-between align-items-center mb-4">
                <h2 className="screen-title mb-0">
                  {chartOfAccountDetails?.level === 5
                    ? 'Sub-Subsidiary Account'
                    : chartOfAccountDetails?.level === 4
                    ? chartOfAccountDetails?.status === 'active'
                      ? 'Unlocked Subsidiary Account'
                      : 'Locked Subsidiary Account'
                    : chartOfAccountDetails?.level === 3
                    ? chartOfAccountDetails?.status === 'active'
                      ? 'Unlocked Controlling Account'
                      : 'Locked Controlling Account'
                    : ''}
                </h2>
                {chartOfAccountDetails?.level === 5 ||
                (chartOfAccountDetails?.level > 2 &&
                  chartOfAccountDetails?.status === 'active') ? (
                  <CustomButton
                    text={'Edit'}
                    onClick={() => setRightSectionType('edit')}
                  />
                ) : null}
              </div>
              <div className="row">
                {[
                  {
                    label: 'Account Type',
                    value: chartOfAccountDetails?.account_type,
                  },
                  {
                    label: 'Account Name',
                    value: chartOfAccountDetails?.account_name,
                  },
                ].map((x, i) => {
                  if (isNullOrEmpty(x.value)) return null;
                  return (
                    <div key={i} className="col-12 col-sm-6 mb-4">
                      <p className="detail-title detail-label-color mb-1">
                        {x.label}
                      </p>
                      <p className="detail-text wrapText mb-0">{x.value}</p>
                    </div>
                  );
                })}
                {[
                  ...(chartOfAccountDetails?.level > 3 &&
                  !isNullOrEmpty(chartOfAccountDetails?.parent_account) // Conditionally show parent account if it has one
                    ? [
                        {
                          label: 'Parent Account',
                          value:
                            chartOfAccountDetails?.parent_account?.account_name,
                        },
                      ]
                    : []),
                  {
                    label: 'Description',
                    value: chartOfAccountDetails?.description,
                  },
                ].map((x, i) => {
                  if (isNullOrEmpty(x.value)) return null;
                  return (
                    <div key={i} className="col-12 mb-4">
                      <p className="detail-title detail-label-color mb-1">
                        {x.label}
                      </p>
                      <p className="detail-text wrapText mb-0">{x.value}</p>
                    </div>
                  );
                })}
                {chartOfAccountDetails?.level === 5 ||
                (chartOfAccountDetails?.level > 2 &&
                  chartOfAccountDetails?.status === 'active') ? (
                  <div className="row mb-4">
                    <div className="col-12 d-flex">
                      <CustomButton
                        text={'Delete'}
                        variant={'danger'}
                        onClick={() => handleDelete(chartOfAccountDetails)}
                      />
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
            <div>
              {chartOfAccountDetails?.created_at && (
                <p className="detail-title detail-label-color mb-1">
                  Created on{' '}
                  {formatDate(
                    chartOfAccountDetails?.created_at,
                    'DD/MM/YYYY - HH:MM:SS'
                  )}{' '}
                  by {chartOfAccountDetails?.creator?.user_name}
                  User
                </p>
              )}
              {!isNullOrEmpty(chartOfAccountDetails?.editor) && (
                <p className="detail-title detail-label-color mb-0">
                  Last Edited on{' '}
                  {formatDate(
                    chartOfAccountDetails?.updated_at,
                    'DD/MM/YYYY - HH:MM:SS'
                  )}{' '}
                  by {chartOfAccountDetails?.editor?.user_name}
                </p>
              )}
            </div>
          </div>
        );
      default:
        break;
    }
  };
  const handleClick = (item, clickType) => {
    if (clickType === 'view') {
      setRightSectionType('view');
    } else if (clickType === 'edit') {
      setRightSectionType('edit');
    } else if (clickType === 'delete') {
      handleDelete(item);
    }

    setSelectedAccount(item.id);
  };

  return (
    <>
      {topSection()}

      <div className="d-card p-0">
        <div className="d-flex flex-column flex-lg-row align-items-stretch">
          <div className="coa-box">
            {chartOfAccounts && (
              <MultiLevelDropdown
                data={chartOfAccounts}
                handleAccountClick={handleClick}
              />
            )}
          </div>
          <div className="coa-box d-flex flex-column">
            {rightSection(rightSectionType)}
          </div>
        </div>
      </div>
    </>
  );
};

export default withModal(ChartOfAccount);
