import React from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import CustomInput from '../../../Components/CustomInput';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { serialNum } from '../../../Utils/Utils';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';

const SpecialCommissionRow = ({
  row,
  index,
  isDisabled,
  commissionAmount,
  updateField,
  handleDeleteRow,
  setShowAddLedgerModal,
  getAccountsByTypeOptions,
}) => {
  return (
    <tr>
      <td>{serialNum(index + 1)}</td>
      <td>
        <SearchableSelect
          options={[
            { label: 'PL', value: 'party' },
            { label: 'GL', value: 'general' },
            { label: 'WIC', value: 'walkin' },
          ]}
          isDisabled={isDisabled}
          placeholder="Ledger"
          value={row.ledger}
          onChange={(selected) => {
            updateField(row.id, 'ledger', selected.value);
          }}
          borderRadius={10}
        />
      </td>
      <td>
        <SearchableSelect
          options={getAccountsByTypeOptions(row.ledger)}
          isDisabled={isDisabled}
          placeholder="Account"
          value={row.credit_account}
          onChange={(selected) => {
            if (selected.label?.toLowerCase()?.startsWith('add new')) {
              setShowAddLedgerModal(selected.label?.toLowerCase());
            } else {
              updateField(row.id, 'credit_account', selected.value);
            }
          }}
          borderRadius={10}
          minWidth={240}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.narration}
          disabled={isDisabled}
          placeholder="Enter Narration"
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
          style={{ minWidth: 300 }}
        />
      </td>
      <td>
        <CustomInput
          type={'number'}
          value={row.percentage}
          disabled={isDisabled}
          onChange={(e) => {
            updateField(row.id, 'percentage', e.target.value, true);
            updateField(
              row.id,
              'amount',
              (e.target.value * parseFloat(commissionAmount)) / 100
            );
          }}
          min={0}
          borderRadius={10}
          style={{ maxWidth: 100 }}
        />
      </td>

      <td>
        <CustomInput
          type={'text'}
          value={row.amount}
          disabled={true}
          placeholder="Amount"
          onChange={(e) => updateField(row.id, 'amount', e.target.value)}
          borderRadius={10}
          style={{ maxWidth: 135 }}
          readOnly
        />
      </td>

      <td>
        <TableActionDropDown
          actions={[
            {
              name: 'Delete',
              icon: HiOutlineTrash,
              onClick: () => handleDeleteRow(row.id),
              className: 'delete',
              disabled: isDisabled,
            },
          ]}
        />
      </td>
    </tr>
  );
};

export default SpecialCommissionRow;
