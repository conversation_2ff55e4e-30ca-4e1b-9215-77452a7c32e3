import React, { useState } from 'react';
import { FaChevronLeft, FaChevronRight, FaPaperclip } from 'react-icons/fa';
import {
  FaMagnifyingGlass
} from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import withModal from '../../../HOC/withModal';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { MOCK_CURRENT_ACCOUNT, MOCK_EXCHANGE_RATES, MOCK_FCD_TABLE_DATA, MOCK_SAVINGS_ACCOUNT, supportLogsData } from '../../../Mocks/MockData';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { foreignCurrencyDealValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
// Add this mock data at the top of the file, after imports

// Add this mock data after the existing mock data constants
const MOCK_A2A_SEARCH_RESULT = {
  id: '05',
  debitLedger: {
    type: 'Part',
    account: 'Account Abc'
  },
  creditLedger: {
    type: 'Party',
    account: 'Account Abc'
  },
  buyFCyDr: {
    currency: 'DHS',
    amount: '300.00'
  },
  buyFCyDrAmount: {
    currency: 'DHS',
    amount: '300.00'
  },
  sellFCCr: {
    currency: 'USD',
    amount: '900.00'
  },
  rateType: '3.*********',
  commissionType: 'Commission Income',
  commission: {
    currency: 'DHS',
    amount: '300.00'
  },
  narration: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo',
  comment: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo',
  receivableComm: '1.0000% Receivable Comm of DHS 100.00 on DHS 10,000.00'
};

// Update the table headers
const a2aTableHeaders = [
  'Date',
  'CBS No.',
  'Debit Ledger',
  'Debit Account',
  'Credit Ledger',
  'Credit Account',
  'Buy FCy',
  'Buy FC Amount',
  'Rate',
  'Sell FCy',
  'Sell FC Amount',
  'Commission FCy',
  'Commission Amount',
  'User ID',
  'Time',
  <FaPaperclip />  // For attachment column
];

// Update the mock table data

// Add validation schema


const ForeignCurrencyDeal = ({ showModal, closeModal }) => {
  const navigate = useNavigate();
  usePageTitle('Account to Account');
  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [date, setDate] = useState('');
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] = useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);
  const [showSearchTable, setShowSearchTable] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [debitLedger, setDebitLedger] = useState('');
  const [debitAccount, setDebitAccount] = useState('');
  const [creditAccount, setCreditAccount] = useState('');
  const [dealType, setDealType] = useState('single');
  const [multiDealEntries, setMultiDealEntries] = useState([]);


  // Add this check to determine if we should show the search result
  const isSearchResult = searchTerm === '05';

  // Single initialValues that updates based on search result
  const initialValues = isSearchResult ? {
    debitLedger: MOCK_A2A_SEARCH_RESULT.debitLedger.type.toLowerCase(),
    debitAccount: MOCK_A2A_SEARCH_RESULT.debitLedger.account,
    creditLedger: MOCK_A2A_SEARCH_RESULT.creditLedger.type.toLowerCase(),
    creditAccount: MOCK_A2A_SEARCH_RESULT.creditLedger.account,
    buyFCyDr: MOCK_A2A_SEARCH_RESULT.buyFCyDr.currency.toLowerCase(),
    buyFCyDrAmount: MOCK_A2A_SEARCH_RESULT.buyFCyDrAmount.amount,
    rateType: MOCK_A2A_SEARCH_RESULT.rateType,
    sellFCDr: MOCK_A2A_SEARCH_RESULT.sellFCCr.amount,
    commissionType: MOCK_A2A_SEARCH_RESULT.commissionType,
    commission: MOCK_A2A_SEARCH_RESULT.commission.amount,
    narration: MOCK_A2A_SEARCH_RESULT.narration,
    comment: MOCK_A2A_SEARCH_RESULT.comment
  } : {
    date: '',
    debitLedger: '',
    debitAccount: '',
    creditLedger: '',
    creditAccount: '',
    buyFCyDr: '',
    buyFCyDrAmount: '',
    buyFCyCr: '',
    buyFCyCrAmount: '',
    rateType: '',
    sellFCDr: '',
    commissionType: '',
    commission: '',
    narration: '',
    comment: ''
  };

  // Update handleEdit function
  const handleEdit = () => {
    setIsDisabled(false);
    setShowForm(true);
  };

  // Update handleCancel function
  const handleCancel = () => {
    setIsDisabled(true);
    setShowForm(false);
    setDate('');
  };

  // Update handleNewClick
  const handleNewClick = () => {
    setIsDisabled(false);
    setDate('');
  };

  const handleAccountChange = (selected, type, setFieldValue) => {
    if (selected.value === 'add_new_pl') {
      setShowAddLedgerModal('add new pl');
    } else if (selected.value === 'add_new_gl') {
      setShowAddLedgerModal('add new gl');
    } else if (selected.value === 'add_new_wic') {
      setShowAddLedgerModal('add new wic');
    } else {
      setFieldValue(type === 'debit' ? 'debitAccount' : 'creditAccount', selected.value);
    }
  };

  const handleDelete = (item) => {
    showModal(
      'Delete',
      `Are you sure you want to delete CBS Number: 05?`,
      () => {
        console.log(item);
        // Close the first modal before showing the second one
        closeModal();
        // Show success modal after a small delay to ensure smooth transition
        setTimeout(() => {
          showModal(
            'Deleted',
            `CBS Number: 05 deleted successfully`,
            false,
            'success'
          );
        }, 100);
      }
    );
  };

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newAccount) => {
              if (showAddLedgerModal === 'add new pl') {
                setDebitAccount(newAccount);
              } else if (showAddLedgerModal === 'add new gl') {
                setDebitLedger(newAccount);
              } else if (showAddLedgerModal === 'add new wic') {
                setCreditAccount(newAccount);
              }
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newAccount) => {
              setDebitLedger(newAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newAccount) => {
              setCreditAccount(newAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        return null;
    }
  };

  // Update getAccountOptions function
  const getAccountOptions = (ledgerType) => {
    switch (ledgerType) {
      case 'pl':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add PL Ledger', value: 'add_new_pl' }
        ];
      case 'gl':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add GL Ledger', value: 'add_new_gl' }
        ];
      case 'wic':
        return [
          { label: 'Account 1', value: 'account_1' },
          { label: 'Add WIC Ledger', value: 'add_new_wic' }
        ];
      default:
        return [];
    }
  };

  const renderSearchResult = () => {
    if (!isSearchResult) return null;

    return (
      <div style={{ maxWidth: 780 }}>
        <div className="row">
          <div className="col-md-6">
            <div className="mb-4">
              <label className="text-muted mb-2">Debit Ledger</label>
              <div className="d-flex gap-2">
                <div>{MOCK_A2A_SEARCH_RESULT.debitLedger.type}</div>
                <div>{MOCK_A2A_SEARCH_RESULT.debitLedger.account}</div>
              </div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Buy FCy (Dr)</label>
              <div>{MOCK_A2A_SEARCH_RESULT.buyFCyDr.currency}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Buy FCy (Dr)</label>
              <div className="d-flex gap-2">
                <div>{MOCK_A2A_SEARCH_RESULT.buyFCyDrAmount.currency}</div>
                <div>{MOCK_A2A_SEARCH_RESULT.buyFCyDrAmount.amount}</div>
              </div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Sell FC (Cr)</label>
              <div className="d-flex gap-2">
                <div>{MOCK_A2A_SEARCH_RESULT.sellFCCr.currency}</div>
                <div>{MOCK_A2A_SEARCH_RESULT.sellFCCr.amount}</div>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="mb-4">
              <label className="text-muted mb-2">Credit Ledger</label>
              <div className="d-flex gap-2">
                <div>{MOCK_A2A_SEARCH_RESULT.creditLedger.type}</div>
                <div>{MOCK_A2A_SEARCH_RESULT.creditLedger.account}</div>
              </div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Rate Type</label>
              <div>{MOCK_A2A_SEARCH_RESULT.rateType}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Commission Type</label>
              <div>{MOCK_A2A_SEARCH_RESULT.commissionType}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Commission</label>
              <div className="d-flex gap-2">
                <div>{MOCK_A2A_SEARCH_RESULT.commission.currency}</div>
                <div>{MOCK_A2A_SEARCH_RESULT.commission.amount}</div>
              </div>
            </div>
          </div>

          <div className="col-12">
            <div className="mb-4">
              <label className="text-muted mb-2">Narration</label>
              <div>{MOCK_A2A_SEARCH_RESULT.narration}</div>
            </div>

            <div className="mb-4">
              <label className="text-muted mb-2">Comment</label>
              <div>{MOCK_A2A_SEARCH_RESULT.comment}</div>
            </div>

            <div className="mb-4">
              <div>{MOCK_A2A_SEARCH_RESULT.receivableComm}</div>
            </div>
          </div>
        </div>

        <div className="mt-3">
          <div className="d-flex gap-3">
            <CustomButton text="Edit" onClick={handleEdit} />
            <CustomButton text="Delete" onClick={handleDelete} className={'secondaryButton'} />
            <CustomButton text="Print" className={'secondaryButton'} />
            <div className="ms-auto d-flex gap-2">
              <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
              <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
              <FaPaperclip size={20} style={{ cursor: 'pointer' }} onClick={() => setUploadAttachmentsModal(true)} />
            </div>
          </div>
          <div className="mt-3">
            <div>Last A2A Number: {MOCK_A2A_SEARCH_RESULT.id}</div>
          </div>
        </div>
      </div>
    );
  };

  // Update the table rendering to include all columns
  const renderSearchTable = () => {
    return (
      <CustomTable
        hasFilters={false}
        setFilters={false}
        headers={a2aTableHeaders}
        pagination={{
          total: MOCK_FCD_TABLE_DATA.length,
          per_page: 10,
          current_page: 1
        }}
        isLoading={false}
        sortKey={false}
        sortOrder={false}
        handleSort={false}
        isPaginated={false}
      >
        <tbody>
          {MOCK_FCD_TABLE_DATA.map((row) => (
            <tr key={row.id} style={{ cursor: 'pointer' }} onClick={() => setSearchTerm(row.id)}>
              <td>{row.date}</td>
              <td>{row.id}</td>
              <td>{row.debitLedger}</td>
              <td>{row.debitAccount}</td>
              <td>{row.creditLedger}</td>
              <td>{row.creditAccount}</td>
              <td>{row.buyFCy}</td>
              <td>{row.buyFCAmount}</td>
              <td>{row.rate}</td>
              <td>{row.sellFCy}</td>
              <td>{row.sellFCAmount}</td>
              <td>{row.commissionFCy}</td>
              <td>{row.commissionAmount}</td>
              <td>{row.userId}</td>
              <td>{row.time}</td>
              <td>{row.hasAttachment}</td>
            </tr>
          ))}
        </tbody>
      </CustomTable>
    );
  };

  // Add this function to handle saving multi-deal entries
  const handleSaveEntry = (values) => {
    const newEntry = {
      id: String(multiDealEntries.length + 1).padStart(2, '0'),
      date: new Date().toLocaleDateString(),
      debitLedger: values.debitLedger,
      debitAccount: values.debitAccount,
      creditLedger: values.creditLedger,
      creditAccount: values.creditAccount,
      buyFCy: values.buyFCyDr,
      buyFCAmount: values.buyFCyDrAmount,
      rate: values.rateType,
      sellFCy: values.buyFCyCr,
      sellFCAmount: values.buyFCyCrAmount,
      commissionFCy: values.commissionType,
      commissionAmount: values.commission,
      userId: 'ABC',
      time: new Date().toLocaleTimeString(),
      hasAttachment: 'No'
    };

    setMultiDealEntries([...multiDealEntries, newEntry]);

    // Reset form after saving entry
    // You may want to reset only specific fields while keeping others
    // This can be handled through the Formik resetForm function
  };

  // Update handleSave to handle both single and multi deals
  const handleSave = (values) => {
    if (dealType === 'multi') {
      handleSaveEntry(values);
    } else {
      console.log('Form values:', values);
      // Proceed with single deal save logic
    }
  };

  const handleLedgerChange = (selected) => {
    setDebitLedger(selected.value);
  };

  // Add this handler function
  const handleDealTypeChange = (selected) => {
    setDealType(selected.value);
  };

  // Update the form section of the render method
  const renderForm = () => (
    <Formik
      initialValues={initialValues}
      validationSchema={foreignCurrencyDealValidationSchema}
      onSubmit={handleSave}
      enableReinitialize={true}
    >
      {({ values, errors, touched, handleChange, handleBlur, setFieldValue }) => (
        <Form>
          <div className="d-flex gap-3 flex-xxl-nowrap flex-wrap mb-3">
            <div className="flex-grow-1">
              <label>Debit Ledger</label>
              <div className="d-flex gap-3">
                <div className="flex-grow-1">
                  <SearchableSelect
                    options={[
                      { label: 'PL', value: 'pl' },
                      { label: 'GL', value: 'gl' },
                      { label: 'WIC', value: 'wic' }
                    ]}
                    value={values.debitLedger}
                    onChange={(selected) => setFieldValue('debitLedger', selected.value)}
                    placeholder="Ledger"
                    isDisabled={isDisabled}
                    minWidth={0}
                    error={touched.debitLedger && errors.debitLedger}
                  />
                </div>
                <div className="flex-grow-1">
                  <SearchableSelect
                    options={getAccountOptions(values.debitLedger)}
                    value={values.debitAccount}
                    onChange={(selected) => handleAccountChange(selected, 'debit', setFieldValue)}
                    placeholder="Select Account"
                    isDisabled={isDisabled || !values.debitLedger}
                    minWidth={0}
                    error={touched.debitAccount && errors.debitAccount}
                  />
                </div>
              </div>
            </div>

            <div className="flex-grow-1">
              <label>Credit Ledger</label>
              <div className="d-flex gap-3">
                <div className="flex-grow-1">
                  <SearchableSelect
                    options={[
                      { label: 'PL', value: 'pl' },
                      { label: 'GL', value: 'gl' },
                      { label: 'WIC', value: 'wic' }
                    ]}
                    value={values.creditLedger}
                    onChange={(selected) => setFieldValue('creditLedger', selected.value)}
                    placeholder="Ledger"
                    isDisabled={isDisabled}
                    minWidth={0}
                    error={touched.creditLedger && errors.creditLedger}
                  />
                </div>
                <div className="flex-grow-1">
                  <SearchableSelect
                    options={getAccountOptions(values.creditLedger)}
                    value={values.creditAccount}
                    onChange={(selected) => handleAccountChange(selected, 'credit', setFieldValue)}
                    placeholder="Select Account"
                    isDisabled={isDisabled || !values.creditLedger}
                    minWidth={0}
                    error={touched.creditAccount && errors.creditAccount}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="d-flex gap-3 flex-wrap mb-3">
            <div className="flex-grow-1">
              <label>Buy FCy (Dr)</label>
              <div className="d-flex gap-3">
                <div className="flex-grow-1">
                  <SearchableSelect
                    name="buyFCyDr"
                    options={[
                      { label: 'DHS', value: 'DHS' },
                      { label: 'BTC', value: 'BTC' },
                      { label: 'CAD', value: 'CAD' },
                      { label: 'ETH', value: 'ETH' },
                      { label: 'EUR', value: 'EUR' },
                      { label: 'GBP', value: 'GBP' },
                      { label: 'PKR', value: 'PKR' },
                      { label: 'INR', value: 'INR' },
                    ]}
                    value={values.buyFCyDr}
                    onChange={(selected) => {
                      if (selected.label?.toLowerCase()?.startsWith('eth')) {
                        setShowMissingCurrencyRateModal(true);
                      } else {
                        setFieldValue('buyFCyDr', selected.value);
                      }
                    }}
                    // value={values.buyFCyDr}
                    // onChange={(selected) => setFieldValue('buyFCyDr', selected.value)}
                    placeholder="Select Buy FCy"
                    isDisabled={isDisabled}
                    error={touched.buyFCyDr && errors.buyFCyDr}
                  />
                </div>
                <div className="flex-grow-1">
                  <CustomInput
                    type="text"
                    name="buyFCyDrAmount"
                    value={values.buyFCyDrAmount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Enter Amount"
                    disabled={isDisabled}
                    error={touched.buyFCyDrAmount && errors.buyFCyDrAmount}
                  />
                </div>
              </div>
            </div>

            <div className="flex-grow-1">
              <label>Buy FCy (Cr)</label>
              <div className="d-flex gap-3">
                <div className="flex-grow-1">
                  <SearchableSelect
                    name="buyFCyCr"
                    options={[
                      { label: 'DHS', value: 'DHS' },
                      { label: 'BTC', value: 'BTC' },
                      { label: 'CAD', value: 'CAD' },
                      { label: 'ETH', value: 'ETH' },
                      { label: 'EUR', value: 'EUR' },
                      { label: 'GBP', value: 'GBP' },
                      { label: 'PKR', value: 'PKR' },
                      { label: 'INR', value: 'INR' },
                    ]}
                    value={values.buyFCyCr}
                    onChange={(selected) => {
                      if (selected.label?.toLowerCase()?.startsWith('eth')) {
                        setShowMissingCurrencyRateModal(true);
                      } else {
                        setFieldValue('buyFCyCr', selected.value);
                      }
                    }}
                    placeholder="Select Buy FCy"
                    isDisabled={isDisabled}
                    error={touched.buyFCyCr && errors.buyFCyCr}
                  />
                </div>
                <div className="flex-grow-1">
                  <CustomInput
                    type="text"
                    name="buyFCyCrAmount"
                    value={values.buyFCyCrAmount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Enter Amount"
                    disabled={isDisabled}
                    error={touched.buyFCyCrAmount && errors.buyFCyCrAmount}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="d-flex gap-3 mb-3">
            <div className="flex-grow-1">
              <label>Rate Type</label>
              <SearchableSelect
                name="rateType"
                options={[
                  { label: 'Type 1', value: 'type1' },
                  { label: 'Type 2', value: 'type2' }
                ]}
                value={values.rateType}
                onChange={(selected) => setFieldValue('rateType', selected.value)}
                placeholder="Select Rate Type"
                isDisabled={isDisabled}
                minWidth={0}
                error={touched.rateType && errors.rateType}
              />
            </div>

            <div className="flex-grow-1">
              <label>Sell FC (Cr)</label>
              <SearchableSelect
                name="sellFCCr"
                options={[
                  { label: 'Option 1', value: 'opt1' },
                  { label: 'Option 2', value: 'opt2' }
                ]}
                value={values.sellFCCr}
                onChange={(selected) => setFieldValue('sellFCCr', selected.value)}
                placeholder="Select Sell FC"
                isDisabled={isDisabled}
                minWidth={0}
                error={touched.sellFCCr && errors.sellFCCr}
              />
            </div>
          </div>

          <div className="d-flex gap-3 mb-3">
            <div className="flex-grow-1">
              <label>Sell FC (Dr)</label>
              <CustomInput
                type="text"
                name="sellFCDr"
                value={values.sellFCDr}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter Sell FC"
                disabled={isDisabled}
                error={touched.sellFCDr && errors.sellFCDr}
              />
            </div>
          </div>

          <div className="d-flex gap-3 mb-3">
            <div className="flex-grow-1">
              <label>Commission Type</label>
              <SearchableSelect
                name="commissionType"
                options={[
                  { label: 'Type 1', value: 'type1' },
                  { label: 'Type 2', value: 'type2' }
                ]}
                value={values.commissionType}
                onChange={(selected) => setFieldValue('commissionType', selected.value)}
                placeholder="Select Commission Type"
                isDisabled={isDisabled}
                error={touched.commissionType && errors.commissionType}
              />
            </div>

            <div className="flex-grow-1">
              <label>Commission</label>
              <CustomInput
                type="text"
                name="commission"
                value={values.commission}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter Commission"
                disabled={isDisabled}
                error={touched.commission && errors.commission}
              />
            </div>
          </div>

          <div className="mb-3">
            <label>Narration</label>
            <CustomInput
              type="textarea"
              name="narration"
              value={values.narration}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Enter Narration"
              rows={4}
              disabled={isDisabled}
              error={touched.narration && errors.narration}
            />
          </div>

          <div className="mb-3">
            <label>Comment</label>
            <CustomInput
              type="textarea"
              name="comment"
              value={values.comment}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Enter Comment"
              rows={4}
              disabled={isDisabled}
              error={touched.comment && errors.comment}
            />
          </div>

          <div className="d-flex gap-3 align-items-center mb-3">
            <CustomButton
              text="Add Special Commission"
              variant="secondary"
              disabled={isDisabled}
              onClick={() => navigate('/transactions/special-comission')}
            />
            {!isDisabled && dealType === 'multi' && (
              <CustomButton
                text="Save Entry"
                type="submit"
              />
            )}
          </div>

          {/* Multi-deal table */}
          {dealType === 'multi' && multiDealEntries.length > 0 && (
            <div className="mt-4">
              <CustomTable
                hasFilters={false}
                setFilters={false}
                headers={a2aTableHeaders}
                pagination={{
                  total: multiDealEntries.length,
                  per_page: 10,
                  current_page: 1
                }}
                isLoading={false}
                sortKey={false}
                sortOrder={false}
                handleSort={false}
                isPaginated={false}
              >
                <tbody>
                  {multiDealEntries.map((row) => (
                    <tr key={row.id}>
                      <td>{row.date}</td>
                      <td>{row.id}</td>
                      <td>{row.debitLedger}</td>
                      <td>{row.debitAccount}</td>
                      <td>{row.creditLedger}</td>
                      <td>{row.creditAccount}</td>
                      <td>{row.buyFCy}</td>
                      <td>{row.buyFCAmount}</td>
                      <td>{row.rate}</td>
                      <td>{row.sellFCy}</td>
                      <td>{row.sellFCAmount}</td>
                      <td>{row.commissionFCy}</td>
                      <td>{row.commissionAmount}</td>
                      <td>{row.userId}</td>
                      <td>{row.time}</td>
                      <td>{row.hasAttachment}</td>
                    </tr>
                  ))}
                </tbody>
              </CustomTable>
            </div>
          )}

          {/* Buttons below table */}
          <div className="d-flex gap-3 align-items-center mt-3">
            {!isDisabled && (
              <>
                {dealType === 'multi' ? (
                  <CustomButton
                    text="Save"
                    type="submit"
                  />
                ) : (
                  <CustomButton
                    text="Save"
                    type="submit"
                  />
                )}
                <CustomButton
                  text="Cancel"
                  className="secondaryButton"
                  onClick={handleCancel}
                  type="button"
                />
              </>
            )}
            <div className="ms-auto d-flex gap-2">
              <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
              <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
              <FaPaperclip
                size={20}
                style={{ cursor: 'pointer' }}
                onClick={() => setUploadAttachmentsModal(true)}
              />
            </div>
          </div>

          <div className="mt-3 d-inline-block">
            <CustomCheckbox label="Account Balance" style={{ border: 0 }} disabled={isDisabled} />
            <CustomCheckbox label="Print" disabled={isDisabled} style={{ border: 0 }} />
          </div>

          {!isDisabled && <div className="mt-2">Last CBS No: 05</div>}
        </Form>
      )}
    </Formik>
  );

  return (
    <>
      <section className="position-relative">
        <div className="d-flex gap-3 justify-content-between flex-wrap">
          <h2 className="screen-title mb-0">Foreign Currency Deal</h2>
          {isDisabled && <CustomButton text="New" onClick={handleNewClick} />}
        </div>

        <div className="d-flex justify-content-between align-items-end mt-3 gap-3">
          <CustomInput
            type="text"
            placeholder="Search A2A"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              if (e.target.value === '') {
                setShowSearchTable(false);
              } else if (e.target.value === '05') {
                setShowSearchTable(false); // Hide table when "05" is entered
              }
            }}
            rightIcon={FaMagnifyingGlass}
            onButtonClick={() => {
              if (searchTerm === '') {
                setShowSearchTable(!showSearchTable);
              }
            }}
          />
          <div className="d-flex align-items-center gap-3">
            <div className="mb-3">
              <SearchableSelect
                name="dealType"
                options={[
                  { label: 'New Single Deal', value: 'single' },
                  { label: 'New Multi Deal', value: 'multi' }
                ]}
                label={'New'}
                value={dealType}
                onChange={handleDealTypeChange}
                placeholder="New Single Deal"
                isDisabled={isDisabled}
              />
            </div>
            {!isSearchResult && (
              <CustomInput
                type="date"
                label="Date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                disabled={isDisabled}
              />
            )}
          </div>
        </div>

        <div className="d-card mt-3">
          {showSearchTable ? (
            renderSearchTable()
          ) : (
            <div className="d-flex justify-content-between gap-4 flex-wrap flex-xl-nowrap">
              {isSearchResult && !showForm ? (
                <div className="flex-grow-1">
                  {renderSearchResult()}
                </div>
              ) : (
                <div className="flex-grow-1" style={{ maxWidth: '780px' }}>
                  {renderForm()}
                </div>
              )}
              <div className='flex-shrink-0' style={{ width: '280px' }}>
                {/* Account Balance Cards */}
                <div>
                  {/* Current Account */}
                  <div>
                    <h6 className="mb-2">Account Balance</h6>
                    <div className="d-card mb-4 account-balance-card">
                      <div className="mb-3 account-name w-100">{MOCK_CURRENT_ACCOUNT.name}</div>
                      <table className="w-100">
                        <thead>
                          <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                            <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>FCy</th>
                            <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Balance</th>
                            <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}></th>
                          </tr>
                        </thead>
                        <tbody>
                          {MOCK_CURRENT_ACCOUNT.balances.map((balance, index) => (
                            <tr key={index}>
                              <td style={{ padding: '8px 0', color: balance.color, fontWeight: '500' }}>
                                {balance.currency}
                              </td>
                              <td style={{ padding: '8px 0' }}>{balance.amount}</td>
                              <td style={{ padding: '8px 0' }}>{balance.type}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Savings Account */}
                  <div>
                    <h6 className="mb-2">Account Balance</h6>
                    <div className="d-card mb-4 account-balance-card">
                      <div className="mb-3 account-name w-100">{MOCK_SAVINGS_ACCOUNT.name}</div>
                      <table className="w-100">
                        <thead>
                          <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                            <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>FCy</th>
                            <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Balance</th>
                            <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}></th>
                          </tr>
                        </thead>
                        <tbody>
                          {MOCK_SAVINGS_ACCOUNT.balances.map((balance, index) => (
                            <tr key={index}>
                              <td style={{ padding: '8px 0', color: balance.color, fontWeight: '500' }}>
                                {balance.currency}
                              </td>
                              <td style={{ padding: '8px 0' }}>{balance.amount}</td>
                              <td style={{ padding: '8px 0' }}>{balance.type}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                {/* Exchange Rates Card */}
                <h6 className="mb-2">Live Exchange Rates Against Base Currency</h6>
                <div className="d-card account-balance-card">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <div className="d-flex align-items-center account-name w-100">
                      <span className="me-2" style={{ color: '#6B7280' }}>Inverse</span>
                      <div className="form-check form-switch">
                        <input className="form-check-input" type="checkbox" style={{ cursor: 'pointer' }} />
                      </div>
                    </div>
                  </div>
                  <table className="w-100">
                    <thead>
                      <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                        <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>FCy</th>
                        <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Rates</th>
                        <th style={{ padding: '8px 0', color: '#6B7280', fontWeight: '500' }}>Change (24h)</th>
                      </tr>
                    </thead>
                    <tbody>
                      {MOCK_EXCHANGE_RATES.map((rate, index) => (
                        <tr key={index}>
                          <td style={{ padding: '8px 0' }}>{rate.currency}</td>
                          <td style={{ padding: '8px 0' }}>{rate.rate}</td>
                          <td style={{
                            padding: '8px 0',
                            color: rate.isPositive ? '#22C55E' : '#EF4444'
                          }}>
                            {rate.change}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </section >

      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          item={supportLogsData[0]}
          // uploadOnly
          getUploadedFiles={setAddedAttachment}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          console.log('Goto rate update screen');
          navigate('/transactions/remittance-rate-of-exchange', {
            state: { currencyToSelect },
          });
        }}
      />
    </>
  );
};

export default withModal(ForeignCurrencyDeal);
