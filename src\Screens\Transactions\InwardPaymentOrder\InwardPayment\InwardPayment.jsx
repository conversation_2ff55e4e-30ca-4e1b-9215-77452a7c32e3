import { Form, Formik } from 'formik';
import { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiPaperClip, HiPrinter } from 'react-icons/hi2';
import CustomButton from '../../../../Components/CustomButton';
import CustomInput from '../../../../Components/CustomInput';
import CustomModal from '../../../../Components/CustomModal';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../../Components/StatusChip/StatusChip';
import TableActionDropDown from '../../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../../HOC/withFilters ';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import { inwardPaymentHeaders } from '../../../../Utils/Constants/TableHeaders';
import { useNavigate } from 'react-router-dom';
import { useFetchTableData } from '../../../../Hooks/useTable';
import {
  addInwardPaymentAttachment,
  changeInwardPaymentStatus,
  deleteInwardPaymentAttachment,
  getAccountsbyType,
  getCurrencies,
  getInwardPaymentListing,
  printInwardPayment,
} from '../../../../Services/Transaction/InwardPayment';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { showToast } from '../../../../Components/Toast/Toast';
import { formatDate, showErrorToast } from '../../../../Utils/Utils';
import AttachmentsView from '../../../../Components/AttachmentsView/AttachmentsView';
import { PulseLoader } from 'react-spinners';

const InwardPayment = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Inward Payment');
  const [showHoldingReasonModal, setShowHoldingReasonModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const navigate = useNavigate();
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [printItem, setPrintItem] = useState(null); // used only for print
  const queryClient = useQueryClient();

  // Fetch Table Data
  const { data, isLoading, isError, error } = useFetchTableData(
    'inwardPaymentListing',
    filters,
    updatePagination,
    getInwardPaymentListing
  );

  const inwardPaymentData = data?.data || [];

  // Mutation: Update Inward Payment Status
  const updateInwardPaymentMutation = useMutation({
    mutationFn: (data) => changeInwardPaymentStatus(data.id, data),
    onSuccess: () => {
      showToast('Status Updated!', 'success');
      queryClient.invalidateQueries(['inwardPaymentListing']);
    },
    onError: (error) => {
      console.error('Error updating Inward Payment Status', error);
      showErrorToast(error);
    },
  });

  const handleHoldingReasonSubmit = (values) => {
    updateInwardPaymentMutation.mutate({
      id: selectedItem?.id,
      status: 'hold',
      reason: values.comment, // sending reason to backend
    });
    setShowHoldingReasonModal(false);
  };

  const {
    data: inwardPaymentPrint,
    isLoading: inwardPaymentIsLoading,
    isError: inwardPaymentIsError,
    error: inwardPaymentError,
  } = useQuery({
    queryKey: ['printInwardPayment', printItem?.id],
    queryFn: () => printInwardPayment(printItem?.id),
    enabled: !!printItem?.id, // Only triggers when printItem is set
    refetchOnWindowFocus: false,
    retry: 1,
  });

  useEffect(() => {
    if (inwardPaymentPrint?.pdf_url || inwardPaymentPrint?.print_url) {
      const url = inwardPaymentPrint.pdf_url || inwardPaymentPrint.print_url;
      window.open(url, '_blank');
      setPrintItem(null); // reset after printing
    }
  }, [inwardPaymentPrint]);

  if (isError) {
    showErrorToast(error);
  }

  if (inwardPaymentIsError) {
    showErrorToast(inwardPaymentError);
  }

  const payTypeMap = {
    cash_deposit: 'Cash Deposit',
    cash_payment: 'Cash Payment',
    pdc: 'PDC',
    cheque_payment: 'Cheque Payment',
    cheque_deposit: 'Cheque Deposit',
  };

  //extra rows data
  const getCurrencySummaries = (data) => {
    const summaries = {};

    data?.forEach((item) => {
      const currency = item?.currency?.currency_code || 'N/A';
      const fcAmount = parseFloat(item?.fc_amount || 0);
      const balanceAmount = parseFloat(
        item?.paid?.balance_amount || item?.fc_amount
      );
      const status = item?.status;

      if (!summaries[currency]) {
        summaries[currency] = {
          fc_total: 0,
          fc_balance: 0,
          approved: 0,
          unapproved: 0,
        };
      }

      summaries[currency].fc_total += fcAmount;
      summaries[currency].fc_balance += balanceAmount;

      if (['approve', 'partial-paid'].includes(status)) {
        summaries[currency].approved += balanceAmount;
      } else {
        summaries[currency].unapproved += balanceAmount;
      }
    });

    return summaries;
  };

  const currencySummaries = getCurrencySummaries(inwardPaymentData);

  const summaryRows = Object.entries(currencySummaries).map(
    ([currency, summary]) => (
      <tr key={`summary-${currency}`} className="table-summary-row">
        <td colSpan={5}></td>
        <td>
          <strong>{currency}</strong>
        </td>
        <td>{summary.fc_balance.toFixed(2)}</td>
        <td>{summary.fc_total.toFixed(2)}</td>
        <td colSpan={3}>
          <strong>
            Approved: {summary.approved.toFixed(2)} - Unapproved:{' '}
            {summary.unapproved.toFixed(2)}
          </strong>
        </td>
        <td colSpan={5}></td>
      </tr>
    )
  );

  const { data: currencies = [] } = useQuery({
    queryKey: ['currenciesTypes'],
    queryFn: getCurrencies,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const [type, setType] = useState('');
  const [ledgerFilter, setLedgerFilter] = useState('');
  const [accountOptions, setAccountOptions] = useState([
    { value: 'All', label: 'All' },
  ]);

  // Fetch Ledger-Specific Accounts for Filter
  useEffect(() => {
    if (filters.ledger && filters.ledger !== ledgerFilter) {
      setLedgerFilter(filters.ledger);
    }
  }, [filters.ledger]);

  useEffect(() => {
    const fetchAccounts = async () => {
      if (!ledgerFilter) return;
      try {
        const data = await getAccountsbyType(ledgerFilter);
        const options = data.map((acc) => ({
          value: acc.id,
          label: acc.title || acc.name,
        }));
        setAccountOptions([{ value: 'All', label: 'All' }, ...options]);
      } catch (err) {
        console.error('Failed to load account types', err);
        setAccountOptions([{ value: 'All', label: 'All' }]);
      }
    };
    fetchAccounts();
  }, [ledgerFilter]);

  const {
    data: accountTypes,
    isLoading: accountTypeLoading,
    isError: accountTypeError,
    error: accountError,
  } = useQuery({
    queryKey: ['accountTypes', type],
    queryFn: () => getAccountsbyType(type),
    enabled: !!type,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const getAccountTypeOptions = () => {
    if (!type) return [{ label: 'Select Type First', value: null }];
    if (accountTypeLoading) return [{ label: 'Loading...', value: null }];
    if (accountTypeError)
      return [{ label: 'Unable to fetch account Type', value: null }];
    if (isNullOrEmpty(accountTypes))
      return [{ label: `No Accounts for type ${type}`, value: null }];

    const options = accountTypes.map((x) => ({ value: x.id, label: x.title }));
    return [
      { label: 'Select Account', value: null, disabled: true },
      ...options,
    ];
  };

  return (
    <section>
      <div className="d-flexflex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">Inward Payment</h2>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={inwardPaymentHeaders}
            pagination={pagination}
            isLoading={isLoading}
            useClearButton={true}
            isPaginated={false}
            additionalFilters={[
              {
                title: 'FC Amount',
                text: 'number',
              },
            ]}
            selectOptions={[
              {
                title: 'currency',
                options: [
                  { value: 'All', label: 'All' },
                  ...currencies.map((c) => ({
                    value: c.id,
                    label: c.currency_code,
                  })),
                ],
              },
              {
                title: 'ledger',
                options: [
                  { value: 'general', label: 'GL' },
                  { value: 'party', label: 'PL' },
                  { value: 'walkin', label: 'WIC' },
                ],
              },
              { title: 'account', options: accountOptions },

            ]}
            dateFilters={[{ title: 'Pay Date' }, { title: 'Debit Note Date' }]}
            rangeFilters={[{ title: 'Debit Note Range' }, { title: 'Amount' }]}
            summaryRows={summaryRows} // ✅ Pass it here
          >
            {(inwardPaymentData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={inwardPaymentHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {inwardPaymentData?.map((item) => (
                  <tr key={item.id}>
                    <td>{formatDate(item?.pay_date)}</td>
                    <td>{item?.beneficiary?.name}</td>
                    <td>{item?.id_number}</td>
                    <td>{item?.sender}</td>
                    <td>{item?.contact_no}</td>
                    <td>{item?.currency?.currency_code}</td>
                    <td>{item?.balance_amount || 0}</td>
                    <td>{item?.fc_amount}</td>
                    <td>{item?.ref_no}</td>
                    <td>{item?.order?.voucher?.voucher_no}</td>
                    <td>{formatDate(item?.order?.date)}</td>
                    <td>{item?.order?.debit_account_details?.title}</td>
                    <td>{payTypeMap[item?.pay_type] || item?.pay_type}</td>
                    <td>{item?.bank_name}</td>
                    <td>{item?.narration}</td>
                    <td>{item?.reason}</td>
                    <td>
                      <div className="d-flex align-items-center">
                        {item?.status === 'pending' && (
                          <StatusChip
                            status="Approve"
                            className="ms-2 cp"
                            onClick={() => {
                              updateInwardPaymentMutation.mutate({
                                id: item?.id,
                                status: 'approve',
                              });
                            }}
                          />
                        )}

                        {item?.status === 'approve' && (
                          <>
                            <StatusChip
                              status="Hold"
                              className="ms-2 cp"
                              onClick={() => {
                                setShowHoldingReasonModal(true);
                                setSelectedItem(item);
                              }}
                            />
                            <StatusChip
                              status="Pay"
                              className="ms-2 cp"
                              onClick={() => {
                                navigate(
                                  `/transactions/inward-payment/pay/${item?.id}`
                                );
                              }}
                            />
                          </>
                        )}

                        {item?.status === 'partial-paid' && (
                          <StatusChip
                            status="Pay"
                            className="ms-2 cp"
                            onClick={() => {
                              navigate(
                                `/transactions/inward-payment/pay/${item?.id}`
                              );
                            }}
                          />
                        )}

                        <TableActionDropDown
                          displaySeparator={false}
                          actions={[
                            {
                              name: 'Attachment',
                              icon: HiPaperClip,
                              onClick: () => {
                                setSelectedItem(item); // ✅ set the selected row item
                                setShowAttachmentsModal(true); // ✅ open the modal
                              },
                              className: 'edit',
                            },
                            {
                              name: 'Print',
                              icon: HiPrinter,
                              onClick: () => {
                                setPrintItem(item); // This will trigger useQuery
                              },
                              className: 'attachments',
                            },
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>

      {/* Attachements Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={selectedItem}
          deleteService={deleteInwardPaymentAttachment}
          uploadService={addInwardPaymentAttachment}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={false}
          queryToInvalidate={['inwardPaymentListing']}
        />
      </CustomModal>

      {/* Holding Reason Modal */}
      <CustomModal
        show={showHoldingReasonModal}
        close={() => setShowHoldingReasonModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">Holding Reason</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              comment: '',
            }}
            // validationSchema={outOfScopeSchema}
            onSubmit={handleHoldingReasonSubmit}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    label="Comment"
                    name="comment"
                    required
                    id="comment"
                    type="textarea"
                    rows={1}
                    placeholder="Enter comment"
                    value={values.comment}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.comment && errors.comment}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {!updateInwardPaymentMutation.isPending ? (
                    <>
                      <CustomButton type="submit" text={'Save'} />
                      <CustomButton
                        variant={'secondaryButton'}
                        text={'Cancel'}
                        type={'button'}
                        onClick={() => setShowHoldingReasonModal(false)}
                      />
                    </>
                  ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </section>
  );
};
export default withFilters(InwardPayment);
