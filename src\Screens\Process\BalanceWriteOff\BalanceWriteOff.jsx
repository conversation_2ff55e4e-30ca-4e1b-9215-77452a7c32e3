import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import withModal from '../../../HOC/withModal';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getbalanceWriteOffListing } from '../../../Services/Process/BalanceWriteOff';
import { statusFiltersConfig } from '../../../Utils/Constants/TableFilter';
import { showErrorToast } from '../../../Utils/Utils';

const BalanceWriteOff = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
  showModal,
}) => {
  const [selectedRows, setSelectedRows] = useState([]);
  const {
    data: { data: balanceWriteOffData = [] } = {},
    isLoading: isLoadingbalanceWriteOff,
    isError: isErrorbalanceWriteOff,
    error: balanceWriteOffError,
  } = useFetchTableData(
    'balanceWriteOffListing',
    filters,
    updatePagination,
    getbalanceWriteOffListing
  );

  const isAllSelected =
    balanceWriteOffData.length > 0 &&
    balanceWriteOffData.every((item) =>
      selectedRows.some((x) => x.id === item.id)
    );

  const handlePostClick = () => {
    if (!!selectedRows?.length) {
      showModal(
        'Balance',
        'Balance has been Written-Off using JV# 17600',
        null,
        'success'
      );
    } else {
      showModal(
        'Not Selected',
        'You have not selected any entry yet. Please choose an entry for posting.',
        null,
        'error'
      );
    }
  };

  const balanceWriteOffHeaders = [
    <div className="checkbox-wrapper">
      <label className="checkbox-container">
        <input
          type="checkbox"
          checked={isAllSelected}
          onChange={(e) => {
            e.target.checked
              ? setSelectedRows(balanceWriteOffData)
              : setSelectedRows([]);
          }}
          name={'header'}
        />
        <span className="custom-checkbox"></span>
      </label>
    </div>,
    'Ledger',
    'Account Name',
    'FCy',
    'Debit Balance',
    'Credit Balance',
  ];

  if (balanceWriteOffError) {
    showErrorToast(balanceWriteOffError, 'error');
  }

  return (
    <>
      <div className="d-flex justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">Balance Write-Off</h2>
        <CustomButton text={'Post'} onClick={handlePostClick} />
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={balanceWriteOffHeaders}
            pagination={pagination}
            isLoading={isLoadingbalanceWriteOff}
            selectOptions={[
              {
                title: 'FCy',
                options: statusFiltersConfig,
              },
              {
                title: 'Account',
                options: statusFiltersConfig,
              },
            ]}
            dateFilters={[{ title: 'Date Range', type: 'date' }]}
            rangeFilters={[{ title: 'Amount', type: 'number' }]}
          >
            {(balanceWriteOffData.length || isErrorbalanceWriteOff) && (
              <tbody>
                {isErrorbalanceWriteOff && (
                  <tr>
                    <td colSpan={balanceWriteOffHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {balanceWriteOffData?.map((item) => (
                  <tr key={item.id}>
                    <td>
                      <div className="checkbox-wrapper">
                        <label className="checkbox-container">
                          <input
                            type="checkbox"
                            checked={
                              selectedRows.some((x) => item.id === x.id) ||
                              false
                            }
                            onChange={(e) => {
                              e.target.checked
                                ? setSelectedRows([...selectedRows, item])
                                : setSelectedRows([
                                    ...selectedRows.filter(
                                      (x) => x.id != item.id
                                    ),
                                  ]);
                            }}
                            name={item.id}
                          />
                          <span className="custom-checkbox"></span>
                        </label>
                      </div>
                    </td>
                    <td>{item?.ledger}</td>
                    <td>{item?.account_name}</td>
                    <td>{item?.fcy}</td>
                    <td>{item?.debit_balance}</td>
                    <td>{item?.credit_balance}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </>
  );
};

export default withModal(withFilters(BalanceWriteOff));
