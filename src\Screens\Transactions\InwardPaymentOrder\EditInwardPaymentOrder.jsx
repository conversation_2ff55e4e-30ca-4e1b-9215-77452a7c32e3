import { Form, Formik } from 'formik';
import React, { useCallback, useRef, useState } from 'react';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA,
  MOCK_SUMMARY_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import { inwardPaymentOrderNewHeaders, SUMMARY_TABLE_HEADERS } from '../../../Utils/Constants/TableHeaders';
import { currencyTransferValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import InwardPaymentOrderRow from './InwardPaymentOrderRow';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
const generateInitialRows = (count) => {
  const rows = {};
  MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA?.tableData.forEach((row) => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      refNo: row.refNo,
      payType: row.payType,
      beneficiary: row.beneficiary,
      sender: row.sender,
      idNumber: row.idNumber,
      contactNo: row.contactNo,
      currency: row.currency,
      fcAmount: row.fcAmount,
      commission: row.commission,
      payDate: row.payDate,
      bankName: row.bankName,
      bankAc: row.bankAc,
      narration: row.narration
    };
  });
  return rows;
};
const INITIAL_STATE = generateInitialRows(3);

const EditInwardPaymentOrder = ({
  setPageState,
  setShowAddOfficeLocationModal,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect
}) => {
  const navigate = useNavigate();
  const [rows, setRows] = useState(INITIAL_STATE);
  const formikRef = useRef();

  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    newRows[id] = {
      id,
      refNo: '',
      payType: '',
      beneficiary: '',
      sender: '',
      idNumber: '',
      contactNo: '',
      currency: 'DHS',
      fcAmount: '',
      commission: '',
      payDate: '',
      bankName: '',
      bankAc: '',
      narration: ''
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    console.log('formValues', formValues);
    console.log('selectedFiles', selectedFiles);
    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([_, v]) => {
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    console.log('submit payload:', payload);
  };

  const handleCancel = () => {
    setPageState('new');
  };

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            debitLedger: MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.debitAccount.party,
            debitAccount: MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.debitAccount.value,
            office: MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.office,
            vatType: MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.vatType,
          }}
          validationSchema={currencyTransferValidationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, handleChange, handleBlur, setFieldValue }) => (
            <Form>
              <div className="row">
                {/* Debit Account Section */}
                <div className="col-12 col-xl-6">
                  <div className="row">
                    <div className="col-12">
                      <label htmlFor="debitLedger">Debit Account</label>
                    </div>
                    <div className="col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitLedger'}
                        options={[
                          { label: 'Party Ledger', value: 'pl' },
                          { label: 'General Ledger', value: 'gl' },
                          { label: 'Walk-in Customer', value: 'wic' },
                        ]}
                        placeholder={'Select Ledger'}
                        value={values.debitLedger}
                        onChange={(selected) => {
                          setFieldValue('debitLedger', selected.value);
                          setFieldValue('debitAccount', ''); // Reset account when ledger changes
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitLedger'}
                        options={[
                          ...(values.debitLedger === 'pl' ? [{ label: 'Add New PL', value: 'add_pl' }] : []),
                          ...(values.debitLedger === 'gl' ? [{ label: 'Add New GL', value: 'add_gl' }] : []),
                          ...(values.debitLedger === 'wic' ? [{ label: 'Add New WIC', value: 'add_wic' }] : []),
                        ]}
                        placeholder={'Select Debit Account'}
                        value={values.debitLedger}
                        onChange={(selected) => {
                          if (selected.value?.startsWith('add_')) {
                            setShowAddLedgerModal(selected.value.replace('add_', 'add new '));
                          } else {
                            setFieldValue('debitLedger', selected.value);
                          }
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                  </div>

                </div>

                {/* Credit Account Section replaced with Office and VAT Type */}
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'office'}
                    label={'Office'}
                    options={[
                      { label: 'Baku - Azerbaijan', value: 'baku_azerbaijan' },
                      { label: 'RAK - UAE', value: 'rak_uae' },
                      { label: 'Sharjah - UAE', value: 'sharjah_uae' },
                      { label: 'Syria', value: 'syria' },
                    ]}
                    placeholder={'Select Office'}
                    value={values.office}
                    onChange={(selected) => {
                      setFieldValue('office', selected.value);
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'vatType'}
                    label={'VAT Type'}
                    options={[
                      { label: 'Standard Rate (5.00%)', value: 'standard_rate' },
                      { label: 'Exempted (Nil)', value: 'exempted' },
                      { label: 'Zero Rate (0.00%)', value: 'zero_rate' },
                      { label: 'Out Of Scope', value: 'out_of_scope' },
                    ]}
                    placeholder={'Select VAT Type'}
                    value={values.vatType}
                    onChange={(selected) => {
                      if (selected.value.toLowerCase() === 'out_of_scope') {
                        setShowOutOfScopeModal(true);
                      } else {
                        setFieldValue('vatType', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12">
                  <CustomTable
                    displayCard={false}
                    headers={inwardPaymentOrderNewHeaders}
                    isPaginated={false}
                    className={'inputTable'}
                    hideSearch
                    hideItemsPerPage
                  >
                    <tbody>
                      {Object.values(rows).map((row, index) => (
                        <InwardPaymentOrderRow
                          key={row.id}
                          row={row}
                          index={index}
                          handleDeleteRow={handleDeleteRow}
                          updateField={updateField}
                          setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
                          setCurrencyToSelect={setCurrencyToSelect}
                        />
                      ))}
                    </tbody>
                  </CustomTable>
                  <div className="my-3">
                    <CustomButton
                      text="Add Special Commission"
                      variant="secondary"
                      type="button"
                      className="w-auto px-5"
                      onClick={() => navigate('/transactions/special-comission')}
                    />
                  </div>
                  <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => { }}
                      />
                      <CustomCheckbox
                        label="Print"
                        style={{ border: 'none', margin: 0 }}
                      />
                    </div>
                  </div>
                </div>

              </div>
            </Form>
          )}
        </Formik>
        <div className="mt-4">
          <CustomTable
            displayCard={false}
            headers={SUMMARY_TABLE_HEADERS}
            data={MOCK_SUMMARY_DATA}
            isPaginated={false}
            hideSearch
            hideItemsPerPage
          >
            <tbody>
              {MOCK_SUMMARY_DATA.map((row, index) => (
                <tr key={index}>
                  <td>{row.currency}</td>
                  <td>{row.total}</td>
                  <td>{row.commission}</td>
                  <td>{row.vatAmount}</td>
                  <td>{row.netTotal}</td>
                </tr>
              ))}
            </tbody>
          </CustomTable>
        </div>
      </div>
      <VoucherNavigationBar
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Update', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherHeading="Last DBN Number"
        lastVoucherNumber={23}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          item={supportLogsData[0]}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default EditInwardPaymentOrder;
