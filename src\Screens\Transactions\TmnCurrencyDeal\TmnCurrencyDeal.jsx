import { useEffect, useState } from 'react';
import { FaMagnifyingGlass } from 'react-icons/fa6';
import { useLocation, useNavigate } from 'react-router-dom';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import BackButton from '../../../Components/BackButton';
import BeneficiaryRegisterForm from '../../../Components/BeneficiaryRegisterForm/BeneficiaryRegisterForm';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import withModal from '../../../HOC/withModal';
import useAccountsByType from '../../../Hooks/useAccountsByType';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { supportLogsData } from '../../../Mocks/MockData';
import useFormStore from '../../../Stores/FormStore';
import { getCurrencyOptions } from '../../../Utils/Utils';
import NewTmnCurrencyDeal from './NewTmnCurrencyDeal';
import TmnCurrencyDealSearchTable from './TmnCurrencyDealSearchTable';
import ViewTmnCurrencyDeal from './ViewTmnCurrencyDeal';
// Add validation schema

const TmnCurrencyDeal = ({ showModal, closeModal }) => {
  const navigate = useNavigate();
  usePageTitle('TMN Currency Deal');
  const { state } = useLocation();
  const currencyOptions = getCurrencyOptions();

  const {
    getFormValues,
    getLastVisitedPage,
    setLastVisitedPage,
    saveFormValues,
  } = useFormStore();

  const [pageState, setPageState] = useState('new');
  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [writeTerm, setWriteTerm] = useState(state?.searchTerm || '');

  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [newlyCreatedBeneficiary, setNewlyCreatedBeneficiary] = useState(null);

  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState('');
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [searchType, setSearchType] = useState('buy');

  // Get account options using custom hook //
  const { getAccountsByTypeOptions } = useAccountsByType();

  // Get last voucher number //
  // const {
  //   data: voucherNumber,
  //   isLoading: isLoadingVoucherNumber,
  //   isError: isErrorVoucherNumber,
  //   error: errorVoucherNumber,
  // } = useQuery({
  //   queryKey: ['voucherNumber', searchTerm],
  //   queryFn: () => getRVVoucherNumber(searchTerm),
  //   refetchOnWindowFocus: false,
  //   retry: 1,
  // });
  // useEffect(() => {
  //   setLastVoucherNumbers({
  //     heading: 'Last TSN Number: ',
  //     last: voucherNumber?.default_voucher_no,
  //     current: voucherNumber?.current_voucher_no,
  //     previous: voucherNumber?.previous_voucher_no,
  //     next: voucherNumber?.next_voucher_no,
  //     isLoadingVoucherNumber: isLoadingVoucherNumber,
  //     isErrorVoucherNumber: isErrorVoucherNumber,
  //     errorVoucherNumber: errorVoucherNumber,
  //   });
  // }, [
  //   voucherNumber,
  //   isLoadingVoucherNumber,
  //   isErrorVoucherNumber,
  //   errorVoucherNumber,
  // ]);

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new beneficiary':
        return (
          <BeneficiaryRegisterForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setNewlyCreatedBeneficiary(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  const renderPageContent = () => {
    const pageComponents = {
      new: (
        <NewTmnCurrencyDeal
          state={state}
          date={date}
          isDisabled={isDisabled}
          setIsDisabled={setIsDisabled}
          currencyOptions={currencyOptions}
          getAccountsByTypeOptions={getAccountsByTypeOptions}
          newlyCreatedAccount={newlyCreatedAccount}
          newlyCreatedBeneficiary={newlyCreatedBeneficiary}
          setShowAddLedgerModal={setShowAddLedgerModal}
          setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
          setCurrencyToSelect={setCurrencyToSelect}
        />
      ),
      view: (
        <ViewTmnCurrencyDeal
          searchTerm={searchTerm}
          setDate={setDate}
          setWriteTerm={setWriteTerm}
          setSearchTerm={setSearchTerm}
          setPageState={setPageState}
          // lastVoucherNumbers={lastVoucherNumbers}
          searchType={searchType}
        />
      ),
      list: (
        <TmnCurrencyDealSearchTable
          date={date}
          searchType={searchType}
          setSearchTerm={setSearchTerm}
          setWriteTerm={setWriteTerm}
          setPageState={setPageState}
        />
      ),
      edit: <h2>EDIT</h2>,
    };

    return pageComponents[pageState] || null;
  };

  return (
    <>
      <section className="position-relative">
        <div
          style={{ height: 43 }}
          className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-4"
        >
          <div>
            {(pageState == 'list' ||
              pageState == 'view' ||
              pageState == 'edit') && (
              <BackButton
                handleBack={() => {
                  pageState == 'edit'
                    ? setPageState('view')
                    : (() => {
                        setDate(new Date().toISOString().split('T')[0]);
                        setPageState('new');
                        setWriteTerm('');
                        setSearchTerm('');
                      })();
                }}
              />
            )}
            <h2 className="screen-title mb-0">TMN Currency Deal</h2>
          </div>
          {pageState == 'new' && isDisabled && (
            <div className="d-flex gap-2">
              <CustomButton
                text="New"
                onClick={() => setIsDisabled(false)}
                className="mb-0"
              />
            </div>
          )}
        </div>

        <div className="d-flex justify-content-between flex-wrap align-items-end mb-3 gap-3">
          <div className="d-flex gap-3">
            <SearchableSelect
              name="searchType"
              options={[
                { label: 'Buy', value: 'buy' },
                { label: 'Sell', value: 'sell' },
              ]}
              showBorders={false}
              borderRadius={10}
              value={searchType}
              onChange={(selected) => {
                setSearchType(selected.value);
                setSearchTerm('');
              }}
              placeholder="Select Type"
            />
            <CustomInput
              style={{ width: '180px' }}
              type="text"
              placeholder={`Enter ${searchType === 'buy' ? 'TBN' : 'TSN'}`}
              error={false}
              showBorders={false}
              value={writeTerm}
              borderRadius={10}
              name="search"
              rightIcon={FaMagnifyingGlass}
              onChange={(e) => {
                setWriteTerm(e.target.value);
              }}
              onButtonClick={() => {
                setSearchTerm(writeTerm);
                if (writeTerm === '') {
                  setPageState('list');
                } else {
                  setPageState('view');
                }
              }}
            />
          </div>

          <CustomInput
            name="Date"
            label={'Date'}
            type="date"
            showBorders={false}
            error={false}
            borderRadius={10}
            value={date}
            disabled={pageState == 'view'}
            onChange={(e) => {
              setDate(e.target.value);
            }}
          />
        </div>
        {renderPageContent()}
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
      {/* Upload Attachments Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          item={supportLogsData[0]}
          // uploadOnly
          getUploadedFiles={setAddedAttachment}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Missing Currency Rate Modal */}
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          switch (pageState) {
            case 'edit':
              setLastVisitedPage('edit_tmn_currency_deal', 'rate-of-exchange');
              break;
            case 'new':
              setLastVisitedPage('new_tmn_currency_deal', 'rate-of-exchange');
              saveFormValues('new_tmn_currency_deal', {
                ...formData,
                date,
              });
              break;
            default:
              break;
          }
          navigate('/transactions/remittance-rate-of-exchange', {
            state: { currencyToSelect },
          });
        }}
      />
    </>
  );
};

export default withModal(TmnCurrencyDeal);
