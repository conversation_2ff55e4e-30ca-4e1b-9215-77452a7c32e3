import React from 'react';
import './accountBalanceCard.css';

const AccountBalanceCard = ({
  heading = 'Account Balance',
  accountName = '',
  balances = [],
  loading = false,
  error = '',
  className = '',
}) => {
  return (
    <>
      {heading && <h6 className="account-balance-card-heading">{heading}</h6>}
      <div className={`d-card mb-4 account-balance-card ${className}`}>
        <div className="mb-3 account-name w-100">{accountName}</div>
        <table className="w-100">
          <thead>
            <tr className="account-balance-card-table-header">
              <th className="account-balance-card-th">FCy</th>
              <th className="account-balance-card-th">Balance</th>
              <th className="account-balance-card-th"></th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan="3" className="account-balance-card-loading">
                  Loading...
                </td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan="3" className="account-balance-card-error">
                  {error}
                </td>
              </tr>
            ) : balances && balances.length > 0 ? (
              balances.map((balance, index) => (
                <tr key={index}>
                  <td
                    className={
                      balance.type == 'Dr.'
                        ? 'account-balance-card-td-dr'
                        : 'account-balance-card-td-cr'
                    }
                  >
                    {balance.currency}
                  </td>
                  <td className="account-balance-card-td">
                    {balance.balance ?? balance.amount}
                  </td>
                  <td className="account-balance-card-td">
                    {console.log(balance)}
                    {balance.type}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="3" className="account-balance-card-empty">
                  No balance data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default AccountBalanceCard;
