import { Form, Formik } from 'formik';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../../Components/CustomButton';
import CustomInput from '../../../../Components/CustomInput';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';

const ExpenseJournal = () => {
  const navigate = useNavigate();
  const handleSubmit = (values) => {
    console.log('Form Values:', values);
    navigate('generated');
  };

  return (
    <section>
      <div className="d-flex gap-3 justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">Expense Journal</h2>
      </div>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <Formik
              initialValues={{
                account: '',
                user_id: '',
                date_from: '',
                date_to: '',
              }}
              onSubmit={handleSubmit}
            >
              {({ values, handleChange, handleBlur, setFieldValue }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        label="Account"
                        name="account"
                        options={[]}
                        value={values.account}
                        onChange={(v) => setFieldValue('account', v.value)}
                        placeholder="Select Accopunt"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        label="User ID"
                        name="user_id"
                        options={[]}
                        value={values.user_id}
                        onChange={(v) => setFieldValue('user_id', v.value)}
                        placeholder="Select User ID"
                      />
                    </div>

                    <div className="col-12 col-sm-6">
                      <div className="row flex-wrap">
                        <label htmlFor="date_from">Date Range</label>
                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="date_from"
                            type="date"
                            value={values.date_from}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="date_to"
                            type="date"
                            value={values.date_to}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="d-flex mb-4">
                    <CustomButton type="submit" text="Generate" />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExpenseJournal;
