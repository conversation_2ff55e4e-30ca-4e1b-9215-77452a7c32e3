import { Form, Formik } from 'formik';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { PDCProcessSettledTypeOptions } from '../../../Utils/Constants/SelectOptions';
import { formatDate, isNullOrEmpty } from '../../../Utils/Utils';
import BackButton from '../../../Components/BackButton';

const ReceivableSettled = () => {
  usePageTitle('PDC Processes');
  const navigate = useNavigate();

  const { id } = useParams();
  const location = useLocation();
  const cheque = location?.state?.cheque || {};
  const [selectedType, setSelectedType] = useState('recall');

  useEffect(() => {
    if (isNullOrEmpty(cheque)) {
      navigate(`/process/pdc-processing`);
    }
    if (cheque?.status && cheque?.status.toLowerCase() !== 'settled') {
      navigate(`/process/pdc-processing/${id}/receivable/settled`);
    }
  }, [cheque]);

  const handleSubmit = (values) => {
    console.log(values);
  };

  console.log('cheque', cheque);

  const renderForm = () => {
    return (
      <Formik
        initialValues={{
          date: '',
        }}
        // validationSchema={passwordResetValidationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, errors, touched, handleChange, handleBlur }) => (
          <Form>
            <div className="row mb-4">
              <div className="col-12 col-sm-6 mb-3">
                <CustomInput
                  name={'date'}
                  type={'date'}
                  required
                  label={'Date'}
                  placeholder={'Enter Date'}
                  value={values.date}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.date && errors.date}
                />
              </div>
              <div className="mb-4">
                <p className="mb-0">Remarks:</p>
                <p className="muted-text">
                  PDCR # {cheque?.cheque_no} Dated:{' '}
                  {formatDate(cheque?.posting_date)} From{' '}
                  {cheque?.title_of_account} for {cheque?.fcy}{' '}
                  {cheque?.fc_amount}.
                </p>
              </div>
              <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                <CustomButton
                  // loading={updatePasswordMutation.isPending}
                  // disabled={updatePasswordMutation.isPending}
                  type={'submit'}
                  text={'Process'}
                />
                <CustomButton
                  // loading={updatePasswordMutation.isPending}
                  // disabled={updatePasswordMutation.isPending}
                  type={'button'}
                  text={'Cancel'}
                  variant="secondaryButton"
                  onClick={() => navigate(-1)}
                />
              </div>
            </div>
          </Form>
        )}
      </Formik>
    );
  };
  return (
    <>
      <div className="mb-3">
        <BackButton url={`/process/pdc-processing?tab=receivables`} />
        <h2 className="screen-title mb-0">PDC Processes</h2>
      </div>
      <div className="d-card py-45 mb-45">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <div className="row mb-3">
              <div className="col-12 col-sm-6 mb-3">
                <SearchableSelect
                  name="process_type"
                  label="Process Type"
                  placeholder="Select Process Type"
                  options={PDCProcessSettledTypeOptions}
                  value={selectedType}
                  onChange={(v) => setSelectedType(v.value)}
                />
              </div>
            </div>
            {renderForm()}
          </div>
        </div>
      </div>
    </>
  );
};

export default ReceivableSettled;
