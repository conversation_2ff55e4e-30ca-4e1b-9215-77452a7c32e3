export const loginCredentials = {
  email: '<EMAIL>',
  password: '123',
  status: true,
  role: 'admin',
  message: 'login successfully',
  token: '1164|ihHvE9J6cn1U3St4Sk6v6JKOdm2ARA87hXYbIdS63831040a',
};
export const loggedInUser = {
  'user-id': 1,
  'full-name': '<PERSON>',
  'first-name': '<PERSON>',
  'last-name': '<PERSON>',
  type: 'admin',
  token: 'somerandomtokenwhichis38characterslong',
  phone: '+190078601',
  dialing_code: '+1',
  email: '<EMAIL>',
  'photo-path':
    'https://static.wikia.nocookie.net/villains/images/8/8d/Red_Dead_2_Arthur_Morgan_Promotional_Portrait_1.png',
};
export const notifications = [
  {
    id: 1,
    user: {
      name: '<PERSON>',
      'profile-image':
        'https://t3.ftcdn.net/jpg/06/92/98/26/360_F_692982616_sFOE56exqjylP20296OjiD83FQxewdDw.jpg',
    },
    action: 'liked your comment',
    notificationText: `<PERSON> reported an issue: "Unable to create a new football discussion group."`,
    timestamp: '5 minutes ago',
    date: '08/19/2024',
    read: true,
  },
  {
    id: 2,
    user: {
      name: 'John Doe',
      'profile-image':
        'https://t3.ftcdn.net/jpg/06/92/98/26/360_F_692982616_sFOE56exqjylP20296OjiD83FQxewdDw.jpg',
    },
    action: 'shared your post',
    notificationText: `Alex raised a query: "Posted football tactics, but it doesn't show in the group."`,
    timestamp: '3 hours ago',
    date: '08/19/2024',
    read: false,
  },
  {
    id: 3,
    user: {
      name: 'Sophia Brown',
      'profile-image':
        'https://t3.ftcdn.net/jpg/06/92/98/26/360_F_692982616_sFOE56exqjylP20296OjiD83FQxewdDw.jpg',
    },
    action:
      'commented on your photo and this text is here just to make the notification very long',
    notificationText: `Chris submitted a complaint: "Messages not sending in the group chat."`,
    timestamp: '10 minutes ago',
    date: '08/19/2024',
    read: true,
  },
  {
    id: 4,
    user: {
      name: 'David Wilson',
      'profile-image':
        'https://t3.ftcdn.net/jpg/06/92/98/26/360_F_692982616_sFOE56exqjylP20296OjiD83FQxewdDw.jpg',
    },
    action: 'followed you',
    notificationText: `Andrew encountered a problem: "Trying to join a football analysis group but getting an error."`,
    timestamp: '1 day ago',
    date: '08/18/2024',
    read: false,
  },
  {
    id: 5,
    user: {
      name: 'Olivia Johnson',
      'profile-image':
        'https://t3.ftcdn.net/jpg/06/92/98/26/360_F_692982616_sFOE56exqjylP20296OjiD83FQxewdDw.jpg',
    },
    action: 'added your post to favorites',
    notificationText: `James flagged an issue: "Football post content gets flagged for no reason."`,
    timestamp: '30 minutes ago',
    date: '08/19/2024',
    read: true,
  },
];

export const remittanceData = {
  labels: ['Sales', 'Revenue', 'Orders', 'Vendors'],
  datasets: [
    {
      label: 'Remittance',
      data: [40, 30, 20, 10],
      backgroundColor: ['#1f4047', '#fdc770', '#B6C0C3', '#666'],
      borderWidth: 0,
    },
  ],
  options: {
    maintainAspectRatio: false,
    responsive: true,
    plugins: {
      legend: {
        position: 'left',
        labels: {
          padding: 0, // Increase padding between items
          boxWidth: 30, // Set width of the color box for each legend item
          boxHeight: 30, // Set width of the color box for each legend item
          font: {
            family: 'Poppins',
            size: 14, // Adjust font size to further space items
            lineHeight: 1.5,
          },
        },
      },
    },
  },
};
export const cashBalanceData = {
  type: 'bar',
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'June', 'July'],
  datasets: [
    {
      label: 'Cashhh',
      data: [65, 59, 80, 81, 56, 55, 40],
      backgroundColor: ['#1f4047', '#fdc770'],
      borderWidth: 0,
      barThickness: 30,
    },
  ],
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        ticks: {
          maxTicksLimit: 6,
          font: {
            family: 'Poppins',
          },
        },
        beginAtZero: true,
      },
      x: {
        ticks: {
          font: {
            family: 'Poppins',
          },
        },
      },
    },
  },
};
export const lineGraphOptions = {
  maintainAspectRatio: false,
  responsive: true,
  interaction: {
    mode: 'nearest', // Show the nearest point
    intersect: false, // Show point even if not exactly on it
  },
  plugins: {
    tooltip: {
      intersect: false,
    },
    legend: {
      display: false,
    },
    title: {
      display: false,
    },
  },
  scales: {
    y: {
      ticks: {
        maxTicksLimit: 6,

        font: {
          family: 'Poppins',
          size: 14, // Adjust font size to further space items
          lineHeight: 1.5,
        },
      },
      title: {
        display: false,
      },
      beginAtZero: true,
    },
    x: {
      ticks: {
        font: {
          family: 'Poppins',
          size: 14, // Adjust font size to further space items
          lineHeight: 1.5,
        },
      },
      title: {
        display: false,
      },
    },
  },
};
export const netIncomeRatioData = {
  labels: [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'June',
    'July',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],
  datasets: [
    {
      data: [10, 17, 12, 14, 15, 18, 19, 23, 25, 20, 29, 32],
      borderRadius: 50,
      tension: 0.4,
      pointBorderColor: '#ccc',
      pointRadius: 0, // Hide points by default
      pointHoverRadius: 6, // Show points when hovering
      pointBorderWidth: 5,
      pointHoverBorderWidth: 5,
      pointBackgroundColor: '#fff',
      borderColor: '#ccc',
      borderWidth: 2,
      fill: {
        target: 'origin',
      },
      backgroundColor: 'rgba(0, 0, 0, 0.2)',
    },
  ],
};
export const subscriptionTableData = {
  status: true,
  message: 'subscription logs',
  detail: {
    current_page: 1,
    data: [
      {
        id: '1',
        serialNumber: '01',
        title: 'Subscription ABC',
        amount: '$40',
        subscriptionDate: 'January 12, 2023',
        expiryDate: 'January 12, 2023',
        status: 'Active',
      },
      {
        id: '2',
        serialNumber: '02',
        title: 'Subscription DEF',
        amount: '$50',
        subscriptionDate: 'March 12, 2023',
        expiryDate: 'March 12, 2024',
        status: 'Active',
      },
      {
        id: '3',
        serialNumber: '03',
        title: 'Subscription GHI',
        amount: '$60',
        subscriptionDate: 'February 12, 2023',
        expiryDate: 'February 12, 2024',
        status: 'Active',
      },
      {
        id: '4',
        serialNumber: '04',
        title: 'Subscription JKL',
        amount: '$70',
        subscriptionDate: 'January 12, 2023',
        expiryDate: 'January 12, 2024',
        status: 'Inactive',
      },
      {
        id: '5',
        serialNumber: '05',
        title: 'Subscription MNO',
        amount: '$80',
        subscriptionDate: 'March 12, 2023',
        expiryDate: 'March 12, 2024',
        status: 'Inactive',
      },
      {
        id: '6',
        serialNumber: '06',
        title: 'Subscription PQR',
        amount: '$90',
        subscriptionDate: 'February 12, 2023',
        expiryDate: 'February 12, 2024',
        status: 'Active',
      },
      {
        id: '7',
        serialNumber: '07',
        title: 'Subscription STU',
        amount: '$95',
        subscriptionDate: 'January 12, 2023',
        expiryDate: 'January 12, 2024',
        status: 'Active',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/subscriptions?page=1',
    from: null,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/subscriptions?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/subscriptions?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/subscriptions',
    per_page: 10,
    prev_page_url: null,
    to: null,
    total: 60,
  },
};
export const wareHouseTableData = {
  status: true,
  message: 'warehouse data',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        code: 'WM01',
        name: 'Location ABCD6',
        created_by: 78,
        edited_by: null,
        parent_id: 78,
        created_at: '2024-10-17T02:49:13.000000Z',
        updated_at: '2024-10-17T04:01:30.000000Z',
      },
      {
        id: 2,
        code: 'WM02',
        name: 'Location ABCD',
        created_by: 78,
        edited_by: null,
        parent_id: 78,
        created_at: '2024-10-17T02:50:20.000000Z',
        updated_at: '2024-10-17T02:50:20.000000Z',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/warehouse?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/warehouse?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/warehouse?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/warehouse',
    per_page: 10,
    prev_page_url: null,
    to: 2,
    total: 2,
  },
};
// MileStone Design
// https://www.figma.com/design/WSDIXs1BdGilcQZJ32S7JY/Milestone---Project?node-id=487-83339&t=dgprVwnzibfJkq4A-1

// Updated
// https://www.figma.com/design/zpHb1r9gkFPu3K95m686Bk/Milestone---Project?node-id=1-8333&t=q9LMySj8QwRsAwbj-1

// API
// https://api.postman.com/collections/18177523-3c549471-5c2f-4c71-9c93-bda29777c1ad?access_key=PMAT-01JAGD9XP6WBKZJ46B1DR7XT5K

export const branchs = [
  {
    value: 'Branch A',
    label: 'Branch A',
  },
  {
    value: 'Branch B',
    label: 'Branch B',
  },
];

// Admin
export const notificationsData = {
  status: true,
  message: 'admin notifications',
  detail: {
    notifications: {
      current_page: 1,
      data: [
        {
          id: '2fb2b0ba-4859-47c4-826c-1742a8ac39db',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 77,
              },
            },
          },
          created_at: '2024-12-28T10:42:45.000000Z',
          updated_at: '2024-06-14T10:42:45.000000Z',
        },
        {
          id: '31d6a0f8-d820-4afd-a1c7-e64abaf8796a',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 74,
              },
            },
          },
          read_at: null,
          created_at: '2024-06-06T12:03:40.000000Z',
          updated_at: '2024-06-06T12:03:40.000000Z',
        },
        {
          id: '3e380bad-85bf-411a-ab5a-ed3977bf179c',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 71,
              },
            },
          },
          created_at: '2024-05-30T13:20:22.000000Z',
          updated_at: '2024-05-30T13:20:22.000000Z',
        },
        {
          id: '6169abb9-24cb-4f47-9bb4-74bc886dfa5a',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 78,
              },
            },
          },
          read_at: '2024-06-14T10:42:45.000000Z',
          created_at: '2024-06-25T11:16:18.000000Z',
          updated_at: '2024-06-25T11:16:18.000000Z',
        },
        {
          id: '704674e3-84c6-4de4-878e-5f6a53a1c16a',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 72,
              },
            },
          },
          read_at: null,
          created_at: '2024-05-30T13:21:05.000000Z',
          updated_at: '2024-05-30T13:21:05.000000Z',
        },
        {
          id: '7b4774e0-0326-4481-8a33-044c1c047810',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 81,
              },
            },
          },
          read_at: null,
          created_at: '2024-06-25T11:17:25.000000Z',
          updated_at: '2024-06-25T11:17:25.000000Z',
        },
        {
          id: '91740da1-89df-407b-a2c0-042ed28a1468',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 83,
              },
            },
          },
          read_at: null,
          created_at: '2024-06-26T11:22:54.000000Z',
          updated_at: '2024-06-26T11:22:54.000000Z',
        },
        {
          id: '9ff7d276-caa5-475d-845a-4d0359d0e91d',
          type: 'App\\Core\\Notifications\\PushNotification',
          notifiable_type: 'App\\Models\\Admin',
          notifiable_id: 1,
          data: {
            title: 'New Feedback',
            body: 'There are many variations of passages of Lorem Ipsum available. There are many variations of passages of Lorem Ipsum available.',
            route: {
              name: 'admin.feedbacks.show',
              params: {
                id: 69,
              },
            },
          },
          read_at: null,
          created_at: '2024-05-30T11:21:32.000000Z',
          updated_at: '2024-05-30T11:21:32.000000Z',
        },
      ],
      first_page_url:
        'http://localhost/food_app/admin-api/notifications?page=1',
      from: 1,
      last_page: 2,
      last_page_url: 'http://localhost/food_app/admin-api/notifications?page=2',
      links: [
        {
          url: null,
          label: '&laquo; Previous',
          active: false,
        },
        {
          url: 'http://localhost/food_app/admin-api/notifications?page=1',
          label: '1',
          active: true,
        },
        {
          url: 'http://localhost/food_app/admin-api/notifications?page=2',
          label: '2',
          active: false,
        },
        {
          url: 'http://localhost/food_app/admin-api/notifications?page=2',
          label: 'Next &raquo;',
          active: false,
        },
      ],
      next_page_url: 'http://localhost/food_app/admin-api/notifications?page=2',
      path: 'http://localhost/food_app/admin-api/notifications',
      per_page: 10,
      prev_page_url: null,
      to: 10,
      total: 14,
    },
    total_notifications: 0,
  },
};
export const userManagementData = [
  {
    id: 1,
    business_id: 12365,
    business_name: 'ABC Corp',
    contact_person: 'John Doe',
    user_id: 'user123',
    phone_no: '1265798687',
    email_address: '<EMAIL>',
    registration_date: '12/01/2024',
    status: 'active',
  },
  {
    id: 2,
    business_id: 18445,
    business_name: 'XYZ Ltd',
    contact_person: 'Jane Smith',
    user_id: 'user124',
    phone_no: '9********0',
    email_address: '<EMAIL>',
    registration_date: '15/02/2024',
    status: 'active',
  },
  {
    id: 3,
    business_id: 4856,
    business_name: 'Acme Co',
    contact_person: 'Robert Brown',
    user_id: 'user125',
    phone_no: '1122334455',
    email_address: '<EMAIL>',
    registration_date: '20/03/2024',
    status: 'active',
  },
  {
    id: 4,
    business_id: 58964,
    business_name: 'Global Tech',
    contact_person: 'Emily White',
    user_id: 'user126',
    phone_no: '9988776655',
    email_address: '<EMAIL>',
    registration_date: '25/04/2024',
    status: 'inactive',
  },
  {
    id: 5,
    business_id: 5687,
    business_name: 'Innovatech',
    contact_person: 'Chris Green',
    user_id: 'user127',
    phone_no: '7766554433',
    email_address: '<EMAIL>',
    registration_date: '30/05/2024',
    status: 'inactive',
  },
  {
    id: 6,
    business_id: 5496,
    business_name: 'TechWave',
    contact_person: 'Alex Johnson',
    user_id: 'user128',
    phone_no: '6655443322',
    email_address: '<EMAIL>',
    registration_date: '05/06/2024',
    status: 'active',
  },
  {
    id: 7,
    business_id: 2314,
    business_name: 'NextGen Solutions',
    contact_person: 'Samantha Lee',
    user_id: 'user129',
    phone_no: '5544332211',
    email_address: '<EMAIL>',
    registration_date: '10/07/2024',
    status: 'active',
  },
];
export const branchData = [
  {
    id: 1,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Unblock',
  },
  {
    id: 2,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Unblock',
  },
  {
    id: 3,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Block',
  },
  {
    id: 4,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Unblock',
  },
  {
    id: 5,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Unblock',
  },
  {
    id: 6,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Block',
  },
  {
    id: 7,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Unblock',
  },
  {
    id: 8,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Unblock',
  },
  {
    id: 9,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Unblock',
  },
  {
    id: 10,
    name: 'Branch Abc',
    address: 'Lorem ipsum',
    manager: 'Manager A',
    supervisor: 'Supervisor A',
    base_currency: 'DHS',
    status: 'Block',
  },
];
export const unlockRequestData = [
  {
    id: '1',
    request_date_time: '25/12/2024 10:15',
    requestor_name: 'User A',
    approval_rejection_date_time: '25/12/2024 12:30',
    status: 'pending',
  },
  {
    id: '2',
    request_date_time: '26/12/2024 09:00',
    requestor_name: 'User B',
    approval_rejection_date_time: '26/12/2024 11:00',
    status: 'approved',
  },
  {
    id: '3',
    request_date_time: '28/12/2024 08:20',
    requestor_name: 'User D',
    approval_rejection_date_time: '28/12/2024 10:10',
    status: 'rejected',
  },
  {
    id: '4',
    request_date_time: '27/12/2024 14:45',
    requestor_name: 'User C',
    approval_rejection_date_time: '27/12/2024 16:00',
    status: 'approved',
  },
  {
    id: '5',
    request_date_time: '29/12/2024 15:30',
    requestor_name: 'User E',
    approval_rejection_date_time: '29/12/2024 17:45',
    status: 'approved',
  },
  {
    id: '6',
    request_date_time: '30/12/2024 13:15',
    requestor_name: 'User F',
    approval_rejection_date_time: '30/12/2024 15:20',
    status: 'approved',
  },
  {
    id: '7',
    request_date_time: '31/12/2024 11:10',
    requestor_name: 'User G',
    approval_rejection_date_time: '31/12/2024 13:00',
    status: 'rejected',
  },
  {
    id: '8',
    request_date_time: '01/01/2025 09:45',
    requestor_name: 'User H',
    approval_rejection_date_time: '01/01/2025 11:30',
    status: 'approved',
  },
  {
    id: '9',
    request_date_time: '02/01/2025 10:20',
    requestor_name: 'User I',
    approval_rejection_date_time: '02/01/2025 12:00',
    status: 'pending',
  },
  {
    id: '10',
    request_date_time: '03/01/2025 08:50',
    requestor_name: 'User J',
    approval_rejection_date_time: '03/01/2025 10:40',
    status: 'approved',
  },
];
export const unlockRequestDetailsData = [
  {
    id: 1,
    name: 'helo',
    email: '<EMAIL>',
    role: 'Owner',
    status: 'Pending',
    unlocking_reason:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. ',
    start_date: 'DD/MM/YYYY - hh:mm',
    end_date: 'DD/MM/YYYY - hh:mm',
    request_date_time: 'DD/MM/YYYY - hh:mm',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
  },
  {
    id: 2,
    name: 'helo',
    email: '<EMAIL>',
    role: 'Owner',
    status: 'Approved',
    unlocking_reason:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. ',
    start_date: 'DD/MM/YYYY - hh:mm',
    end_date: 'DD/MM/YYYY - hh:mm',
    request_date_time: 'DD/MM/YYYY - hh:mm',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
  },
  {
    id: 3,
    name: 'helo',
    email: '<EMAIL>',
    role: 'Owner',
    status: 'Rejected',
    unlocking_reason:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. ',
    rejection_reason:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. ',
    start_date: 'DD/MM/YYYY - hh:mm',
    end_date: 'DD/MM/YYYY - hh:mm',
    request_date_time: 'DD/MM/YYYY - hh:mm',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
  },
];
export const subscriptionManagementData = [
  {
    id: 1,
    subscription_name: 'Basic Plan',
    number_of_users: 10,
    user_id: 1,
    branches: 4,
    price_monthly: '$15',
    price_yearly: '$150',
    modification_date: '12/25/2024',
    status: 'active',
  },
  {
    id: 2,
    subscription_name: 'Standard Plan',
    number_of_users: 20,
    user_id: 1,
    branches: 6,
    price_monthly: '$25',
    price_yearly: '$250',
    modification_date: '12/26/2024',
    status: 'active',
  },
  {
    id: 3,
    subscription_name: 'Premium Plan',
    number_of_users: 50,
    user_id: 1,
    branches: 10,
    price_monthly: '$50',
    price_yearly: '$500',
    modification_date: '12/27/2024',
    status: 'active',
  },
  {
    id: 4,
    subscription_name: 'Enterprise Plan',
    number_of_users: 100,
    user_id: 1,
    branches: 15,
    price_monthly: '$100',
    price_yearly: '$1000',
    modification_date: '12/28/2024',
    status: 'inactive',
  },
  {
    id: 5,
    subscription_name: 'Basic Plus',
    number_of_users: 15,
    user_id: 1,
    branches: 5,
    price_monthly: '$20',
    price_yearly: '$200',
    modification_date: '12/29/2024',
    status: 'inactive',
  },
  {
    id: 6,
    subscription_name: 'Pro Plan',
    number_of_users: 30,
    user_id: 1,
    branches: 8,
    price_monthly: '$35',
    price_yearly: '$350',
    modification_date: '12/30/2024',
    status: 'active',
  },
  {
    id: 7,
    subscription_name: 'Ultimate Plan',
    number_of_users: 200,
    user_id: 1,
    branches: 20,
    price_monthly: '$200',
    price_yearly: '$2000',
    modification_date: '01/01/2025',
    status: 'active',
  },
];
export const subscriptionRequestsData = [
  {
    id: 1,
    user_name: 'Custom Package',
    email: '<EMAIL>',
    expected_users: 10,
    expected_branches: 5,
    status: 'Pending',
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
  {
    id: 2,
    user_name: 'Custom Package',
    email: '<EMAIL>',
    expected_users: 15,
    expected_branches: 3,
    status: 'Approved',
  },
  {
    id: 3,
    user_name: 'Custom Package',
    email: '<EMAIL>',
    expected_users: 20,
    expected_branches: 7,
    status: 'Rejected',
  },
  {
    id: 4,
    user_name: 'Custom Package',
    email: '<EMAIL>',
    expected_users: 8,
    expected_branches: 4,
    status: 'Approved',
  },
  {
    id: 5,
    user_name: 'Custom Package',
    email: '<EMAIL>',
    expected_users: 12,
    expected_branches: 6,
    status: 'Rejected',
  },
  {
    id: 6,
    user_name: 'Custom Package',
    email: '<EMAIL>',
    expected_users: 18,
    expected_branches: 9,
    status: 'Pending',
  },
  {
    id: 7,
    user_name: 'Custom Package',
    email: '<EMAIL>',
    expected_users: 14,
    expected_branches: 5,
    status: 'Approved',
  },
];

export const adminSubscriptionLogsData = [
  {
    id: 1,
    business_name: 'Abc Corp',
    subscription_name: 'Basic Plan',
    amount: '$20',
    subscription_type: 'Monthly',
    subscription_date: '01/01/2024',
    status: 'Active',
  },
  {
    id: 2,
    business_name: 'Abc Corp',
    subscription_name: 'Basic Plan',
    amount: '$20',
    subscription_type: 'Monthly',
    subscription_date: '02/01/2024',
    status: 'Active',
  },
  {
    id: 3,
    business_name: 'Abc Corp',
    subscription_name: 'Basic Plan',
    amount: '$20',
    subscription_type: 'Monthly',
    subscription_date: '03/01/2024',
    status: 'Active',
  },
  {
    id: 4,
    business_name: 'Abc Corp',
    subscription_name: 'Premium Plan',
    amount: '$50',
    subscription_type: 'Yearly',
    subscription_date: '04/01/2023',
    status: 'Expired',
  },
  {
    id: 5,
    business_name: 'Abc Corp',
    subscription_name: 'Premium Plan',
    amount: '$50',
    subscription_type: 'Yearly',
    subscription_date: '05/01/2023',
    status: 'Expired',
  },
  {
    id: 6,
    business_name: 'Abc Corp',
    subscription_name: 'Standard Plan',
    amount: '$30',
    subscription_type: 'Yearly',
    subscription_date: '06/01/2024',
    status: 'Active',
  },
  {
    id: 7,
    business_name: 'Abc Corp',
    subscription_name: 'Standard Plan',
    amount: '$30',
    subscription_type: 'Yearly',
    subscription_date: '07/01/2024',
    status: 'Active',
  },
];
export const supportTypeManagementData = [
  {
    id: 1,
    support_type: 'General Inquiry',
    creation_date: '01/01/2024',
  },
  {
    id: 2,
    support_type: 'Requirement',
    creation_date: '02/01/2024',
  },
  {
    id: 6,
    support_type: 'Complaint',
    creation_date: '06/01/2024',
  },
  {
    id: 7,
    support_type: 'Type ABC',
    creation_date: '07/01/2024',
  },
];
export const supportLogsData = [
  {
    id: 1,
    name: 'Abc',
    email: '<EMAIL>',
    contact_number: '125684659',
    support_type: 'Support',
    date: '01/01/2024',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
  {
    id: 2,
    name: 'Abc',
    email: '<EMAIL>',
    contact_number: '125684659',
    support_type: 'General Inquiry',
    date: '02/01/2024',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
  {
    id: 3,
    name: 'Abc',
    email: '<EMAIL>',
    contact_number: '125684659',
    support_type: 'Requirement',
    date: '03/01/2024',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
  {
    id: 4,
    name: 'Abc',
    email: '<EMAIL>',
    contact_number: '125684659',
    support_type: 'Type ABC',
    date: '04/01/2024',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
  {
    id: 5,
    name: 'Abc',
    email: '<EMAIL>',
    contact_number: '125684659',
    support_type: 'Type ABC',
    date: '05/01/2024',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
  {
    id: 6,
    name: 'Abc',
    email: '<EMAIL>',
    contact_number: '125684659',
    support_type: 'Type ABC',
    date: '06/01/2024',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
      {
        id: 80,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DEE8ZUl2HyGD7kwXTpsel90dTkwrLEkoeTFm2mlf.pdf',
      },
    ],
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
  {
    id: 7,
    name: 'Abc',
    email: '<EMAIL>',
    contact_number: '125684659',
    support_type: 'Type ABC',
    date: '07/01/2024',
    files: [
      {
        id: 79,
        fileable_type: 'App\\Models\\DocumentRegister',
        fileable_id: 34,
        path: 'DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
        name: 'trq2.pdf',
        file_url:
          'https://custom-dev.onlinetestingserver.com/milestone/storage/media/DIA2ym38vBj0Q2KW0MlfOM47UQPy2rGDUr1Tg4j6.pdf',
      },
    ],
    additional_comments:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar tempor. Cum sociis',
  },
];
export const branchDetailsData = {
  // Branch Details tab
  branch_name: 'Downtown Branch',
  address: '123 Financial Street',
  city: 'New York',
  contact_no: '+**********',
  manager: 'manager1',
  supervisor: 'supervisor1',
  base_currency: 'USD',

  // Posting Account tab
  account_payable: 'AP-001',
  account_receivable: 'AR-001',
  post_dated_chqs_recd: 'PDC-001',
  post_dated_chqs_payable: 'PDP-001',
  cash_customer: 'CC-001',
  bank_suspense_ac: 'BSA-001',
  inward_remittance_payment: 'IRP-001',
  fc_remittance_ac: 'FCR-001',
  commission_ac: 'COM-001',
  commission_expense_ac: 'COMEXP-001',
  discount_exp_ac: 'DEXP-001',
  iwt_receivable_ac: 'IWT-001',
  vat_input_ac: 'VATIN-001',
  vat_output_ac: 'VATOUT-001',
  remittance_income: 'RI-001',
  counter_income: 'CI-001',
  vat_absorb_expense_ac: 'VATABSORB-001',
  cost_of_sale_ac: 'COS-001',
  stock_in_hand_ac: 'SIH-001',
  depreciation_exp: 'DEP-001',
  misc_exp: 'MISC-001',
  write_off_account: 'WOA-001',

  // System Dates tab
  opening_date: '2024-01-01',
  closed_upto: '2024-03-15',
  accept_data_upto: '2024-03-31',

  // Dashboard tab
  startup_alert_period: 3,
  currency_rate_trend: 6,
  dashboard_comparison: 30,
  currency_pairs: [1, 2, 3], // IDs of selected currency pairs

  // Central Bank Limits tab
  inwards_payment_order: 50000,
  outwards_remittance: 75000,
  counter_transaction: 25000,
  cash_limit: 100000,
  cash_bank_pay_limit: 200000,
  monthly_transaction: 1000000,
  counter_commission: 1000,

  // VAT Parameters tab
  vat_trn: 'VAT********9',
  country: 'US',
  default_city: 'New York',
  cities: 'New York, Los Angeles, Chicago',
  vat_type: 'variable',
  vat_percentage: '5.00',
  vat_rates: [
    { id: 1, title: 'Standard Rate', percentage: '5.00' },
    { id: 2, title: 'Exempted', percentage: 'Nill' },
    { id: 3, title: 'Zero Rate', percentage: '0.00' },
    {
      id: 4,
      title: 'Out of Scope',
      percentage:
        'A small popup will appear to the user to write the reason why does the VAT Amount is out of scope',
    },
    { id: 5, title: 'Special Rate', percentage: '3.00' },
  ],

  // Misc Parameters tab
  disable_party_id: true,
  disable_beneficiary: false,
  enable_personalized_marking: true,
  show_agent_commission_cbs: true,
  show_agent_commission_fsn: false,
  show_agent_commission_fbn: true,
  allow_advance_commission: false,
  fsn_entry_approval: true,
  fbn_entry_approval: true,
  cbs_entry_approval: false,
  rv_entry_approval: true,
  pv_entry_approval: true,
  trq_entry_approval: false,
  a2a_entry_approval: true,
  jv_entry_approval: true,
  tsn_tbn_entry_approval: false,
  enable_two_setup_approval: true,
  debit_posting_account: 'v1',
  credit_posting_account: 'v2',
  rounding_off: true,

  // Additional metadata
  created_at: '2024-03-15T10:30:00Z',
  created_by: 'admin',
  status: 'active',
  branch_code: 'DTN001',
  last_modified: '2024-03-15T14:45:00Z',
  modified_by: 'admin',
};
export const unlockRequestLogsData = [
  {
    id: 1,
    request_date_time: '2025-01-20T10:30:00Z',
    requestor_name: 'User ABC',
    approval_rejection_date_time: '2025-01-20T12:30:00Z',
    status: 'Approved',
  },
  {
    id: 2,
    request_date_time: '2025-01-20T11:00:00Z',
    requestor_name: 'User ABC',
    approval_rejection_date_time: '2025-01-20T13:00:00Z',
    status: 'Approved',
  },
  {
    id: 3,
    request_date_time: '2025-01-20T11:30:00Z',
    requestor_name: 'User ABC',
    approval_rejection_date_time: null,
    status: 'Pending',
  },
  {
    id: 4,
    request_date_time: '2025-01-20T12:00:00Z',
    requestor_name: 'User ABC',
    approval_rejection_date_time: '2025-01-20T14:00:00Z',
    status: 'Rejected',
  },
  {
    id: 5,
    request_date_time: '2025-01-20T12:15:00Z',
    requestor_name: 'User ABC',
    approval_rejection_date_time: '2025-01-20T14:15:00Z',
    status: 'Rejected',
  },
  {
    id: 6,
    request_date_time: '2025-01-20T12:45:00Z',
    requestor_name: 'User ABC',
    approval_rejection_date_time: '2025-01-20T14:45:00Z',
    status: 'Approved',
  },
  {
    id: 7,
    request_date_time: '2025-01-20T13:00:00Z',
    requestor_name: 'User ABC',
    approval_rejection_date_time: '2025-01-20T15:00:00Z',
    status: 'Approved',
  },
];
export const transactionLogsData = [
  {
    id: 1,
    transaction_type: 'CBS',
    number: 268379,
    transaction_date: '2024-10-22T00:00:00Z',
    modification_date: '2024-10-22T00:00:00Z',
    modification_time: '2024-10-22T11:33:20',
    user_id: 'Abbas',
    action_type: 'Edited',
  },
  {
    id: 2,
    transaction_type: 'FSN',
    number: 3836982,
    transaction_date: '2024-10-22T00:00:00Z',
    modification_date: '2024-10-22T00:00:00Z',
    modification_time: '2024-10-22T11:33:20',
    user_id: 'Abbas',
    action_type: 'Edited',
  },
  {
    id: 3,
    transaction_type: 'PV',
    number: 3836556,
    transaction_date: '2024-10-22T00:00:00Z',
    modification_date: '2024-10-22T00:00:00Z',
    modification_time: '2024-10-22T11:33:20',
    user_id: 'Abbas',
    action_type: 'Deleted',
  },
  {
    id: 4,
    transaction_type: 'JV',
    number: 873572,
    transaction_date: '2024-10-22T00:00:00Z',
    modification_date: '2024-10-22T00:00:00Z',
    modification_time: '2024-10-22T11:33:20',
    user_id: 'Abbas',
    action_type: 'Deleted',
  },
];
export const systemIntegrityData = [
  {
    type: 'TRQ',
    number: 1513,
    date: '2024-10-22T00:00:00Z',
    title_of_account: 'Lucy Chen',
    narration: 'Paid to RMB Ortsa Account',
    fcy: 'DHS DHS',
    lc_debit: 100763.0,
    lc_credit: null,
    cost_center: null,
    user_id: 'Shamal',
    fc_amount: 100763.0,
  },
  {
    type: 'FBN',
    number: 1513,
    date: '2024-10-22T00:00:00Z',
    title_of_account: 'Lucy Chen',
    narration: 'Paid to RMB Ortsa Account',
    fcy: 'DHS DHS',
    lc_debit: null,
    lc_credit: 10763.01,
    cost_center: null,
    user_id: 'Shamal',
    fc_amount: 10763.01,
  },
  {
    type: 'FBN',
    number: 1513,
    date: '2024-10-22T00:00:00Z',
    title_of_account: 'Lucy Chen',
    narration: 'SPM Electronics',
    fcy: 'DHS DHS',
    lc_debit: null,
    lc_credit: 10763.01,
    cost_center: null,
    user_id: 'Shamal',
    fc_amount: 10763.01,
  },
  {
    type: 'TRQ',
    number: 1513,
    date: '2024-10-22T00:00:00Z',
    title_of_account: 'Lucy Chen',
    narration: 'Paid to RMB Ortsa Account',
    fcy: 'DHS DHS',
    lc_debit: null,
    lc_credit: 10763.01,
    cost_center: null,
    user_id: 'Shamal',
    fc_amount: 10763.01,
  },
  {
    type: 'TRQ',
    number: 1513,
    date: '2024-10-22T00:00:00Z',
    title_of_account: 'Lucy Chen',
    narration: 'Paid to RMB Ortsa Account',
    fcy: 'DHS DHS',
    lc_debit: null,
    lc_credit: 10763.01,
    cost_center: null,
    user_id: 'Shamal',
    fc_amount: 10763.01,
  },
  {
    type: 'TRQ',
    number: 1513,
    date: '2024-10-22T00:00:00Z',
    title_of_account: 'Lucy Chen',
    narration: 'SPM Electronics',
    fcy: 'USD USD',
    lc_debit: null,
    lc_credit: 27873.92,
    cost_center: null,
    user_id: 'Shamal',
    fc_amount: 27873.92,
  },
  {
    type: 'TRQ',
    number: 1513,
    date: '2024-10-22T00:00:00Z',
    title_of_account: 'Lucy Chen',
    narration: 'Paid to RMB Ortsa Account',
    fcy: 'USD USD',
    lc_debit: null,
    lc_credit: 237873.92,
    cost_center: null,
    user_id: 'Shamal',
    fc_amount: 237873.92,
  },
];
export const dealRegisterUpdationData = [
  {
    date: '2024-10-22T00:00:00Z',
    fcy: 'USD',
    account: -4400934.145,
    deal_register: -4400934.145,
    counter: 0.05,
    difference: 0.05,
  },
  {
    date: '2024-10-22T00:00:00Z',
    fcy: 'USD',
    account: 755799.715,
    deal_register: 755799.715,
    counter: -0.425,
    difference: -0.425,
  },
  {
    date: '2024-10-22T00:00:00Z',
    fcy: 'USD',
    account: 755799.715,
    deal_register: 755799.715,
    counter: -0.425,
    difference: -0.425,
  },
  {
    date: '2024-10-22T00:00:00Z',
    fcy: 'EUR',
    account: 755799.715,
    deal_register: 755799.715,
    counter: -0.425,
    difference: -0.425,
  },
  {
    date: '2024-10-22T00:00:00Z',
    fcy: 'EUR',
    account: 755799.715,
    deal_register: 755799.715,
    counter: -0.425,
    difference: -0.425,
  },
  {
    date: '2024-10-22T00:00:00Z',
    fcy: 'EUR',
    account: 755799.715,
    deal_register: 755799.715,
    counter: -0.425,
    difference: -0.425,
  },
  {
    date: '2024-10-22T00:00:00Z',
    fcy: 'EUR',
    account: 755799.715,
    deal_register: 755799.715,
    counter: -0.425,
    difference: -0.425,
  },
];
export const pdcProcessReceivablesData = {
  status: true,
  message: 'PDC Process Receivables listing',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        cheque_no: 102937,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 50000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Open',
      },
      {
        id: 2,
        cheque_no: 102935,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 100000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Settled',
      },
      {
        id: 3,
        cheque_no: 102937,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 50000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Discounted Collection',
      },
      {
        id: 4,
        cheque_no: 102935,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 100000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Collection',
      },
      {
        id: 5,
        cheque_no: 102935,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 100000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Cancelled',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance',
    per_page: 10,
    prev_page_url: null,
    to: 2,
    total: 2,
  },
};
export const pdcProcessPayablesData = {
  status: true,
  message: 'PDC Process Payables listing',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        cheque_no: 102937,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 50000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Open',
      },
      {
        id: 2,
        cheque_no: 102935,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 100000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Settled',
      },
      {
        id: 3,
        cheque_no: 102937,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 50000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Cancelled',
      },
      {
        id: 4,
        cheque_no: 102935,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 100000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Cancelled',
      },
      {
        id: 5,
        cheque_no: 102935,
        due_date: '2024-10-30T00:00:00Z',
        posting_date: '2024-10-30T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 100000.0,
        drawn_on: 'Emirates Bank',
        title_of_account: 'KM Financials',
        narration: 'CH, 1000004 19/10/2024',
        status: 'Cancelled',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance',
    per_page: 10,
    prev_page_url: null,
    to: 2,
    total: 2,
  },
};
export const pdcrPaymentPostingData = {
  status: true,
  message: 'PDCR Payment Posting listing',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        cheque_no: 104512,
        dated: '2024-10-30T00:00:00Z',
        fcy: 'USD',
        fc_amount: 75000.5,
        received_from: 'ABC Corp',
        issued_to: 'XYZ Ltd',
        status: 'Settled',
      },
      {
        id: 2,
        cheque_no: 103678,
        dated: '2024-10-29T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 120000.75,
        received_from: 'LMN Bank',
        issued_to: 'PQR Co',
        status: 'Return Unpaid',
      },
      {
        id: 3,
        cheque_no: 105789,
        dated: '2024-10-28T00:00:00Z',
        fcy: 'EUR',
        fc_amount: 65000.0,
        received_from: 'XYZ Holdings',
        issued_to: 'DEF Ltd',
        status: 'Revert',
      },
      {
        id: 4,
        cheque_no: 102345,
        dated: '2024-10-24T00:00:00Z',
        fcy: 'DHS',
        fc_amount: 98000.2,
        received_from: 'GHI Industries',
        issued_to: 'JKL Pvt',
        status: 'Settled',
      },
      {
        id: 5,
        cheque_no: 108912,
        dated: '2024-10-19T00:00:00Z',
        fcy: 'USD',
        fc_amount: 54000.65,
        received_from: 'OPQ Enterprises',
        issued_to: 'MNO Ltd',
        status: 'Revert',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance',
    per_page: 10,
    prev_page_url: null,
    to: 2,
    total: 2,
  },
};
export const balanceWriteOffData = {
  status: true,
  message: 'Balance Write Off listing',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        ledger: 'Corporate',
        account_name: 'XYZ Ltd',
        fcy: 'USD',
        debit_balance: 452376.25,
        credit_balance: 983245.5,
      },
      {
        id: 2,
        ledger: 'Retail',
        account_name: 'ABC Corp',
        fcy: 'DHS',
        debit_balance: 678912.0,
        credit_balance: 562341.0,
      },
      {
        id: 3,
        ledger: 'Supplier',
        account_name: 'LMN Bank',
        fcy: 'EUR',
        debit_balance: 789654.75,
        credit_balance: 0.0,
      },
      {
        id: 4,
        ledger: 'Party',
        account_name: 'DEF Industries',
        fcy: 'DHS',
        debit_balance: 342678.8,
        credit_balance: 576890.2,
      },
      {
        id: 5,
        ledger: 'Walk-in',
        account_name: 'PQR Enterprises',
        fcy: 'USD',
        debit_balance: 908765.6,
        credit_balance: 245678.3,
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance',
    per_page: 10,
    prev_page_url: null,
    to: 5,
    total: 5,
  },
};
export const transactionApprovalData = {
  status: true,
  message: 'Transaction Approval listing',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        trans_type: 'RV',
        trans_no: 456,
        trans_date: '12/02/2025',
        party: 'Party B',
        secondary_account: 'Account XYZ',
        currency: 'USD',
        amount: 250,
        user_id: 'User DEF',
        approved_by: 'User XYZ',
        received_from_paid_to: 'User PQR',
        comment: 'Transaction verified',
        status: 'Approved',
        attachments: 'Yes',
      },
      {
        id: 2,
        trans_type: 'PV',
        trans_no: 789,
        trans_date: '05/03/2025',
        party: 'Party C',
        secondary_account: 'Account LMN',
        currency: 'EUR',
        amount: 500,
        user_id: 'User GHI',
        approved_by: 'User ABC',
        received_from_paid_to: 'User XYZ',
        comment: 'Pending confirmation',
        status: 'Pending',
        attachments: 'No',
      },
      {
        id: 3,
        trans_type: 'PV',
        trans_no: 321,
        trans_date: '20/01/2025',
        party: 'Party A',
        secondary_account: 'Account ABC',
        currency: 'DHS',
        amount: 750,
        user_id: 'User ABC',
        approved_by: 'User DEF',
        received_from_paid_to: 'User XYZ',
        comment: 'Rejected due to insufficient funds',
        status: 'Unapproved',
        attachments: 'No',
      },
      {
        id: 4,
        trans_type: 'RV',
        trans_no: 654,
        trans_date: '28/02/2025',
        party: 'Party D',
        secondary_account: 'Account DEF',
        currency: 'GBP',
        amount: 100,
        user_id: 'User XYZ',
        approved_by: 'User PQR',
        received_from_paid_to: 'User ABC',
        comment: 'Transaction completed successfully',
        status: 'Approved',
        attachments: 'Yes',
      },
      {
        id: 5,
        trans_type: 'PV',
        trans_no: 987,
        trans_date: '15/02/2025',
        party: 'Party E',
        secondary_account: 'Account UVW',
        currency: 'JPY',
        amount: 600,
        user_id: 'User JKL',
        approved_by: 'User MNO',
        received_from_paid_to: 'User XYZ',
        comment: 'Processing...',
        status: 'Pending',
        attachments: 'Yes',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance',
    per_page: 10,
    prev_page_url: null,
    to: 5,
    total: 5,
  },
};
export const rateRevaluationData = {
  status: true,
  message: 'Rate Revaluation listing',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        group: 'Forex',
        currency: 'USD',
        fc_balance: '512,300',
        valuation_rate: '3.456789',
        value_in_dhs: '1,234,567.89',
        gain_loss: '10,234.56',
      },
      {
        id: 2,
        group: 'Forex',
        currency: 'EUR',
        fc_balance: '-415,620',
        valuation_rate: '3.987654',
        value_in_dhs: '2,345,678.90',
        gain_loss: '-15,678.45',
      },
      {
        id: 3,
        group: 'Exchange',
        currency: 'CAD',
        fc_balance: '278,450',
        valuation_rate: '2.876543',
        value_in_dhs: '987,654.32',
        gain_loss: '5,432.10',
      },
      {
        id: 4,
        group: 'Exchange',
        currency: 'GBP',
        fc_balance: '-198,700',
        valuation_rate: '4.123456',
        value_in_dhs: '3,456,789.01',
        gain_loss: '-7,890.12',
      },
      {
        id: 5,
        group: 'Forex',
        currency: 'AUD',
        fc_balance: '354,780',
        valuation_rate: '2.543210',
        value_in_dhs: '654,321.98',
        gain_loss: '3,210.65',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance',
    per_page: 10,
    prev_page_url: null,
    to: 5,
    total: 5,
  },
};
export const transactionLockData = {
  status: true,
  message: 'Rate Revaluation listing',
  detail: {
    current_page: 1,
    data: [
      {
        id: 1,
        trans_type: 'PV',
        trans_no: 101,
        trans_date: '12/01/2024',
        party: 'Party X',
        currency: 'USD',
        amount: 250,
        locked_by: 'User XYZ',
        locked_datetime: '12/01/2024 14:30',
      },
      {
        id: 2,
        trans_type: 'RV',
        trans_no: 102,
        trans_date: '13/01/2024',
        party: 'Party Y',
        currency: 'EUR',
        amount: 300,
        locked_by: 'User ABC',
        locked_datetime: '13/01/2024 15:45',
      },
      {
        id: 3,
        trans_type: 'PV',
        trans_no: 103,
        trans_date: '14/01/2024',
        party: 'Party Z',
        currency: 'GBP',
        amount: 180,
        locked_by: 'User DEF',
        locked_datetime: '14/01/2024 12:20',
      },
      {
        id: 4,
        trans_type: 'RV',
        trans_no: 104,
        trans_date: '15/01/2024',
        party: 'Party A',
        currency: 'JPY',
        amount: 500,
        locked_by: 'User MNO',
        locked_datetime: '15/01/2024 10:10',
      },
      {
        id: 5,
        trans_type: 'PV',
        trans_no: 105,
        trans_date: '16/01/2024',
        party: 'Party B',
        currency: 'AUD',
        amount: 220,
        locked_by: 'User PQR',
        locked_datetime: '16/01/2024 09:05',
      },
    ],
    first_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    from: 1,
    last_page: 1,
    last_page_url:
      'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
    links: [
      {
        url: null,
        label: '&laquo; Previous',
        active: false,
      },
      {
        url: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance?page=1',
        label: '1',
        active: true,
      },
      {
        url: null,
        label: 'Next &raquo;',
        active: false,
      },
    ],
    next_page_url: null,
    path: 'https://custom-dev.onlinetestingserver.com/milestone/user-api/user-maintenance',
    per_page: 10,
    prev_page_url: null,
    to: 5,
    total: 5,
  },
};

// Reports
export const journalReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      type: 'JV',
      transition_no: 987654,
      date: 'dd/mm/yyyy',
      title_of_account: 'ACME Corp',
      narration: 'Transaction details',
      fcy: 'USD',
      debit: 500,
      credit: null,
      base_amount: 500,
      cost_center: 'Finance',
      user_id: 'AdminUser',
      updated_on: '15/02/2025-10:30',
      attachment: 'Yes',
    },
    {
      id: 2,
      type: 'JV',
      transition_no: 654321,
      date: 'dd/mm/yyyy',
      title_of_account: 'XYZ Ltd',
      narration: 'Invoice settlement',
      fcy: 'EUR',
      debit: null,
      credit: 300,
      base_amount: 300,
      cost_center: 'Operations',
      user_id: 'AdminUser',
      updated_on: '15/02/2025-10:45',
      attachment: 'No',
    },
    {
      id: 3,
      type: 'JV',
      transition_no: 123789,
      date: 'dd/mm/yyyy',
      title_of_account: 'Tech Solutions',
      narration: 'Expense reimbursement',
      fcy: 'GBP',
      debit: 750,
      credit: null,
      base_amount: 750,
      cost_center: 'HR',
      user_id: 'SysAdmin',
      updated_on: '15/02/2025-11:00',
      attachment: 'Yes',
    },
    {
      id: 4,
      type: 'JV',
      transition_no: 456987,
      date: 'dd/mm/yyyy',
      title_of_account: 'Logistics Inc',
      narration: 'Freight charges',
      fcy: 'CAD',
      debit: null,
      credit: 200,
      base_amount: 200,
      cost_center: 'Logistics',
      user_id: 'SysAdmin',
      updated_on: '15/02/2025-11:15',
      attachment: 'No',
    },
  ],
};
export const walkInCustomerStatementData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      date: '14/11/2024',
      type: 'TSN',
      tran_no: '1234',
      narration: 'Sell TMN',
      fcy: 'DHS',
      debit: '1200',
      credit: '',
      balance: '12,000',
      sign: 'Dr',
      value_date: '14/11/2024',
    },
    {
      id: 2,
      date: '14/11/2024',
      type: 'TSN',
      tran_no: '1234',
      narration: 'Sell TMN',
      fcy: 'DHS',
      debit: '1200',
      credit: '',
      balance: '12,000',
      sign: 'Dr',
      value_date: '14/11/2024',
    },
    {
      id: 3,
      date: '14/11/2024',
      type: 'TSN',
      tran_no: '1234',
      narration: 'Buy DHS',
      fcy: 'TMN',
      debit: '',
      credit: '192200',
      balance: '192200',
      sign: 'Cr',
      value_date: '14/11/2024',
    },
  ],
};
export const walkInCustomerAccountJournalData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      title_of_account: 'Cash Customers',
      narration: 'Lorem Ipsum',
      fcy: 'DHS',
      debit: 173972.982,
      credit: null,
    },
    {
      id: 2,
      title_of_account: 'Cash Customers',
      narration: 'Lorem Ipsum',
      fcy: 'TWN',
      debit: null,
      credit: 473874.0,
    },
  ],
};
export const walkInCustomerOutstandingBalanceData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      title_of_account: 'Sultan',
      fcy: 'DHS',
      debit: '173,972.982',
      credit: '',
    },
    {
      id: 2,
      title_of_account: 'Ismail',
      fcy: 'USD',
      debit: '',
      credit: '473,874.00',
    },
    {
      id: 3,
      title_of_account: 'Sultan',
      fcy: 'EUR',
      debit: '173,972.982',
      credit: '',
    },
    {
      id: 4,
      title_of_account: 'Ismail',
      fcy: 'USD',
      debit: '',
      credit: '473,874.00',
    },
  ],
};
export const statementOfAccountsData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      date: '14/11/2024',
      type: 'TSN',
      tran_no: '1234',
      narration: 'Sell TMN',
      fcy: 'DHS',
      debit: 1200,
      credit: null,
      lc_balance: 12000,
      sign: 'Dr',
      value_date: '14/11/2024',
    },
    {
      id: 2,
      date: '14/11/2024',
      type: 'TSN',
      tran_no: '1234',
      narration: 'Sell TMN',
      fcy: 'DHS',
      debit: 1200,
      credit: null,
      lc_balance: 12000,
      sign: 'Dr',
      value_date: '14/11/2024',
    },
    {
      id: 3,
      date: '14/11/2024',
      type: 'TSN',
      tran_no: '1234',
      narration: 'Buy DHS',
      fcy: 'USD',
      debit: null,
      credit: 192200,
      lc_balance: 192200,
      sign: 'Cr',
      value_date: '14/11/2024',
    },
  ],
};
export const outstandingBalanceData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      title_of_account: 'Sultan',
      fcy: 'DHS',
      debit: 173972.982,
      credit: null,
      base_currency_value: 173972.982,
    },
    {
      id: 2,
      title_of_account: 'Ismail',
      fcy: 'USD',
      debit: null,
      credit: 473874.0,
      base_currency_value: -173972.982,
    },
    {
      id: 3,
      title_of_account: 'Hashim',
      fcy: 'EUR',
      debit: 173972.982,
      credit: null,
      base_currency_value: 173972.982,
    },
    {
      id: 4,
      title_of_account: 'Sultan A',
      fcy: 'DHS',
      debit: 173972.982,
      credit: null,
      base_currency_value: -173972.982,
    },
    {
      id: 5,
      title_of_account: 'Ismail A',
      fcy: 'USD',
      debit: null,
      credit: 473874.0,
      base_currency_value: -173972.982,
    },
    {
      id: 6,
      title_of_account: 'Hashim A',
      fcy: 'EUR',
      debit: 173972.982,
      credit: null,
      base_currency_value: 173972.982,
    },
  ],
};
export const expenseJournalData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      type: 'TRQ',
      tran_no: '1234',
      date: '14/11/2024',
      account_title: 'Bank Charges',
      narration: 'WIO BANK OPEN DEMIR',
      fcy: 'DHS',
      fcy_amount: '40,500.00',
      base_currency_value: 'DHS',
      user_id: 'ABU',
    },
    {
      id: 2,
      type: 'JV',
      tran_no: '1234',
      date: '14/11/2024',
      account_title: 'Bank Charges',
      narration: 'WIO BANK OPEN DEMIR',
      fcy: 'EUR',
      fcy_amount: '910.0',
      base_currency_value: 'EUR',
      user_id: 'ABU',
    },
    {
      id: 3,
      type: 'PV',
      tran_no: '1234',
      date: '14/11/2024',
      account_title: 'Bank Charges',
      narration: 'PAID TO MISR ARAVON RM',
      fcy: 'DHS',
      fcy_amount: '3000',
      base_currency_value: 'DHS',
      user_id: 'ABU',
    },
  ],
};
export const postDatedChequesData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      title_of_account: 'KATSAN',
      cheque_no: '12346',
      base_amount: '12346',
      due_date: 'dd/mm/yyyy',
      drawn_on: 'Bank A',
      posting_date: 'dd/mm/yyyy',
      status: 'Open',
      fcy: 'USD',
      fc_amount: '12346',
      cost_center: '',
      discount_collection_bank: '',
    },
    {
      id: 2,
      title_of_account: 'KATSAN',
      cheque_no: '12346',
      base_amount: '12346',
      due_date: 'dd/mm/yyyy',
      drawn_on: 'Bank A',
      posting_date: 'dd/mm/yyyy',
      status: 'Open',
      fcy: 'USD',
      fc_amount: '12346',
      cost_center: '',
      discount_collection_bank: '',
    },
    {
      id: 3,
      title_of_account: 'KATSAN',
      cheque_no: '12346',
      base_amount: '12346',
      due_date: 'dd/mm/yyyy',
      drawn_on: 'Bank A',
      posting_date: 'dd/mm/yyyy',
      status: 'Open',
      fcy: 'USD',
      fc_amount: '12346',
      cost_center: '',
      discount_collection_bank: '',
    },
    {
      id: 4,
      title_of_account: 'KATSAN',
      cheque_no: '12346',
      base_amount: '12346',
      due_date: 'dd/mm/yyyy',
      drawn_on: 'Bank A',
      posting_date: 'dd/mm/yyyy',
      status: 'Open',
      fcy: 'USD',
      fc_amount: '12346',
      cost_center: '',
      discount_collection_bank: '',
    },
  ],
};
export const vatTaxReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      type: 'RV',
      tran_no: '268379',
      date: 'dd/mm/yyyy',
      ledger: 'General',
      title_of_account: 'KATSAN',
      fcy: 'USD',
      fc_amount: '268379',
      vat_amount: '268379',
      net_total: '268379',
      base_amount: '268379',
      base_vat_amount: '268379',
      base_net_total: '268379',
    },
    {
      id: 2,
      type: 'PV',
      tran_no: '268379',
      date: 'dd/mm/yyyy',
      ledger: 'Walk-in',
      title_of_account: 'KATSAN',
      fcy: 'DHS',
      fc_amount: '268379',
      vat_amount: '268379',
      net_total: '268379',
      base_amount: '268379',
      base_vat_amount: '268379',
      base_net_total: '268379',
    },
    {
      id: 3,
      type: 'IPV',
      tran_no: '268379',
      date: 'dd/mm/yyyy',
      ledger: 'Party',
      title_of_account: 'KATSAN',
      fcy: 'USD',
      fc_amount: '268379',
      vat_amount: '268379',
      net_total: '268379',
      base_amount: '268379',
      base_vat_amount: '268379',
      base_net_total: '268379',
    },
    {
      id: 4,
      type: 'RV',
      tran_no: '268379',
      date: 'dd/mm/yyyy',
      ledger: 'Walk-in',
      title_of_account: 'KATSAN',
      fcy: 'EUR',
      fc_amount: '268379',
      vat_amount: '268379',
      net_total: '268379',
      base_amount: '268379',
      base_vat_amount: '268379',
      base_net_total: '268379',
    },
  ],
};
export const budgetingForecastingReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      metrics: 'Abc',
      projected: 268379,
      actual: 268379,
      variance: -200,
    },
    {
      id: 2,
      metrics: 'Abc',
      projected: 268379,
      actual: 268379,
      variance: 200,
    },
    {
      id: 3,
      metrics: 'Abc',
      projected: 268379,
      actual: 268379,
      variance: 0,
    },
    {
      id: 4,
      metrics: 'Abc',
      projected: 268379,
      actual: 268379,
      variance: -21,
    },
  ],
};
export const currencyTransferRegisterReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      time: 'hh:mm',
      from_account: 'KATSAN',
      to_account: 'ABC Account',
      currency: 'USD',
      amount: 268379,
      narration: 'lorem ipsum',
      net_total: 268379,
      doc_type: 'Doc. A',
      doc_no: 12345,
      bank: 'Bank A',
      city: 'City A',
      code: '123abc',
    },
    {
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      time: 'hh:mm',
      from_account: 'KATSAN',
      to_account: 'ABC Account',
      currency: 'DHS',
      amount: 268379,
      narration: 'lorem ipsum',
      net_total: 268379,
      doc_type: 'Doc. A',
      doc_no: 12345,
      bank: 'Bank A',
      city: 'City A',
      code: '123abc',
    },
    {
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      time: 'hh:mm',
      from_account: 'KATSAN',
      to_account: 'ABC Account',
      currency: 'USD',
      amount: 268379,
      narration: 'lorem ipsum',
      net_total: 268379,
      doc_type: 'Doc. A',
      doc_no: 12345,
      bank: 'Bank A',
      city: 'City A',
      code: '12abc',
    },
    {
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      time: 'hh:mm',
      from_account: 'KATSAN',
      to_account: 'ABC Account',
      currency: 'EUR',
      amount: 268379,
      narration: 'lorem ipsum',
      net_total: 268379,
      doc_type: 'Doc. A',
      doc_no: 12345,
      bank: 'Bank A',
      city: 'City A',
      code: '123abc',
    },
  ],
};
export const outwardRemittanceReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      tran_type: 'FSN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'USD',
      fc_amount: 'ABC Account',
      against_fcy: 'USD',
      rate: 2683,
      commission: 268379,
      doc_swift: 'not received',
      against_fc_amount: 12346,
      confirmation_status: 'Not Confirmed',
      comment: 'lorem ipsum',
    },
    {
      id: 2,
      tran_type: 'FBN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'DHS',
      fc_amount: 'ABC Account',
      against_fcy: 'DHS',
      rate: 2683,
      commission: 268379,
      doc_swift: 'not received',
      against_fc_amount: 12346,
      confirmation_status: 'Not Confirmed',
      comment: 'lorem ipsum',
    },
    {
      id: 3,
      tran_type: 'FBN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'USD',
      fc_amount: 'ABC Account',
      against_fcy: 'USD',
      rate: 26837,
      commission: 268379,
      doc_swift: 'not received',
      against_fc_amount: 12346,
      confirmation_status: 'Confirmed',
      comment: 'lorem ipsum',
    },
    {
      id: 4,
      tran_type: 'FBN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'EUR',
      fc_amount: 'ABC Account',
      against_fcy: 'EUR',
      rate: 2683,
      commission: 268379,
      doc_swift: 'not received',
      against_fc_amount: 12346,
      confirmation_status: 'Confirmed',
      comment: 'lorem ipsum',
      opposing_no: 'FBN 1',
    },
  ],
};
export const outwardRemittanceEnquiryData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      tran_type: 'FSN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'USD',
      fc_amount: 'ABC Account',
      against_fcy: 'USD',
      rate: 2683,
      user_id: 268379,
      status: 'Not Confirmed',
      against_fc_amount: 12346,
      opposing_no: 'FBN 1',
      opposing_account: 'Account B',
    },
    {
      id: 2,
      tran_type: 'FBN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'DHS',
      fc_amount: 'ABC Account',
      against_fcy: 'DHS',
      rate: 2683,
      user_id: 268379,
      status: 'Not Confirmed',
      against_fc_amount: 12346,
      opposing_no: 'FBN 2',
      opposing_account: 'Account B',
    },
    {
      id: 3,
      tran_type: 'FBN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'USD',
      fc_amount: 'ABC Account',
      against_fcy: 'USD',
      rate: 26837,
      user_id: 268379,
      status: 'Confirmed',
      against_fc_amount: 12346,
      opposing_no: 'FBN 3',
      opposing_account: 'Account C',
    },
    {
      id: 4,
      tran_type: 'FBN',
      tran_no: 268379,
      date: 'dd/mm/yyyy',
      account: 'Account A',
      beneficiary: 'KATSAN',
      fcy: 'EUR',
      fc_amount: 'ABC Account',
      against_fcy: 'EUR',
      rate: 2683,
      user_id: 268379,
      status: 'Confirmed',
      against_fc_amount: 12346,
      comment: 'lorem ipsum',
      opposing_no: 'FBN 1',
      opposing_account: 'Account B',
    },
  ],
};
export const inwardRemittanceReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      tran_no: 1,
      tran_date: 'dd/mm/yyyy',
      account: 'SHERNOBY',
      nationality: 'Abc',
      beneficiary_name: 'Abc',
      beneficiary_place_of_work: 'Abc',
      beneficiary_nationality: 'AFGHAN',
      beneficiary_id_no: '784-1999-75 9842',
      contact_number: '784-1999-75 9842',
      country_of_origin: 'UAE',
      purpose: 'Reg.Transfer',
      fcy: 'USD',
      fc_amount: 2683,
      lc_amount: 2683,
    },
    {
      id: 2,
      tran_no: 2,
      tran_date: 'dd/mm/yyyy',
      account: 'SHERNOBY',
      nationality: 'Abc',
      beneficiary_name: 'Abc',
      beneficiary_place_of_work: 'Abc',
      beneficiary_nationality: 'SRI LANKA',
      beneficiary_id_no: '.',
      contact_number: '.',
      country_of_origin: '.',
      purpose: 'Reg.Transfer',
      fcy: 'DHS',
      fc_amount: 2683,
      lc_amount: 2683,
    },
    {
      id: 3,
      tran_no: 3,
      tran_date: 'dd/mm/yyyy',
      account: 'ILSHAD',
      nationality: 'Abc',
      beneficiary_name: 'Abc',
      beneficiary_place_of_work: 'Abc',
      beneficiary_nationality: 'UZBEKISTAN',
      beneficiary_id_no: '784-1999-7 59842',
      contact_number: '784-1999-7',
      country_of_origin: 'UAE',
      purpose: 'Reg.Transfer',
      fcy: 'USD',
      fc_amount: 26837,
      lc_amount: 26837,
    },
    {
      id: 4,
      tran_no: 4,
      tran_date: 'dd/mm/yyyy',
      account: 'ILSHAD',
      nationality: '.',
      beneficiary_name: '.',
      beneficiary_place_of_work: 'Abc',
      beneficiary_nationality: 'INDIA',
      beneficiary_id_no: '.',
      contact_number: '.',
      country_of_origin: '.',
      purpose: 'Reg.Transfer',
      fcy: 'EUR',
      fc_amount: 2683,
      lc_amount: 2683,
    },
  ],
};
export const dealRegisterReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      account: 'TRUST WALLET USDT',
      buy_fcy: 'dd/mm/yyyy',
      buy_fc_amount: '2,000,000',
      sell_fcy: 'USD',
      sell_fc_amount: '2,000,000',
      rate: 2.33,
      tran_no: 100,
      value_date: '9/14/2023',
      user_id: 'SHAMAL',
      date: '9/14/2023',
      time: '17:53',
    },
    {
      id: 2,
      account: 'BALTIC FUEL COMPANY',
      buy_fcy: 'dd/mm/yyyy',
      buy_fc_amount: 542452,
      sell_fcy: 'DHS',
      sell_fc_amount: 542452,
      rate: 2.34,
      tran_no: '011',
      value_date: '9/14/2023',
      user_id: 'WALEED',
      date: '9/14/2023',
      time: '17:53',
    },
    {
      id: 3,
      account: 'BALTIC FUEL COMPANY',
      buy_fcy: 'dd/mm/yyyy',
      buy_fc_amount: 2121212,
      sell_fcy: 'USD',
      sell_fc_amount: 2121212,
      rate: 2.34,
      tran_no: '022',
      value_date: '9/14/2023',
      user_id: 'SHAMAL',
      date: '9/14/2023',
      time: '17:53',
    },
    {
      id: 4,
      account: 'aBC',
      buy_fcy: 'dd/mm/yyyy',
      buy_fc_amount: ********,
      sell_fcy: 'EUR',
      sell_fc_amount: ********,
      rate: 2.33,
      tran_no: 211,
      value_date: '9/14/2023',
      user_id: 'WALEED',
      date: '9/14/2023',
      time: '17:53',
    },
  ],
};
export const accountTurnoverReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      ledger: 'Party',
      account: 'TRUST WALLET USDT',
      contact_no: '12*********',
      fcy: 'USD',
      balance_bf: '.',
      total_debit: 1234556,
      total_credit: ********,
      balance_cf: '3.******** Cr',
    },
    {
      id: 2,
      ledger: 'Walk-in',
      account: 'TRUST WALLET USDT',
      contact_no: '12*********',
      fcy: 'DHS',
      balance_bf: '20,725 Cr',
      total_debit: 1237890,
      total_credit: *********,
      balance_cf: '3.******** Cr',
    },
    {
      id: 3,
      ledger: 'General',
      account: 'BALTIC FUEL COMPANY',
      contact_no: '12*********',
      fcy: 'USD',
      balance_bf: '.',
      total_debit: 1234556,
      total_credit: 123455,
      balance_cf: '2.******** Cr',
    },
    {
      id: 4,
      ledger: 'Party',
      account: 'TRUST WALLET USDT',
      contact_no: '12*********',
      fcy: 'DHS',
      balance_bf: '2,000,000 Dr',
      total_debit: ********,
      total_credit: 67890,
      balance_cf: '72.******** Cr',
    },
    {
      id: 5,
      ledger: 'Walk-in',
      account: 'BALTIC FUEL COMPANY',
      contact_no: '12*********',
      fcy: 'USD',
      balance_bf: '.',
      total_debit: *********,
      total_credit: 12345,
      balance_cf: '2.******** Cr',
    },
    {
      id: 6,
      ledger: 'General',
      account: 'REMITTANCE REVENUE A/C',
      contact_no: '12*********',
      fcy: 'DHS',
      balance_bf: '1,835,000 Dr',
      total_debit: *********,
      total_credit: ********,
      balance_cf: '3.******** Cr',
    },
    {
      id: 7,
      ledger: 'Party',
      account: 'BALTIC FUEL COMPANY',
      contact_no: '12*********',
      fcy: 'USD',
      balance_bf: '.',
      total_debit: *********,
      total_credit: *********,
      balance_cf: '96.******** Dr',
    },
  ],
};
export const exchangeProfitLossReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      currency: 'EUR',
      opening_bal: -125007.74,
      open_rate: 3.********,
      open_in_lc: -494572.47,
      total_buy: null,
      avg_buy_rate: null,
      buy_in_lc: null,
      total_sell: null,
      avg_sell_rate: null,
      sell_in_lc: null,
      closing_bal: -125007.74,
      avg_close_lc: 3.********,
      close_in_lc: -495047.72,
      cost_of_sale: null,
      profit_loss: null,
    },
    {
      id: 2,
      currency: 'PKR',
      opening_bal: null,
      open_rate: null,
      open_in_lc: null,
      total_buy: 500000.0,
      avg_buy_rate: 75.********,
      buy_in_lc: 6611.46,
      total_sell: 366150.0,
      avg_sell_rate: 73.23,
      sell_in_lc: 5000.0,
      closing_bal: 133850.0,
      avg_close_lc: 75.********,
      close_in_lc: 1769.89,
      cost_of_sale: 4841.57,
      profit_loss: 158.43,
    },
    {
      id: 3,
      currency: 'RUB',
      opening_bal: 2931473.0,
      open_rate: 26.4039107,
      open_in_lc: 29717.29,
      total_buy: null,
      avg_buy_rate: null,
      buy_in_lc: null,
      total_sell: null,
      avg_sell_rate: null,
      sell_in_lc: null,
      closing_bal: 2931473.0,
      avg_close_lc: 26.4039107,
      close_in_lc: 111024.2,
      cost_of_sale: null,
      profit_loss: null,
    },
    {
      id: 4,
      currency: 'TMN',
      opening_bal: *********.0,
      open_rate: 19000.0,
      open_in_lc: 17565.79,
      total_buy: *********.0,
      avg_buy_rate: 16580.0,
      buy_in_lc: 20000.0,
      total_sell: *********.0,
      avg_sell_rate: 16580.0,
      sell_in_lc: 20000.0,
      closing_bal: *********.0,
      avg_close_lc: 19000.0,
      close_in_lc: 17565.79,
      cost_of_sale: null,
      profit_loss: null,
    },
  ],
};
export const accountEnquiryData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      type: 'CBS',
      number: 123,
      date: '30/10/2024',
      title_of_account: 'Account ABC',
      narration: 'Lorem Ipsum',
      debit: 123456,
      credit: null,
      fcy: 'CYN',
      fc_amount: 123456,
    },
    {
      id: 2,
      type: 'FSN',
      number: 123,
      date: '30/10/2024',
      title_of_account: 'Account ABC',
      narration: 'Emirates Bank',
      debit: null,
      credit: 123456,
      fcy: 'USD',
      fc_amount: 123456,
    },
    {
      id: 3,
      type: 'PV',
      number: 123,
      date: '30/10/2024',
      title_of_account: 'Account ABC',
      narration: 'Lorem Ipsum',
      debit: 123456,
      credit: null,
      fcy: 'EUR',
      fc_amount: 123456,
    },
    {
      id: 4,
      type: 'RV',
      number: 123,
      date: '30/10/2024',
      title_of_account: 'Account ABC',
      narration: 'Lorem Ipsum',
      debit: 123456,
      credit: 123456,
      fcy: 'DHS',
      fc_amount: 123456,
    },
    {
      id: 5,
      type: 'RV',
      number: 123,
      date: '30/10/2024',
      title_of_account: 'Account ABC',
      narration: 'Lorem Ipsum',
      debit: null,
      credit: null,
      fcy: 'CAD',
      fc_amount: 123456,
    },
  ],
};
export const financialReportData = {
  status: true,
  message: 'Journal Report listing',
  detail: [
    {
      id: 1,
      account: '4 - Revenue',
      level: 1,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: null,
    },
    {
      id: 2,
      account: '41 - Account 1 Level 2',
      level: 2,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: null,
    },
    {
      id: 3,
      account: '4101 - Account 1 Level 3',
      level: 3,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: null,
    },
    {
      id: 4,
      account: '410101 - Account 1 Level 4',
      level: 4,
      fcy: 'DHS',
      fc_debit: null,
      fc_credit: ********,
      lc_debit: null,
      lc_credit: ********,
    },
    {
      id: 5,
      account: '******** - Account 1 Level 5',
      level: 5,
      fcy: 'SAR',
      fc_debit: 1835000,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 6,
      account: '******** - Account 1 Level 5',
      level: 5,
      fcy: 'EUR',
      fc_debit: null,
      fc_credit: 1835000,
      lc_debit: null,
      lc_credit: 1835000,
    },
    {
      id: 7,
      account: '******** - Account 2 Level 5',
      level: 5,
      fcy: 'SAR',
      fc_debit: 1835000,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 8,
      account: '110101 - Account 1 Level 4 Total',
      level: 4,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: 1835000,
    },
    {
      id: 9,
      account: '110101 - Account 2 Level 4',
      level: 4,
      fcy: 'DHS',
      fc_debit: 1835000,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 10,
      account: '1101 - Account 1 Level 3 Total',
      level: 3,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 11,
      account: '11 - Account 1 Level 2 Total',
      level: 2,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 12,
      account: '4 - Revenue Total',
      level: 1,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 13,
      account: '5 - Expenses',
      level: 1,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: null,
    },
    {
      id: 14,
      account: '51 - Account 1 Level 2',
      level: 2,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: null,
    },
    {
      id: 15,
      account: '5101 - Account 1 Level 3',
      level: 3,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: null,
    },
    {
      id: 16,
      account: '510101 - Account 1 Level 4',
      fcy: 'DHS',
      fc_debit: null,
      fc_credit: ********,
      lc_debit: null,
      lc_credit: ********,
    },
    {
      id: 17,
      account: '******** - Account 1 Level 5',
      level: 5,
      fcy: 'SAR',
      fc_debit: 1835000,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 18,
      account: '******** - Account 1 Level 5',
      level: 5,
      fcy: 'EUR',
      fc_debit: null,
      fc_credit: 1835000,
      lc_debit: null,
      lc_credit: 1835000,
    },
    {
      id: 19,
      account: '******** - Account 2 Level 5',
      level: 5,
      fcy: 'SAR',
      fc_debit: 1835000,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 20,
      account: '210101 - Account 1 Level 4 Total',
      level: 4,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: null,
      lc_credit: 1835000,
    },
    {
      id: 21,
      account: '210101 - Account 2 Level 4',
      level: 4,
      fcy: 'DHS',
      fc_debit: 1835000,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 22,
      account: '5101 - Account 1 Level 3 Total',
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 23,
      account: '51 - Account 1 Level 2 Total',
      level: 2,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
    {
      id: 24,
      account: '5 - Expenses Total',
      level: 1,
      fcy: null,
      fc_debit: null,
      fc_credit: null,
      lc_debit: 1835000,
      lc_credit: null,
    },
  ],
};

// Transactions
export const MOCK_ACCOUNT_BALANCES = {
  account1: {
    name: 'Account ABC',
    balances: [
      { currency: 'DHS', amount: '210,212.00', type: 'Dr.', color: '#22C55E' },
      { currency: 'USD', amount: '45,000.00', type: 'Cr.', color: '#EF4444' },
    ],
  },
  account2: {
    name: 'Account DEF',
    balances: [
      { currency: 'DHS', amount: '180,500.00', type: 'Dr.', color: '#22C55E' },
      { currency: 'EUR', amount: '32,000.00', type: 'Cr.', color: '#EF4444' },
    ],
  },
};


export const MOCK_ACCOUNT_BALANCES_MODE = {
  account1: {
    name: 'Account ABC',
    balances: [
      { currency: 'DHS', amount: '210,212.00', type: 'Dr.', color: '#22C55E' },
      { currency: 'USD', amount: '45,000.00', type: 'Cr.', color: '#EF4444' },
    ],
  },
  account2: {
    name: 'Account DEF',
    balances: [
      { currency: 'DHS', amount: '180,500.00', type: 'Dr.', color: '#22C55E' },
      { currency: 'EUR', amount: '32,000.00', type: 'Cr.', color: '#EF4444' },
    ],
  },
};



export const outwardRemittanceData = {
  reference_no: '*********',
  ledger: 'party',
  account: 'Account Abc',
  beneficiary: 'Beneficiary Abc',
  address: 'Lorem ipsum dolor sit am',
  nationality: 'Country ABC',
  bank_details: {
    bank_name: 'Bank ABC',
    bank_ac: '************',
    swift_code: '1122343',
    routing_number: '1234555',
    city: 'City B',
    country: 'Country A',
    corresponding_bank: 'Bank A',
    bank_account_number: '11223431122343112234311',
    swift_bic_code: '1122343',
  },
  purpose: 'Purpose A',
  by_order: 'Enter By Order',
  send_fc: 'USD',
  against: 'EUR',
  send_amount: {
    currency: 'Currency A',
    amount: 2000.0,
  },
  rate: 4.0,
  against_amount: {
    currency: 'Currency A',
    amount: 8000.0,
  },
  charges: {
    currency: 'Currency A',
    amount: 800.0,
  },
  vat: {
    terms: 'Standard Rates (5.00%)',
    amount: {
      currency: 'Currency A',
      value: 0.0,
    },
  },
  net_total: {
    currency: 'Currency A',
    amount: 8800.0,
  },
  base_rate: 2.0,
  lcy_amount: {
    currency: 'Currency A',
    amount: 17600.0,
  },
  settle_thru: 'oN A/C',
};
// Wireframes
// https://j8a2xn.axshare.com

export const MOCK_CURRENT_ACCOUNT = {
  name: 'Current Account',
  balances: [
    { currency: 'DHS', amount: '210,212.00', type: 'Dr.', color: '#22C55E' },
    { currency: 'USD', amount: '45,000.00', type: 'Cr.', color: '#EF4444' },
  ],
};
export const MOCK_SAVINGS_ACCOUNT = {
  name: 'Savings Account',
  balances: [
    { currency: 'DHS', amount: '180,500.00', type: 'Dr.', color: '#22C55E' },
    { currency: 'EUR', amount: '32,000.00', type: 'Cr.', color: '#EF4444' },
  ],
};
export const MOCK_EXCHANGE_RATES = [
  { currency: 'DHS', rate: '2.22', change: '+2%', isPositive: true },
  { currency: 'USD', rate: '45,000.00', change: '-2%', isPositive: false },
  { currency: 'EUR', rate: '1.85', change: '+1.5%', isPositive: true },
  { currency: 'GBP', rate: '2.15', change: '-0.5%', isPositive: false },
];
export const MOCK_EXCHANGE_RATES_NEW = [
  { currency: 'TMN', net_total: '2,222,000,000.00' },
  { currency: 'USD', net_total: '2,222,000,000.00' },
  { currency: 'DHS', net_total: '2,222,000,000.00' },
  { currency: 'USD', net_total: '2,222,000,000.00' },
  { currency: 'PKR', net_total: '2,222,000,000.00' },
  { currency: 'EUR', net_total: '2,222,000,000.00' },
];
export const MOCK_TRANSACTIONS = {
  deposit: {
    '03': {
      transactionType: 'Deposit',
      chequeNumber: '********',
      fromAccount: 'Account Abc',
      toAccount: 'Account Abc',
      currency: 'DHS',
      amount: '10,000',
      narration:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo',
    },
  },
  withdrawal: {
    '03': {
      transactionType: 'Withdrawal',
      chequeNumber: '********',
      fromAccount: 'Account Xyz',
      toAccount: 'Account Abc',
      currency: 'DHS',
      amount: '15,000',
      narration: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    },
  },
  inward_tt: {
    '03': {
      transactionType: 'Inward TT',
      bank: 'Bank A',
      fromAccount: 'Party Account Abc',
      chequeNumber: '********',
      currency: 'DHS',
      amount: '10,000',
      commissionType: 'Commission Income',
      commissionPercentage: '10',
      commissionAmount: '1000',
      country: 'Country A',
      narration:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo',
    },
  },
};
export const MOCK_DEPOSIT_DATA = [
  {
    id: 1,
    date: '20-03-2024',
    bdv_no: '20',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'Yes',
  },
  {
    id: 2,
    date: '20-03-2024',
    bdv_no: '22',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'No',
  },
  // Add more mock data entries as needed
];
export const MOCK_WITHDRAWAL_DATA = [
  {
    id: 1,
    date: 'DD/MM/YYYY',
    bwv_no: '20',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'Yes',
  },
  {
    id: 2,
    date: 'DD/MM/YYYY',
    bwv_no: '22',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'No',
  },
  {
    id: 3,
    date: 'DD/MM/YYYY',
    bwv_no: '24',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'Yes',
  },
  {
    id: 4,
    date: 'DD/MM/YYYY',
    bwv_no: '26',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'No',
  },
];
export const MOCK_INWARD_TT_DATA = [
  {
    id: 1,
    date: 'DD/MM/YYYY',
    bittv_no: '20',
    bank: 'Bank A',
    ledger: 'WIC',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    fc_commission: '100',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'Yes',
  },
  {
    id: 2,
    date: 'DD/MM/YYYY',
    bittv_no: '22',
    bank: 'Bank A',
    ledger: 'PL',
    from_account: 'Account ABC',
    to_account: 'Account ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    fc_commission: '100',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: 'No',
  },
  // Add more mock data as needed
];
export const MOCK_PDCR_DATA = [
  {
    id: 1,
    pdcr_party: 'TSM',
    cheque_number: '123456',
    due_date: 'dd/mm/yyyy',
    currency: 'DHS',
    fc_amount: '15000',
    pdcr_bank: 'HSBC',
  },
  {
    id: 2,
    pdcr_party: 'TSM',
    cheque_number: '123456',
    due_date: 'dd/mm/yyyy',
    currency: 'DHS',
    fc_amount: '15000',
    pdcr_bank: 'HSBC',
  },
  {
    id: 3,
    pdcr_party: 'TSM',
    cheque_number: '123456',
    due_date: 'dd/mm/yyyy',
    currency: 'DHS',
    fc_amount: '15000',
    pdcr_bank: 'HSBC',
  },
];
export const MOCK_SEARCH_RESULT = {
  ppvNumber: '05',
  issuedTo: {
    type: 'General',
    account: 'Account Abc',
  },
  narration:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo',
  accountBalance: {
    name: 'Account ABC',
    balances: [
      { currency: 'DHS', amount: '28,030.00', type: 'Dr', color: '#22C55E' },
      { currency: 'USD', amount: '45,000.00', type: 'Cr', color: '#EF4444' },
    ],
  },
  pdcrDetails: {
    pdcr_party: 'TSM',
    cheque_number: '123456',
    due_date: 'dd/mm/yyyy',
    currency: 'DHS',
    fc_amount: '15000',
    pdcr_bank: 'HSBC',
  },
};
export const MOCK_SEARCH_TABLE_DATA = [
  {
    date: 'DD/MM/YYYY',
    ppv_no: '20',
    ledger: 'WIC',
    pdcr_party: 'ABC',
    pdcr_bank: 'HSBC',
    cheque_number: '123456',
    due_date: 'DD/MM/YYYY',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: true,
  },
  {
    date: 'DD/MM/YYYY',
    ppv_no: '22',
    ledger: 'PL',
    pdcr_party: 'ABC',
    pdcr_bank: 'HSBC',
    cheque_number: '123456',
    due_date: 'DD/MM/YYYY',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: false,
  },
  {
    date: 'DD/MM/YYYY',
    ppv_no: '24',
    ledger: 'GL',
    pdcr_party: 'ABC',
    pdcr_bank: 'HSBC',
    cheque_number: '123456',
    due_date: 'DD/MM/YYYY',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: true,
  },
  {
    date: 'DD/MM/YYYY',
    ppv_no: '26',
    ledger: 'WIC',
    pdcr_party: 'ABC',
    pdcr_bank: 'HSBC',
    cheque_number: '123456',
    due_date: 'DD/MM/YYYY',
    fcy: 'DHS',
    fc_amount: '1000',
    lc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    has_attachment: false,
  },
];
export const MOCK_OUTWARD_REMITTANCE_DATA = [
  {
    id: 1,
    buy_fcy: 'DHS',
    fsn_no: 20,
    debit_ledger: 'GL',
    debit_account: 'Account ABC',
    reference_no: 1000,
    beneficiary: 'Abc',
    sending_fc_amount: 1000,
    against_amount: 1000,
    charges: 123,
    vat: 123,
    net_total: 1000,
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    id: 2,
    buy_fcy: 'DHS',
    fsn_no: 22,
    debit_ledger: 'WIC',
    debit_account: 'Account ABC',
    reference_no: 1000,
    beneficiary: 'Abc',
    sending_fc_amount: 1000,
    against_amount: 1000,
    charges: 123,
    vat: 123,
    net_total: 1000,
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'No',
  },
  {
    id: 3,
    buy_fcy: 'DHS',
    fsn_no: 24,
    debit_ledger: 'WIC',
    debit_account: 'Account ABC',
    reference_no: 1000,
    beneficiary: 'Abc',
    sending_fc_amount: 1000,
    against_amount: 1000,
    charges: 123,
    vat: 123,
    net_total: 1000,
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    id: 4,
    buy_fcy: 'DHS',
    fsn_no: 26,
    debit_ledger: 'PL',
    debit_account: 'Account ABC',
    reference_no: 1000,
    beneficiary: 'Abc',
    sending_fc_amount: 1000,
    against_amount: 1000,
    charges: 123,
    vat: 123,
    net_total: 1000,
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'No',
  },
];
export const MOCK_OUTWARD_REMITTANCE_REGISTER_DATA = [
  {
    id: 1,
    fsn_number: '',
    date: 'mm/dd/yy',
    account_name: 'Account ABC',
    beneficiary: 'Abc',
    fcy: 'USD',
    fc_amount: 500000.0,
    ag_fcy: 'EUR',
    ag_fcy_amount: 283938.1,
    against_tt: '1000',
    fbn_account_name: 'Account ABC',
    fc_payment_amount: 500000.0,
    fc_balance_amount: 500000.0,
    pay_from_account: 'Account ABC',
    office_location: 'Office ABC',
    doc_swift: 'Not received',
    confirmation_status: 'Not confirmed',
    approved_by: 'Admin',
    comment: null,
    action: ['Post'],
  },
  {
    id: 2,
    fsn_number: '',
    date: 'mm/dd/yy',
    account_name: 'Account ABC',
    beneficiary: 'Abc',
    fcy: 'USD',
    fc_amount: 500000.0,
    ag_fcy: 'EUR',
    ag_fcy_amount: 283938.1,
    against_tt: '1000',
    fbn_account_name: 'Account ABC',
    fc_payment_amount: 500000.0,
    fc_balance_amount: 500000.0,
    pay_from_account: 'Account ABC',
    office_location: 'Office ABC',
    doc_swift: 'Not received',
    confirmation_status: 'Not confirmed',
    approved_by: null,
    action: ['Approve'],
  },
  {
    id: 3,
    fsn_number: '',
    date: 'mm/dd/yy',
    account_name: 'Account ABC',
    beneficiary: 'Abc',
    fcy: 'USD',
    fc_amount: 500000.0,
    ag_fcy: 'EUR',
    ag_fcy_amount: 283938.1,
    against_tt: '1000',
    fbn_account_name: 'Account ABC',
    fc_payment_amount: 500000.0,
    fc_balance_amount: 500000.0,
    pay_from_account: 'Account ABC',
    office_location: 'Office ABC',
    doc_swift: 'Received',
    confirmation_status: 'Confirmed',
    approved_by: 'Admin',
    comment: 'lorem ipsum dolor sit amet',
    action: ['Hold', 'Post'],
  },
  {
    id: 4,
    fsn_number: '',
    date: 'mm/dd/yy',
    account_name: 'Account ABC',
    beneficiary: 'Abc',
    fcy: 'USD',
    fc_amount: 500000.0,
    ag_fcy: 'EUR',
    ag_fcy_amount: 283938.1,
    against_tt: '1000',
    fbn_account_name: 'Account ABC',
    fc_payment_amount: 500000.0,
    fc_balance_amount: 500000.0,
    pay_from_account: 'Account ABC',
    office_location: 'Office ABC',
    doc_swift: 'Received',
    confirmation_status: 'Confirmed',
    approved_by: 'Admin',
    comment: null,
    action: ['Post'],
  },
];

export const MOCK_APPLICATION_PRINTING_DATA = [
  {
    id: 1,
    fsn_number: 1,
    date: 'mm/dd/yy',
    amount: 500000.0,
    fcy: 'USD',
    beneficiary: 'Beneficiary ABC',
    fc_amount: 500000.0,
    account_name: 'Party ABC',
    account_number: '1234656',
    status: 'Printed',
  },
  {
    id: 2,
    fsn_number: 2,
    date: 'mm/dd/yy',
    amount: 500000.0,
    fcy: 'USD',
    beneficiary: 'Beneficiary ABC',
    fc_amount: 500000.0,
    account_name: 'Party ABC',
    account_number: '1234656',
    status: 'Printed',
  },
  {
    id: 3,
    fsn_number: 3,
    date: 'mm/dd/yy',
    amount: 500000.0,
    fcy: 'USD',
    beneficiary: 'Beneficiary ABC',
    fc_amount: 500000.0,
    account_name: 'Party ABC',
    account_number: '1234656',
    status: 'Not Printed',
  },
  {
    id: 4,
    fsn_number: 1,
    date: 'mm/dd/yy',
    amount: 500000.0,
    fcy: 'USD',
    beneficiary: 'Beneficiary ABC',
    fc_amount: 500000.0,
    account_name: 'Party ABC',
    account_number: '1234656',
    status: 'Not Printed',
  },
];

export const MOCK_TTR_REGISTER_BANK_DETAILS_DATA = [
  {
    id: 1,
    date: 'mm/dd/yy',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: '',
    amount: '500000.0',
    account_name: 'Party ABC',
    allocated: '100000.0',
    unallocated: '400000.0',
    confirmed: '100000.0',
    unconfirmed: '400000.0',
    user_id: 'ABC',
  },
  {
    id: 2,
    date: 'mm/dd/yy',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: '',
    amount: '500000.0',
    account_name: 'Party ABC',
    allocated: '100000.0',
    unallocated: '400000.0',
    confirmed: '100000.0',
    unconfirmed: '400000.0',
    user_id: 'ABC',
  },
  {
    id: 3,
    date: 'mm/dd/yy',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: '',
    amount: '500000.0',
    account_name: 'Party ABC',
    allocated: '100000.0',
    unallocated: '400000.0',
    confirmed: '100000.0',
    unconfirmed: '400000.0',
    user_id: 'ABC',
  },
  {
    id: 4,
    date: 'mm/dd/yy',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: 'Lorem ipsum dolor sit amet',
    amount: '500000.0',
    account_name: 'Party ABC',
    allocated: '100000.0',
    unallocated: '400000.0',
    confirmed: '100000.0',
    unconfirmed: '400000.0',
    user_id: 'ABC',
  },
];

export const MOCK_TTR_REGISTER_ALLOCATION_DATA = [
  {
    id: 1,
    date: '20-03-2024',
    debit_party: 'ABC Party',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: 'Sample remarks',
    allocated: '80,000,000',
    confirmed: '60,000,000',
    unconfirmed: '20,000,000',
  },
  {
    id: 2,
    date: '20-03-2024',
    debit_party: 'ABC Party',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: 'Sample remarks',
    allocated: '60,000,000',
    confirmed: '40,000,000',
    unconfirmed: '20,000,000',
  },
  {
    id: 3,
    date: '20-03-2024',
    debit_party: 'ABC Party',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: 'Sample remarks',
    allocated: '40,000,000',
    confirmed: '20,000,000',
    unconfirmed: '20,000,000',
  },
];

export const MOCK_TTR_REGISTER_CONFIRMATION_DATA = [
  {
    id: 1,
    date: '20-03-2024',
    debit_party: 'XYZ Corp',
    credit_party: 'ABC Trading',
    bank_name: 'ABC Bank',
    bank_account: '1234656',
    remarks: 'Payment for services',
    allocated: '75,000,000',
    confirmed: '60,000,000',
    unconfirmed: '15,000,000',
    status: 'Pending',
  },
  {
    id: 2,
    date: '2024-03-19',
    debit_party: 'DEF Limited',
    credit_party: 'GHI Services',
    bank_name: 'DEF Bank',
    bank_account: '7891011',
    remarks: 'Monthly transfer',
    allocated: '120,000,000',
    confirmed: '100,000,000',
    unconfirmed: '20,000,000',
    status: 'Confirmed',
  },
  {
    id: 3,
    date: '2024-03-18',
    debit_party: 'JKL Industries',
    credit_party: 'MNO Enterprises',
    bank_name: 'JKL Bank',
    bank_account: '4567890',
    remarks: 'Contract payment',
    allocated: '95,000,000',
    confirmed: '80,000,000',
    unconfirmed: '15,000,000',
    status: 'Pending',
  },
];

export const MOCK_A2A_TABLE_DATA = [
  {
    id: '20',
    date: 'DD/MM/YYYY',
    debitLedger: 'GL',
    debitAccount: 'Account ABC',
    creditLedger: 'GL',
    creditAccount: 'Account ABC',
    currency: 'DHS',
    fcAmount: '1000',
    lcAmount: '1000',
    commission: '50',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'Yes',
  },
  {
    id: '22',
    date: 'DD/MM/YYYY',
    debitLedger: 'WIC',
    debitAccount: 'Account ABC',
    creditLedger: 'WIC',
    creditAccount: 'Account ABC',
    currency: 'DHS',
    fcAmount: '1000',
    lcAmount: '1000',
    commission: '50',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'No',
  },
  {
    id: '24',
    date: 'DD/MM/YYYY',
    debitLedger: 'WIC',
    debitAccount: 'Account ABC',
    creditLedger: 'WIC',
    creditAccount: 'Account ABC',
    currency: 'DHS',
    fcAmount: '1000',
    lcAmount: '1000',
    commission: '50',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'Yes',
  },
  {
    id: '26',
    date: 'DD/MM/YYYY',
    debitLedger: 'PL',
    debitAccount: 'Account ABC',
    creditLedger: 'PL',
    creditAccount: 'Account ABC',
    currency: 'DHS',
    fcAmount: '1000',
    lcAmount: '1000',
    commission: '50',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'No',
  },
];

export const MOCK_FCD_TABLE_DATA = [
  {
    id: '20',
    date: 'DD/MM/YYYY',
    debitLedger: 'GL',
    debitAccount: 'Account ABC',
    creditLedger: 'GL',
    creditAccount: 'Account ABC',
    buyFCy: 'DHS',
    buyFCAmount: '1000',
    rate: '10.********',
    sellFCy: 'DHS',
    sellFCAmount: '1000',
    commissionFCy: 'DHS',
    commissionAmount: '1000',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'Yes',
  },
  {
    id: '22',
    date: 'DD/MM/YYYY',
    debitLedger: 'WIC',
    debitAccount: 'Account ABC',
    creditLedger: 'WIC',
    creditAccount: 'Account ABC',
    buyFCy: 'DHS',
    buyFCAmount: '1000',
    rate: '10.********',
    sellFCy: 'DHS',
    sellFCAmount: '1000',
    commissionFCy: 'DHS',
    commissionAmount: '1000',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'No',
  },
  {
    id: '24',
    date: 'DD/MM/YYYY',
    debitLedger: 'WIC',
    debitAccount: 'Account ABC',
    creditLedger: 'WIC',
    creditAccount: 'Account ABC',
    buyFCy: 'DHS',
    buyFCAmount: '1000',
    rate: '10.********',
    sellFCy: 'DHS',
    sellFCAmount: '1000',
    commissionFCy: 'DHS',
    commissionAmount: '1000',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'Yes',
  },
  {
    id: '26',
    date: 'DD/MM/YYYY',
    debitLedger: 'PL',
    debitAccount: 'Account ABC',
    creditLedger: 'PL',
    creditAccount: 'Account ABC',
    buyFCy: 'DHS',
    buyFCAmount: '1000',
    rate: '10.********',
    sellFCy: 'DHS',
    sellFCAmount: '1000',
    commissionFCy: 'DHS',
    commissionAmount: '1000',
    userId: 'ABC',
    time: 'hh:mm',
    hasAttachment: 'No',
  },
];

export const MOCK_SUSPENSE_VOUCHER_DATA = {
  ledger: { label: 'GL', value: 'gl' },
  account: { label: 'Account ABC', value: 'a1' },
  office: { label: 'Office ABC', value: 'o1' },
  currency: { label: 'DHS', value: 'dhs' },
  tableData: [
    {
      id: 1,
      narration: 'Lorem ipsum',
      debit: 3000,
      credit: '',
      status: 'Approved',
    },
    {
      id: 2,
      narration: 'Lorem ipsum dolor sit amet.',
      debit: 3000,
      credit: 3000,
      status: 'Settled',
    },
    {
      id: 3,
      narration: 'Lorem ipsum dolor sit amet',
      debit: 3000,
      credit: '',
      status: 'Open',
    },
  ],
};

export const MOCK_SUSPENSE_VOUCHERS_TABLE_DATA = [
  {
    id: 1,
    date: 'DD/MM/YYYY',
    svr_no: 20,
    ledger: 'GL',
    account_name: 'ABC',
    fcy: 'DHS',
    debit_amount: 1000,
    credit_amount: 1000,
    status: 'Open',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    id: 2,
    date: 'DD/MM/YYYY',
    svr_no: 22,
    ledger: 'PL',
    account_name: 'ABC',
    fcy: 'DHS',
    debit_amount: 1000,
    credit_amount: 1000,
    status: 'Approved',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'No',
  },
  {
    id: 3,
    date: 'DD/MM/YYYY',
    svr_no: 24,
    ledger: 'WIC',
    account_name: 'ABC',
    fcy: 'DHS',
    debit_amount: 1000,
    credit_amount: 1000,
    status: 'Settled',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    id: 4,
    date: 'DD/MM/YYYY',
    svr_no: 26,
    ledger: 'PL',
    account_name: 'ABC',
    fcy: 'DHS',
    debit_amount: 1000,
    credit_amount: 1000,
    status: 'Settled',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'No',
  },
];

export const MOCK_SUSPENSE_POSTING_DATA = [
  {
    id: 1,
    svr_no: 2,
    date: 'dd/mm/yyyy',
    account: 'Habib Bank',
    currency: 'DHS',
    narration: 'Unknown Amount',
    debit: 15000,
    credit: 0,
    sjv_no: null,
    posted_account: null,
    approved_by: 'Hashim',
    action: ['Approve'],
  },
  {
    id: 2,
    svr_no: 2,
    date: 'dd/mm/yyyy',
    account: 'Habib Bank',
    currency: 'DHS',
    narration: 'Unknown Amount',
    debit: 15000,
    credit: 0,
    sjv_no: 12,
    posted_account: 'Account ABC',
    approved_by: 'Hashim',
    action: ['Hold', 'Post'],
  },
  {
    id: 3,
    svr_no: 2,
    date: 'dd/mm/yyyy',
    account: 'Habib Bank',
    currency: 'DHS',
    narration: 'Unknown Amount',
    debit: 15000,
    credit: 0,
    sjv_no: null,
    posted_account: null,
    approved_by: null,
    action: ['Cancel Posting'],
  },
];

export const MOCK_ALLOCATION_TABLE_DATA = [
  {
    id: '1',
    accountName: 'Account ABC',
    amount: '117278.00',
    docType: 'HAVALE',
    number: '1234',
    bank: 'Bank ABC',
    code: 'ABC123',
    city: 'Emirates',
    description: 'First allocation',
  },
  {
    id: '2',
    accountName: 'Account ABC',
    amount: '117278.00',
    docType: 'HAVALE',
    number: '1235',
    bank: 'Bank ABC',
    code: 'ABC124',
    city: 'Emirates',
    description: 'Second allocation',
  },
];
// Update the mock table data with complete information
export const MOCK_TMN_TABLE_DATA = [
  {
    id: '20',
    date: '15/03/2024',
    type: 'Buy',
    mode: 'Regular',
    account: {
      type: 'Party',
      name: 'Account ABC',
    },
    ledgerName: 'GL',
    accountName: 'Account ABC',
    beneficiary: 'Beneficiary ABC',
    bank: 'Bank ABC',
    bankAccount: '**********',
    city: 'Emirates',
    purpose: 'Purpose ABC',
    buyFCy: 'DHS',
    buyFCAmount: '1000.00',
    rate: '10.********',
    rateType: {
      type: 'X',
      rate: '10.********',
    },
    sellFCy: 'TMN',
    sellFCAmount: '10123.45',
    commissionFCy: 'DHS',
    commissionAmount: '10.00',
    vatAmount: '0.50',
    fcNetTotal: '1010.50',
    agFCy: {
      currency: 'TMN',
      amount: '10123.45',
    },
    total: {
      currency: 'DHS',
      amount: '1000.00',
    },
    receivableComm: '1.0000% Receivable Comm of DHS 10.00 on DHS 1,000',
    userId: 'ABC123',
    time: '14:30',
    hasAttachment: 'Yes',
  },
  {
    id: '22',
    date: '15/03/2024',
    type: 'Sell',
    mode: 'Regular',
    account: {
      type: 'Party',
      name: 'Account XYZ',
    },
    ledgerName: 'WIC',
    accountName: 'Account XYZ',
    beneficiary: 'Beneficiary XYZ',
    bank: 'Bank XYZ',
    bankAccount: '09********',
    city: 'Dubai',
    purpose: 'Purpose XYZ',
    buyFCy: 'TMN',
    buyFCAmount: '20000.00',
    rate: '9.********',
    rateType: {
      type: 'Y',
      rate: '9.********',
    },
    sellFCy: 'DHS',
    sellFCAmount: '2025.25',
    commissionFCy: 'DHS',
    commissionAmount: '20.25',
    vatAmount: '1.01',
    fcNetTotal: '2046.51',
    agFCy: {
      currency: 'DHS',
      amount: '2025.25',
    },
    total: {
      currency: 'TMN',
      amount: '20000.00',
    },
    receivableComm: '1.0000% Receivable Comm of DHS 20.25 on DHS 2,025.25',
    userId: 'XYZ789',
    time: '15:45',
    hasAttachment: 'No',
  },
];

export const MOCK_CURRENCY_TRANSFER_DATA = [
  {
    date: 'DD/MM/YYYY',
    trq_no: '20',
    debit_ledger: 'GL',
    debit_account: 'Account ABC',
    credit_ledger: 'GL',
    credit_account: 'Account ABC',
    doc_type: 'Doc A',
    doc_no: '12345',
    bank: 'Bank A',
    city: 'City A',
    fcy: 'DHS',
    fc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    date: 'DD/MM/YYYY',
    trq_no: '22',
    debit_ledger: 'WIC',
    debit_account: 'Account ABC',
    credit_ledger: 'WIC',
    credit_account: 'Account ABC',
    doc_type: 'Doc A',
    doc_no: '12345',
    bank: 'Bank A',
    city: 'City A',
    fcy: 'DHS',
    fc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'No',
  },
  {
    date: 'DD/MM/YYYY',
    trq_no: '24',
    debit_ledger: 'WIC',
    debit_account: 'Account ABC',
    credit_ledger: 'WIC',
    credit_account: 'Account ABC',
    doc_type: 'Doc A',
    doc_no: '12345',
    bank: 'Bank A',
    city: 'City A',
    fcy: 'DHS',
    fc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    date: 'DD/MM/YYYY',
    trq_no: '26',
    debit_ledger: 'PL',
    debit_account: 'Account ABC',
    credit_ledger: 'PL',
    credit_account: 'Account ABC',
    doc_type: 'Doc A',
    doc_no: '12345',
    bank: 'Bank A',
    city: 'City A',
    fcy: 'DHS',
    fc_amount: '1000',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'No',
  },
];

export const MOCK_CURRENCY_TRANSFER_VIEW_DATA = {
  debitAccount: {
    party: 'Party',
    label: 'Account ABC',
    value: 'a1',
  },
  creditAccount: {
    party: 'Party',
    label: 'Account ABC',
    value: 'a1',
  },
  accountTitle: {
    label: 'Show',
    value: 'show',
  },
  tableData: [
    {
      id: 1,
      currency: 'DHS',
      amount: '24444.00',
      narration: 'Lorem',
      docType: 'Doc A',
      docNo: '12122',
      bank: 'Bank A',
      city: 'City A',
      code: '',
      action: '↗',
      status: 'Approved',
    },
    {
      id: 2,
      currency: 'DHS',
      amount: '24444.00',
      narration: 'Lorem',
      docType: 'Doc A',
      docNo: '12122',
      bank: 'Bank A',
      city: 'City A',
      code: '',
      action: '↗',
      status: 'Settled',
    },
  ],
};

export const MOCK_DEAL_REGISTRY_DATA = [
  {
    id: 1,
    account: 'Account A',
    buy: '234',
    sell: '',
    ag_fcy: 'USD',
    ag_fc_amt: '500',
    rate: '232.********',
    user_id: 'Hashim',
    convert_rate: '2344********',
    trans_no: 'TBN7',
    value_date: 'dd/mm/yyyy',
    description: 'Lorem Ipsum',
  },
  {
    id: 2,
    account: 'Account A',
    buy: '234',
    sell: '',
    ag_fcy: 'USD',
    ag_fc_amt: '500',
    rate: '232.********',
    user_id: 'Hashim',
    convert_rate: '************',
    trans_no: 'TBN7',
    value_date: 'dd/mm/yyyy',
    description: 'Lorem Ipsum',
  },
  {
    id: 3,
    account: 'Account A',
    buy: '',
    sell: '345',
    ag_fcy: 'DHS',
    ag_fc_amt: '1000',
    rate: '32.********',
    user_id: 'Hashim',
    convert_rate: '2344********',
    trans_no: 'TSN 13',
    value_date: 'dd/mm/yyyy',
    description: 'Lorem Ipsum',
  },
  {
    id: 4,
    account: 'Account A',
    buy: '',
    sell: '345',
    ag_fcy: 'DHS',
    ag_fc_amt: '500',
    rate: '32.********',
    user_id: 'Hashim',
    convert_rate: '2344********',
    trans_no: 'TSN 13',
    value_date: 'dd/mm/yyyy',
    description: 'Lorem Ipsum',
  },
];

export const MOCK_SUMMARY_TABLE_DATA = [
  {
    id: 1,
    type: 'Opening',
    fcAmount: '*********.00',
    rate: '1234.********',
    baseValue: '78078.90',
  },
  {
    id: 2,
    type: 'Total Buy',
    fcAmount: '*********.00',
    rate: '1234.********',
    baseValue: '78078.90',
  },
  {
    id: 3,
    type: 'Total Sell',
    fcAmount: '*********.00',
    rate: '1234.********',
    baseValue: '78078.90',
  },
  {
    id: 4,
    type: 'Closing',
    fcAmount: '*********.00',
    rate: '1234.********',
    baseValue: '78078.90',
  },
];

export const MOCK_POSITION_SUMMARY_DATA = [
  {
    id: 1,
    currency: 'DHS',
    currencyName: 'AE Dirhams',
    fcOpening: 4600.0,
    fcBuy: 25000.0,
    fcSell: null,
    fcClosing: 2375369.22,
    avgClosingRate: 1.0,
  },
  {
    id: 2,
    currency: 'USD',
    currencyName: 'United States Dollars',
    fcOpening: -2838.23,
    fcBuy: null,
    fcSell: 7770.97,
    fcClosing: 38783.0,
    avgClosingRate: 238434.9,
  },
  {
    id: 3,
    currency: 'TMN',
    currencyName: 'Iranian Toman',
    fcOpening: 3456.88,
    fcBuy: 2500000.0,
    fcSell: 20962682.22,
    fcClosing: 34829.0,
    avgClosingRate: 238434.9,
  },
  {
    id: 4,
    currency: 'EUR',
    currencyName: 'Euro Member Countries',
    fcOpening: -45789.0,
    fcBuy: null,
    fcSell: null,
    fcClosing: 21129273.8,
    avgClosingRate: 238434.9,
  },
];

export const MOCK_BENEFICIARY_DATA = [
  {
    id: 1,
    name: 'User ABC',
    address: 'Lorem Ipsum Lorem Ipsum Lorem',
    telephoneNumber: '********9',
    mobileNumber: '********9',
    idType: 'Emirates ID',
    idNumber: '********9',
    expiryDate: 'dd/mm/yyyy',
  },
  {
    id: 2,
    name: 'User XYZ',
    address: 'Lorem Ipsum Lorem Ipsum Lorem',
    telephoneNumber: '********9',
    mobileNumber: '********9',
    idType: 'Emirates ID',
    idNumber: '********9',
    expiryDate: 'dd/mm/yyyy',
  },
  {
    id: 3,
    name: 'User ABC',
    address: 'Lorem Ipsum Lorem Ipsum Lorem',
    telephoneNumber: '********9',
    mobileNumber: '********9',
    idType: 'Emirates ID',
    idNumber: '********9',
    expiryDate: 'dd/mm/yyyy',
  },
];

export const MOCK_SUMMARY_DATA = [
  {
    currency: 'DHS',
    total: '123.00',
    commission: '100',
    vatAmount: '5.00',
    netTotal: '286.00',
  },
];

export const MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA = {
  debitAccount: {
    party: 'Walk in customer',
    label: 'Account Abc',
    value: 'acc1',
  },
  office: 'Syria',
  vatType: 'Charge',
  vatTerms: 'Standard Rates (5.00%)',
  tableData: [
    {
      id: 1,
      refNo: '1',
      payType: 'Cash payment',
      beneficiary: 'User A',
      sender: 'Abc',
      idNumber: '123457',
      contactNo: '********',
      currency: 'DHS',
      fcAmount: '123',
      commission: '123',
      payDate: 'dd/mm/yyyy',
      bankName: 'Bank A',
      bankAccount: '**********',
      narration: 'Lorem Ipsum',
      status: 'Pending',
    },
    {
      id: 2,
      refNo: '2',
      payType: 'Cash payment',
      beneficiary: 'User A',
      sender: 'Abc',
      idNumber: '123457',
      contactNo: '********',
      currency: 'DHS',
      fcAmount: '123',
      commission: '123',
      payDate: 'dd/mm/yyyy',
      bankName: 'Bank A',
      bankAccount: '**********',
      narration: 'Lorem Ipsum',
      status: 'Pending',
    },
  ],
};

export const MOCK_INWARD_PAYMENT_ORDER_DATA = [
  {
    date: 'DD/MM/YYYY',
    dbv_no: '20',
    debit_ledger: 'GL',
    debit_account: 'Account ABC',
    reference_no: '12345',
    pay_type: 'Cash Payment',
    pay_date: 'dd/mm/yyyy',
    beneficiary: 'ABC',
    sender: 'ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    commission: '123',
    vat: '123',
    net_total: '12345',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    date: 'DD/MM/YYYY',
    dbv_no: '21',
    debit_ledger: 'GL',
    debit_account: 'Account ABC',
    reference_no: '12345',
    pay_type: 'Cash Payment',
    pay_date: 'dd/mm/yyyy',
    beneficiary: 'ABC',
    sender: 'ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    commission: '123',
    vat: '123',
    net_total: '12345',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
  {
    date: 'DD/MM/YYYY',
    dbv_no: '22',
    debit_ledger: 'GL',
    debit_account: 'Account ABC',
    reference_no: '12345',
    pay_type: 'Cash Payment',
    pay_date: 'dd/mm/yyyy',
    beneficiary: 'ABC',
    sender: 'ABC',
    fcy: 'DHS',
    fc_amount: '1000',
    commission: '123',
    vat: '123',
    net_total: '12345',
    user_id: 'ABC',
    time: 'hh:mm',
    attachment: 'Yes',
  },
];

export const MOCK_INWARD_PAYMENT_DATA = [
  {
    id: 1,
    debiteNoteNumber: '5',
    settlementNo: 'DPV 9',
    payDate: 'dd/mm/yyyy',
    account: 'Emirates NBD Bank',
    beneficiary: 'MR KIM',
    mode: 'Bank',
    currency: 'USD',
    fcAmount: '25000.00',
    paidBy: 'Hashim',
  },
  {
    id: 2,
    debiteNoteNumber: '5',
    settlementNo: 'DPV 9',
    payDate: 'dd/mm/yyyy',
    account: 'Emirates NBD Bank',
    beneficiary: 'MR KIM',
    mode: 'Bank',
    currency: 'USD',
    fcAmount: '25000.00',
    paidBy: 'Hashim',
  },
  {
    id: 3,
    debiteNoteNumber: '5',
    settlementNo: 'DPV 9',
    payDate: 'dd/mm/yyyy',
    account: 'Emirates NBD Bank',
    beneficiary: 'MR KIM',
    mode: 'Bank',
    currency: 'USD',
    fcAmount: '25000.00',
    paidBy: 'Hashim',
  },
];

export const MOCK_INWARD_PAYMENT_PAGE_DATA = [
  {
    id: 1,
    payDate: 'DD/MM/YYYY',
    beneficiary: 'MR KIM',
    idNumber: '********9',
    sender: 'MURTAZA',
    contactNo: '',
    currency: 'USD',
    fcBalanceAmount: '2500.00',
    fcTotal: '2500.00',
    refNo: '002',
    debitNoteNumber: '5',
    debitNoteDate: '03/04/2024',
    debitParty: 'Trading LLC',
    payType: 'CASH DEPOSIT',
    bank: 'ENBD',
    detail: 'Lorem Ipsum',
    comment: '',
    action: ['Approve'],
  },
  {
    id: 2,
    payDate: 'DD/MM/YYYY',
    beneficiary: 'MR KIM',
    idNumber: '********9',
    sender: 'MURTAZA',
    contactNo: '',
    currency: 'DHR',
    fcBalanceAmount: '2500.00',
    fcTotal: '2500.00',
    refNo: '002',
    debitNoteNumber: '5',
    debitNoteDate: '03/04/2024',
    debitParty: 'Trading LLC',
    payType: 'CASH DEPOSIT',
    bank: 'ENBD',
    detail: 'Lorem Ipsum',
    comment: '',
    action: ['Hold', 'Pay'],
  },
  // Add more mock data entries as needed
];

export const MOCK_PAYMENT_VOUCHER_DATA = {
  ledger: 'party',
  account: 'abc',
  paid_to: 'abc',
  mode: 'cash',
  paid_to_account: 'abc',
  cheque_number: '**********',
  due_date: '01-01-2025',
  narration: 'test',
  currency: 'usd',
  amount: 100.00,
  commission_type: 'Commission Income',
  commission_percentage: 10,
  vat_terms: 'Standard 5%',
  vat_amount: 10.00,
  net_total: 110.00,
  comment: 'test',
  signature: '',
};

export const MOCK_PAYMENT_VOUCHER_TABLE_DATA = [
  {
    id: 1,
    ledger: 'PL',
    pv_no: 20,
    account_name: 'Account ABC',
    paid_to: 'ABC',
    fcy: 'DHS',
    amount: 1000,
    commission: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    created_at: '2024-10-17T02:49:13.000000Z',
    attachments: 'Yes',
  },
  {
    id: 2,
    ledger: 'WIC',
    pv_no: 22,
    account_name: 'Account ABC',
    paid_to: 'ABC',
    fcy: 'DHS',
    amount: 1000,
    commission: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    created_at: '2024-10-17T02:49:13.000000Z',
    attachments: 'No',
  },
  {
    id: 3,
    ledger: 'WIC',
    pv_no: 24,
    account_name: 'Account ABC',
    paid_to: 'ABC',
    fcy: 'DHS',
    amount: 1000,
    commission: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    created_at: '2024-10-17T02:49:13.000000Z',
    attachments: 'Yes',
  },
  {
    id: 4,
    ledger: 'GL',
    pv_no: 26,
    account_name: 'Account ABC',
    paid_to: 'ABC',
    fcy: 'DHS',
    amount: 1000,
    commission: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    created_at: '2024-10-17T02:49:13.000000Z',
    attachments: 'No',
  },
];

export const MOCK_INTERNAL_PAYMENT_VOUCHER_DATA = {
  ledger: { value: 'gl', label: 'GL' },
  account: { value: 'a1', label: 'Account 1' },
  cost_center: { value: 'abc', label: 'ABC' },
  mode: { value: 'cash', label: 'Cash' },
  paid_from_account: { value: 'abc', label: 'ABC Account' },
  cheque_number: { value: '**********', label: '**********' },
  due_date: '20-03-2024',
  currency: { value: 'usd', label: 'USD' },
  amount: '1000',
  narration: 'Sample narration',
  tableData: [
    {
      id: '1',
      ledger: 'GL',
      debit_account: 'Account 1',
      narration: 'Test narration',
      currency: 'USD',
      amount: '500',
      vat_percentage: '5',
      vat_amount: '25',
    },
  ],
};

export const MOCK_INTERNAL_PAYMENT_VOUCHER_TABLE_DATA = [
  {
    id: 1,
    created_at: '2024-10-17T02:49:13.000000Z',
    ipv_no: 20,
    cr_ledger: 'PL',
    credit_account: 'Account ABC',
    dt_ledger: 'PL',
    debit_account: 'Account ABC',
    fcy: 'DHS',
    amount: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    attachment: 'Yes',
  },
  {
    id: 2,
    created_at: '2024-10-17T02:49:13.000000Z',
    ipv_no: 22,
    cr_ledger: 'WIC',
    credit_account: 'Account ABC',
    dt_ledger: 'WIC',
    debit_account: 'Account ABC',
    fcy: 'DHS',
    amount: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    attachment: 'No',
  },
  {
    id: 3,
    created_at: '2024-10-17T02:49:13.000000Z',
    ipv_no: 24,
    cr_ledger: 'WIC',
    credit_account: 'Account ABC',
    dt_ledger: 'WIC',
    debit_account: 'Account ABC',
    fcy: 'DHS',
    amount: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    attachment: 'Yes',
  },
  {
    id: 4,
    created_at: '2024-10-17T02:49:13.000000Z',
    ipv_no: 26,
    cr_ledger: 'GL',
    credit_account: 'Account ABC',
    dt_ledger: 'GL',
    debit_account: 'Account ABC',
    fcy: 'DHS',
    amount: 1000,
    vat: 1000,
    fcy_net_total: 1000,
    lc_net_total: 1000,
    user_id: 'Abc',
    attachment: 'No',
  },
];

export const cashBalanceReportData = [
  {
    id: 1,
    account: 'Cash Account',
    fcy: 'DHS',
    balance: ********.982,
  },
  {
    id: 2,
    account: 'Cash Account',
    fcy: 'TMN',
    balance: 538983.222,
  },
  {
    id: 3,
    account: 'Cash Account',
    fcy: 'USD',
    balance: 393369.714,
  },
  {
    id: 4,
    account: 'Cash Account',
    fcy: 'DHS',
    balance: ********.982,
  },
  {
    id: 5,
    account: 'Cash Account',
    fcy: 'TMN',
    balance: 538983.222,
  },
  {
    id: 6,
    account: 'Cash Account',
    fcy: 'USD',
    balance: ********,
  },
];

export const bankBalanceReportData = [
  {
    id: 1,
    account: 'Abdullah Salary Account',
    fcy: 'DHS',
    balance: ********.982,
  },
  {
    id: 2,
    account: 'ADCB Petro DHS',
    fcy: 'TMN',
    balance: 538983.222,
  },
  {
    id: 3,
    account: 'EIB Masadan',
    fcy: 'USD',
    balance: 393369.714,
  },
  {
    id: 4,
    account: 'ADCB Petro DHS',
    fcy: 'DHS',
    balance: ********.982,
  },
  {
    id: 5,
    account: 'EIB Masadan',
    fcy: 'TMN',
    balance: 538983.222,
  },
  {
    id: 6,
    account: 'ADCB Petro DH',
    fcy: 'USD',
    balance: ********,
  },
];
