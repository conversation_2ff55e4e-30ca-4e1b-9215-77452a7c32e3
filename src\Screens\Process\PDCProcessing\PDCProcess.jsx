import { React, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import HorizontalTabs from '../../../Components/HorizontalTabs/HorizontalTabs';
import PayablesTable from './PayablesTable';
import ReceivableTable from './ReceivableTable';

const PDCProcess = () => {
  const [activeTab, setActiveTab] = useState('receivables');
  const [searchParams] = useSearchParams();
  useEffect(() => {
    let tab = searchParams.get('tab');
    if (tab) setActiveTab(tab);
  }, []);

  const renderTab = () => {
    switch (activeTab) {
      case 'receivables':
        return <ReceivableTable />;
      case 'payables':
        return <PayablesTable />;
      default:
        return <p>Select a Tab</p>;
    }
  };

  return (
    <>
      <div className=" mb-5">
        <h2 className="screen-title mb-0">PDC Processes</h2>
      </div>
      <div className="beechMein">
        <HorizontalTabs
          tabs={[
            { label: 'Receivables', value: 'receivables' },
            { label: 'Payables', value: 'payables' },
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
      {renderTab()}
    </>
  );
};

export default PDCProcess;
