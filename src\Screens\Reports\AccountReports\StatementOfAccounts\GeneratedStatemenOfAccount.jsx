import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import AttachmentsView from '../../../../Components/AttachmentsView/AttachmentsView';
import CustomButton from '../../../../Components/CustomButton';
import CustomModal from '../../../../Components/CustomModal';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import withFilters from '../../../../HOC/withFilters ';
import {
  statementOfAccountsData,
  supportLogsData,
} from '../../../../Mocks/MockData';
import { statementOfAccountsHeaders } from '../../../../Utils/Constants/TableHeaders';

const GeneratedStatemenOfAccount = ({ filters, setFilters, pagination }) => {
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);

  const tableData = statementOfAccountsData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap flex-lg-nowrap gap-3 mb-3">
        <h2 className="screen-title m-0 d-inline text-nowrap">
          Statement of Accounts
        </h2>
        <div
          className="d-flex gap-3 flex-wrap align-content-start"
          style={{ direction: 'rtl' }}
        >
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Email as PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Email as PDF');
            }}
          />
          <CustomButton
            text={'Email as Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Email as Excel');
            }}
          />

          <CustomButton
            text={'View Attachment'}
            variant={'secondaryButton'}
            onClick={() => {
              setShowAttachmentsModal(true);
              console.log('View Attachment');
            }}
          />
          <CustomButton
            text={'Mark/Unmark'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Mark/Unmark');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={statementOfAccountsHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'Currency', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'Sort by',
                options: [
                  { value: 'title', label: 'Title of Account' },
                  { value: 'fcy_amount', label: 'FCy Amount' },
                ],
              },
            ]}
            dateFilters={[{ title: 'Period' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={statementOfAccountsHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.date}</td>
                    <td>{item.type}</td>
                    <td>{item.tran_no}</td>
                    <td>{item.narration}</td>
                    <td>{item.fcy}</td>
                    <td>{item.debit}</td>
                    <td>{item.credit}</td>
                    <td>{item.lc_balance}</td>
                    <td>{item.sign}</td>
                    <td>{item.value_date}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          item={supportLogsData[0]}
          queryToInvalidate={'attachmentsss'}
          // deleteService={deleteDocumentRegisterAttachment}
          // uploadService={addDocumentRegisterAttachment}
          closeUploader={setShowAttachmentsModal}
        />
      </CustomModal>
    </section>
  );
};

export default withFilters(GeneratedStatemenOfAccount);
