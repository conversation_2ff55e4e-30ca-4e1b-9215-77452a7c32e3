import React from 'react';
import { Col, Row } from 'react-bootstrap';
import BackButton from '../../../../Components/BackButton';
import CustomButton from '../../../../Components/CustomButton';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import withFilters from '../../../../HOC/withFilters ';
import { vatTaxReportData } from '../../../../Mocks/MockData';
import { vatTaxReportHeaders } from '../../../../Utils/Constants/TableHeaders';
import CustomInput from '../../../../Components/CustomInput';

const VATTaxReport = ({ filters, setFilters, pagination }) => {
  const tableData = vatTaxReportData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">VAT Tax Report</h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={vatTaxReportHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              {
                title: 'Transaction Type',
                options: [{ value: 'All', label: 'All' }],
              },
              { title: 'Ledger', options: [{ value: 'All', label: 'All' }] },
              { title: 'Account', options: [{ value: 'All', label: 'All' }] },
            ]}
            dateFilters={[{ title: 'Transaction Date Range' }]}
            renderAtEnd={
              <div className="d-flex justify-content-end mb-2">
                <CustomInput
                  label="Total Base VAT Amount"
                  className="tableInputs"
                  type="text"
                  borderRadius={10}
                  showBorders={false}
                  readOnly
                  value={1234123}
                />
              </div>
            }
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={vatTaxReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item, i) => (
                  <tr key={item.id}>
                    <td>{item.type}</td>
                    <td>{item.tran_no}</td>
                    <td>{item.date}</td>
                    <td>{item.ledger}</td>
                    <td>{item.title_of_account}</td>
                    <td>{item.fcy}</td>
                    <td>{item.fc_amount}</td>
                    <td>{item.vat_amount}</td>
                    <td>{item.net_total}</td>
                    <td>{item.base_amount}</td>
                    <td>{item.base_vat_amount}</td>
                    <td>{item.base_net_total}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(VATTaxReport);
