import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiMiniLink, HiPrinter } from 'react-icons/hi2';
import CustomButton from '../../../../Components/CustomButton';
import CustomInput from '../../../../Components/CustomInput';
import CustomModal from '../../../../Components/CustomModal';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../../Components/StatusChip/StatusChip';
import TableActionDropDown from '../../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../../HOC/withFilters ';
import { MOCK_OUTWARD_REMITTANCE_REGISTER_DATA } from '../../../../Mocks/MockData';
import { outwardRemittanceRegisterHeaders } from '../../../../Utils/Constants/TableHeaders';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
const OutwardRemittanceRegister = ({ filters, setFilters, pagination }) => {
  usePageTitle('Outward Remittance Register');
  const [showHoldingReasonModal, setShowHoldingReasonModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const tableData = MOCK_OUTWARD_REMITTANCE_REGISTER_DATA;
  const isLoading = false;
  const isError = false;

  const handleHoldingReasonSubmit = (values) => {
    console.log(selectedItem, values);
    setShowHoldingReasonModal(false);
  };

  return (
    <section>
      <div className="d-flexflex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">
          Outward Remittance Register
        </h2>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={outwardRemittanceRegisterHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              {
                title: 'Status',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'FCy',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'Confirmation',
                options: [{ value: 'All', label: 'All' }],
              },
              { title: 'Doc Swift', options: [{ value: 'All', label: 'All' }] },
            ]}
            dateFilters={[{ title: 'Period' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={outwardRemittanceRegisterHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    {/* <td>{item?.fsn_number}</td> */}
                    <td>{item?.id}</td>
                    <td>{item?.date}</td>
                    <td>{item?.account_name}</td>
                    <td>{item?.beneficiary}</td>
                    <td>{item?.fcy}</td>
                    <td>{item?.fc_amount}</td>
                    <td>{item?.ag_fcy}</td>
                    <td>{item?.ag_fcy_amount}</td>
                    <td>{item?.against_tt}</td>
                    <td>{item?.fbn_account_name}</td>
                    <td>{item?.fc_payment_amount}</td>
                    <td>{item?.fc_balance_amount}</td>
                    <td>{item?.pay_from_account}</td>
                    <td>{item?.office_location}</td>
                    <td>{item?.doc_swift}</td>
                    <td>{item?.confirmation_status}</td>
                    <td>{item?.approved_by}</td>
                    <td>{item?.comment}</td>
                    <td>
                      <div className="d-flex align-items-center">
                        {item?.action.map((action, index) => {
                          return (
                            <StatusChip
                              key={`${index}-${action}`}
                              status={action}
                              className="ms-2 cp"
                              onClick={() => {
                                console.log(item?.id, action);
                                if (action === 'Hold') {
                                  setShowHoldingReasonModal(true);
                                  setSelectedItem(item);
                                }
                              }}
                            />
                          );
                        })}
                        <TableActionDropDown
                          displaySeparator={false}
                          actions={[
                            {
                              name: 'Link',
                              icon: HiMiniLink,
                              onClick: () => console.log(item),
                              className: 'edit',
                            },
                            {
                              name: 'Print',
                              icon: HiPrinter,
                              onClick: () => console.log(item),
                              className: 'attachments',
                            },
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>

      {/* Holding Reason Modal */}
      <CustomModal
        show={showHoldingReasonModal}
        close={() => setShowHoldingReasonModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">Holding Reason</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              comment: '',
            }}
            // validationSchema={outOfScopeSchema}
            // onSubmit={handleOutOfScopeReasonSubmit}
            onSubmit={handleHoldingReasonSubmit}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    label="Comment"
                    name="comment"
                    required
                    id="comment"
                    type="textarea"
                    rows={1}
                    placeholder="Enter comment"
                    value={values.comment}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.comment && errors.comment}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addClassificationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Save'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowHoldingReasonModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </section>
  );
};
export default withFilters(OutwardRemittanceRegister);
