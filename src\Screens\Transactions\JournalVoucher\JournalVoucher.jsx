import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { FaMagnifyingGlass, FaRegClone } from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import BackButton from '../../../Components/BackButton';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import useAccountsByType from '../../../Hooks/useAccountsByType';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { getVoucherNumber } from '../../../Services/Transaction/JournalVoucher';
import useFormStore from '../../../Stores/FormStore';
import useSettingsStore from '../../../Stores/SettingsStore';
import { getCurrencyOptions } from '../../../Utils/Utils';
import EditJournalVoucher from './EditJournalVoucher';
import JournalVouchersTable from './JournalVouchersTable';
import NewJournalVoucher from './NewJournalVoucher';
import ViewJournalVoucher from './ViewJournalVoucher';

const JournalVoucher = () => {
  usePageTitle('Journal Voucher');
  const navigate = useNavigate();
  const {
    getFormValues,
    getLastVisitedPage,
    saveFormValues,
    setLastVisitedPage,
  } = useFormStore();
  const { updatePrintSetting } = useSettingsStore();
  const currencyOptions = getCurrencyOptions();
  const [restoreValuesFromStore, setRestoreValuesFromStore] = useState(false);
  const [formData, setFormData] = useState({
    rows: {},
    totalDebit: 0,
    totalCredit: 0,
    addedAttachments: [],
  });

  {
    /*
    [new, edit, view, list]
    Edit is for specific JV search and view it's detail
    List is for JV listing
    View is for specific JV search and view 
    */
  }
  const [pageState, setPageState] = useState('new');

  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [writeTerm, setWriteTerm] = useState(''); // To Make search term only work on ButtonClick (Not passing ref as do not want to change component at this stage)
  const [writeCloneTerm, setWriteCloneTerm] = useState(''); // To Make search term only work on ButtonClick (Not passing ref as do not want to change component at this stage)
  const [cloneJV, setCloneJV] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [addLedgerRowId, setAddLedgerRowId] = useState(null);
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);
  const [lastVoucherNumbers, setLastVoucherNumbers] = useState({
    heading: 'Last JV Number: ',
    current: '',
    previous: '',
    next: '',
    isLoadingVoucherNumber: false,
    isErrorVoucherNumber: false,
    errorVoucherNumber: null,
  });
  // Queries
  // Get last voucher number //
  const {
    data: voucherNumber,
    isLoading: isLoadingVoucherNumber,
    isError: isErrorVoucherNumber,
    error: errorVoucherNumber,
  } = useQuery({
    queryKey: ['voucherNumber', searchTerm],
    queryFn: () => getVoucherNumber(searchTerm),
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Get account options using custom hook //
  const { getAccountsByTypeOptions } = useAccountsByType();

  // Handle navigation from Rate of Exchange page
  useEffect(() => {
    const lastPage = getLastVisitedPage('journalVoucher');
    if (lastPage === 'remittanceRateOfExchange') {
      const savedFormData = getFormValues('journalVoucher');
      if (savedFormData) {
        // Set page state to new and enable the table
        setPageState('new');
        setIsDisabled(false);
        setRestoreValuesFromStore(true);
      }
    }
  }, []);

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              if (
                addLedgerRowId &&
                typeof window.updateJVRowField === 'function'
              ) {
                window.updateJVRowField(
                  addLedgerRowId,
                  'account_id',
                  newlyCreatedAccount.id
                );
              }
              setShowAddLedgerModal('');
              setAddLedgerRowId(null);
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              if (
                addLedgerRowId &&
                typeof window.updateJVRowField === 'function'
              ) {
                window.updateJVRowField(
                  addLedgerRowId,
                  'account_id',
                  newlyCreatedAccount.id
                );
              }
              setShowAddLedgerModal('');
              setAddLedgerRowId(null);
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  const renderPageContent = () => {
    const pageComponents = {
      new: (
        <NewJournalVoucher
          date={date}
          setDate={setDate}
          isDisabled={isDisabled}
          setIsDisabled={setIsDisabled}
          getAccountsByTypeOptions={getAccountsByTypeOptions}
          currencyOptions={currencyOptions}
          setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
          setCurrencyToSelect={setCurrencyToSelect}
          setShowAddLedgerModal={setShowAddLedgerModal}
          setAddLedgerRowId={setAddLedgerRowId}
          setPageState={setPageState}
          cloneJV={cloneJV}
          setCloneJV={setCloneJV}
          setWriteCloneTerm={setWriteCloneTerm}
          setSearchTerm={setSearchTerm}
          lastVoucherNumbers={lastVoucherNumbers}
          onFormDataChange={setFormData}
          restoreValuesFromStore={restoreValuesFromStore}
          updatePrintSetting={updatePrintSetting}
        />
      ),
      view: (
        <ViewJournalVoucher
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          setWriteTerm={setWriteTerm}
          setDate={setDate}
          setPageState={setPageState}
          lastVoucherNumbers={lastVoucherNumbers}
        />
      ),
      list: (
        <JournalVouchersTable
          setSearchTerm={setSearchTerm}
          setPageState={setPageState}
          date={date}
        />
      ),
      edit: (
        <EditJournalVoucher
          date={date}
          getAccountsByTypeOptions={getAccountsByTypeOptions}
          currencyOptions={currencyOptions}
          setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
          setCurrencyToSelect={setCurrencyToSelect}
          setShowAddLedgerModal={setShowAddLedgerModal}
          newlyCreatedAccount={newlyCreatedAccount}
          setPageState={setPageState}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          isDisabled={false}
          setIsDisabled={setIsDisabled}
          lastVoucherNumbers={lastVoucherNumbers}
          updatePrintSetting={updatePrintSetting}
        />
      ),
    };

    return pageComponents[pageState] || null;
  };

  useEffect(() => {
    setLastVoucherNumbers({
      heading: 'Last JV Number: ',
      last: voucherNumber?.default_voucher_no,
      current: voucherNumber?.current_voucher_no,
      previous: voucherNumber?.previous_voucher_no,
      next: voucherNumber?.next_voucher_no,
      isLoadingVoucherNumber: isLoadingVoucherNumber,
      isErrorVoucherNumber: isErrorVoucherNumber,
      errorVoucherNumber: errorVoucherNumber,
    });
  }, [
    voucherNumber,
    isLoadingVoucherNumber,
    isErrorVoucherNumber,
    errorVoucherNumber,
  ]);

  return (
    <>
      <section className="position-relative">
        <div
          style={{ height: 43 }}
          className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-4"
        >
          <div>
            {(pageState == 'view' ||
              pageState == 'list' ||
              pageState == 'edit') && (
              <BackButton
                handleBack={() => {
                  pageState == 'edit'
                    ? setPageState('view')
                    : (() => {
                        setDate(new Date().toISOString().split('T')[0]);
                        setPageState('new');
                        setWriteTerm('');
                        setSearchTerm('');
                      })();
                }}
              />
            )}
            <h2 className="screen-title mb-0">Journal Voucher</h2>
          </div>
          {pageState == 'new' && isDisabled && (
            <div className="d-flex gap-2">
              <CustomButton text={'New'} onClick={() => setIsDisabled(false)} />
            </div>
          )}
        </div>
        <Row>
          <Col xs={12}>
            <div className="d-flex align-items-start justify-content-between flex-wrap-reverse mb-3">
              <div className="d-flex gap-3 align-items-end mt-3">
                <CustomInput
                  style={{ width: '180px' }}
                  type="text"
                  placeholder="Search"
                  error={false}
                  showBorders={false}
                  borderRadius={10}
                  name="search"
                  autoComplete="off"
                  rightIcon={FaMagnifyingGlass}
                  value={writeTerm}
                  onChange={(e) => {
                    setWriteTerm(e.target.value);
                  }}
                  onButtonClick={() => {
                    setSearchTerm(writeTerm);
                    setCloneJV(writeCloneTerm);
                    if (writeTerm === '') {
                      setPageState('list');
                    } else {
                      setPageState('view');
                    }
                  }}
                />
                {pageState == 'new' && (
                  <CustomInput
                    style={{ width: '180px' }}
                    type="text"
                    placeholder="Clone JV"
                    error={false}
                    showBorders={false}
                    borderRadius={10}
                    name="clonejv"
                    rightIcon={FaRegClone}
                    value={writeCloneTerm}
                    onChange={(e) => {
                      setWriteCloneTerm(e.target.value);
                    }}
                    onButtonClick={() => {
                      setCloneJV(writeCloneTerm);
                      setIsDisabled(false);
                    }}
                  />
                )}
              </div>
              <div>
                <CustomInput
                  name="Date"
                  label={'Date'}
                  type="date"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={date}
                  disabled={pageState == 'view'}
                  onChange={(e) => {
                    setDate(e.target.value);
                  }}
                />
              </div>
            </div>
            {renderPageContent()}
          </Col>
        </Row>
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        // style={{ maxHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>

      {/* Missing Currency Rate Modal */}
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          // Save form data before navigation
          if (pageState === 'new') {
            saveFormValues('journalVoucher', {
              ...formData,
              date,
            });
            setLastVisitedPage('journalVoucher', 'remittanceRateOfExchange');
          }
          navigate('/transactions/remittance-rate-of-exchange', {
            state: { currencyToSelect },
          });
        }}
      />
    </>
  );
};

export default JournalVoucher;
