import { Form, Formik } from 'formik';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../../Components/CustomButton';
import CustomInput from '../../../../Components/CustomInput';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';
import BackButton from '../../../../Components/BackButton';
import { walkInCustomerAccountJournalHeaders } from '../../../../Utils/Constants/TableHeaders';
import { walkInCustomerAccountJournalData } from '../../../../Mocks/MockData';
import CustomTable from '../../../../Components/CustomTable/CustomTable';

const WalkInCustomerAccountJournal = () => {
  const navigate = useNavigate();
  const tableData = walkInCustomerAccountJournalData.detail;
  const isLoading = false;
  const isError = false;

  const handleSubmit = (values) => {
    console.log('Form Values:', values);
  };

  return (
    <section>
      <div className="d-flex gap-3 justify-content-between flex-wrap mb-4">
        <div className="d-flex flex-column gap-2 mb-4">
          <BackButton />
          <h2 className="screen-title m-0 d-inline">Account Journal</h2>
        </div>
      </div>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <Formik
              initialValues={{
                transaction_type: '',
                transaction_no: '',
                date: '',
                user_id: '',
              }}
              onSubmit={handleSubmit}
            >
              {({ values, handleChange, handleBlur, setFieldValue }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="Transaction Type"
                        name="transaction_type"
                        type="text"
                        value={values.transaction_type}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="Transaction No"
                        name="transaction_no"
                        type="text"
                        value={values.transaction_no}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="Date"
                        name="date"
                        type="date"
                        value={values.date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="User ID"
                        name="user_id"
                        type="text"
                        value={values.user_id}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                  </div>

                  {/* <div className="d-flex mb-4">
                    <CustomButton type="submit" text="Generate" />
                  </div> */}
                </Form>
              )}
            </Formik>
          </div>
        </div>
      <CustomTable
        displayCard={false}
        headers={walkInCustomerAccountJournalHeaders}
        hasFilters={false}
        isPaginated={false}
        hideSearch
        hideItemsPerPage
      >
        {(tableData?.length || isError) && (
          <tbody>
            {isError && (
              <tr>
                <td colSpan={walkInCustomerAccountJournalHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {tableData?.map((item) => (
              <tr key={item.id}>
                <td>{item.title_of_account}</td>
                <td>{item.narration}</td>
                <td>{item.fcy}</td>
                <td>{item.debit}</td>
                <td>{item.credit}</td>
              </tr>
            ))}
          </tbody>
        )}
      </CustomTable>
      </div>
    </section>
  );
};

export default WalkInCustomerAccountJournal;
