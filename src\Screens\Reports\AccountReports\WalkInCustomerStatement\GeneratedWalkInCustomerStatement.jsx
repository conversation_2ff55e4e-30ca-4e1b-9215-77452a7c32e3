import React from 'react';
import { Col, Row } from 'react-bootstrap';
import BackButton from '../../../../Components/BackButton';
import CustomButton from '../../../../Components/CustomButton';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import withFilters from '../../../../HOC/withFilters ';
import { walkInCustomerStatementData } from '../../../../Mocks/MockData';
import { walkInCustomerStatementHeaders } from '../../../../Utils/Constants/TableHeaders';
import { useNavigate } from 'react-router-dom';

const GeneratedWalkInCustomerStatement = ({
  filters,
  setFilters,
  pagination,
}) => {
  const navigate = useNavigate();
  const tableData = walkInCustomerStatementData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <div className="d-flex flex-column gap-2 mb-4">
          <BackButton />
          <h2 className="screen-title m-0 d-inline">
            Walk-In Customer Statement
          </h2>
        </div>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Mark/Unmark'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Mark/Unmark');
            }}
          />
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={walkInCustomerStatementHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'Account', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'Transaction Type',
                options: [{ value: 'All', label: 'All' }],
              },
              { title: 'Currency', options: [{ value: 'All', label: 'All' }] },
              { title: 'Mark type', options: [{ value: 'All', label: 'All' }] },
            ]}
            rangeFilters={[{ title: 'FCY Amount Range' }]}
            dateFilters={[{ title: 'Period' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={walkInCustomerStatementHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.date}</td>
                    <td>{item.type}</td>
                    <td
                      onClick={() => navigate(`${item.tran_no}`)}
                      className="cp underlineOnHover"
                    >
                      {item.tran_no}
                    </td>
                    <td>{item.narration}</td>
                    <td>{item.fcy}</td>
                    <td>{item.debit}</td>
                    <td>{item.credit}</td>
                    <td>{item.balance}</td>
                    <td>{item.sign}</td>
                    <td>{item.value_date}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(GeneratedWalkInCustomerStatement);
