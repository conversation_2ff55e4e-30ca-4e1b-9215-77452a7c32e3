import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import { useFetchTableData } from '../../../Hooks/useTable';
import { receiptVoucherTableHeaders } from '../../../Utils/Constants/TableHeaders';
import { formatDate } from '../../../Utils/Utils';
import { getReceiptVoucherListing } from '../../../Services/Transaction/ReceiptVoucher';

const ReceiptVouchersTable = ({
  date,
  filters,
  pagination,
  updatePagination,
  setPageState,
  setSearchTerm,
}) => {
  const {
    data: { data: receiptVoucherData = [] } = {},
    isLoading,
    isError,
    error,
  } = useFetchTableData(
    'receiptVoucherListing',
    {
      ...filters,
      date: date,
    },
    updatePagination,
    getReceiptVoucherListing
  );

  if (isError) {
    console.error(error);
  }

  return (
    <Row>
      <Col xs={12}>
        <CustomTable
          hasFilters={false}
          headers={receiptVoucherTableHeaders}
          pagination={pagination}
          updatePagination={updatePagination}
          isLoading={isLoading}
          hideItemsPerPage
          hideSearch
        >
          {(receiptVoucherData?.length || isError) && (
            <tbody>
              {isError && (
                <tr>
                  <td colSpan={receiptVoucherTableHeaders.length}>
                    <p className="text-danger mb-0">
                      Unable to fetch data at this time
                    </p>
                  </td>
                </tr>
              )}
              {receiptVoucherData?.map((item) => (
                <tr key={item.id}>
                  <td>{formatDate(item.voucher?.date, 'DD/MM/YYYY')}</td>
                  <td
                    onClick={() => {
                      setSearchTerm(item.voucher.voucher_no);
                      setPageState('view');
                    }}
                  >
                    <p className="hyper-link text-decoration-underline cp mb-0">
                      {item.voucher.voucher_no}
                    </p>
                  </td>
                  <td>{item.new_ledger}</td>
                  <td>{item.account_details?.title}</td>
                  <td>{item.received_from?.name}</td>
                  <td>{item.amount_account?.currency_code}</td>
                  <td>{item.amount}</td>
                  <td>
                    <p
                      className={`mb-0 ${
                        item.commission_type == 'Income'
                          ? 'text-success'
                          : item.commission_type == 'Expense'
                          ? 'text-danger'
                          : ''
                      }`}
                    >
                      {item.commission ||
                        item?.special_commission?.total_commission}
                    </p>
                  </td>
                  <td>{item.vat_amount ? item.vat_amount : item.vat_terms}</td>
                  <td>{item.net_total}</td>
                  <td>{item?.lc_net_total}</td>
                  <td>{item.creator?.user_id}</td>
                  <td>{formatDate(item.created_at, 'HH:MM')}</td>
                  <td>
                    {item.attachments?.charAt(0).toUpperCase() +
                      item?.attachments?.slice(1)}
                  </td>
                </tr>
              ))}
            </tbody>
          )}
        </CustomTable>
      </Col>
    </Row>
  );
};

export default withFilters(ReceiptVouchersTable);
