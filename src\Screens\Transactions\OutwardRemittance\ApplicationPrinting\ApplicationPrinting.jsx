import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiPrinter } from 'react-icons/hi2';
import CustomButton from '../../../../Components/CustomButton';
import CustomModal from '../../../../Components/CustomModal';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import TableActionDropDown from '../../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../../HOC/withFilters ';
import { MOCK_APPLICATION_PRINTING_DATA } from '../../../../Mocks/MockData';
import { applicationPrintingHeaders } from '../../../../Utils/Constants/TableHeaders';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';
import { usePageTitle } from '../../../../Hooks/usePageTitle';

const ApplicationPrinting = ({ filters, setFilters, pagination }) => {
  usePageTitle('Application Printing');
  const [showAssignAccountModal, setShowAssignAccountModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const tableData = MOCK_APPLICATION_PRINTING_DATA;
  const isLoading = false;
  const isError = false;

  const handleAssignAccountSubmit = (values) => {
    console.log(selectedItem, values);
    setShowAssignAccountModal(false);
  };

  return (
    <section>
      <div className="d-flexflex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">Application Printing</h2>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={applicationPrintingHeaders}
            pagination={pagination}
            isLoading={isLoading}
            // hideSearch
            selectOptions={[
              {
                title: 'Status',
                options: [{ value: 'All', label: 'All' }],
              },
            ]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={applicationPrintingHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item?.fsn_number}</td>
                    <td>{item?.date}</td>
                    <td>{item?.amount}</td>
                    <td>{item?.fcy}</td>
                    <td>{item?.beneficiary}</td>
                    <td>{item?.fc_amount}</td>
                    <td>{item?.account_name}</td>
                    <td>{item?.account_number}</td>
                    <td>{item?.status}</td>
                    <td>
                      <div className="d-flex align-items-center">
                        <TableActionDropDown
                          actions={[
                            {
                              name: 'Print',
                              icon: HiPrinter,
                              onClick: () => {
                                setSelectedItem(item);
                                console.log(item.id, 'print');
                                setShowAssignAccountModal(true);
                              },
                              className: 'attachments',
                            },
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>

      {/* Assign Account Modal */}
      <CustomModal
        show={showAssignAccountModal}
        close={() => setShowAssignAccountModal(false)}
        size={'lg'}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">Assign Account</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              ledger: '',
              account: '',
              output: '',
            }}
            // validationSchema={outOfScopeSchema}
            // onSubmit={handleOutOfScopeReasonSubmit}
            onSubmit={handleAssignAccountSubmit}
          >
            {({ values, errors, touched, handleChange, handleBlur, setFieldValue }) => (
              <Form>
                <div className="d-flex gap-3 mb-45">
                  <SearchableSelect
                    label="Ledger"
                    name="ledger"
                    required
                    id="ledger"
                    options={[
                      {
                        label: 'PL',
                        value: 'PL',
                      },
                      {
                        label: 'GL',
                        value: 'GL',
                      },
                      {
                        label: 'WIC',
                        value: 'WIC',
                      },
                    ]}
                    type="select"
                    placeholder="Select Ledger"
                    value={values.ledger}
                    onChange={(selected) => {
                      setFieldValue('ledger', selected.value);
                    }}
                    onBlur={handleBlur}
                    error={touched.ledger && errors.ledger}
                    style={{ flex: 1 }}
                  />
                  <SearchableSelect
                    label="Account"
                    name="account"
                    required
                    id="account"
                    type="select"
                    placeholder="Select Account"
                    value={values.account}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.account && errors.account}
                    style={{ flex: 1 }}
                  />
                </div>
                <div className="mb-45">
                  <SearchableSelect
                    label="Output"
                    name="output"
                    required
                    id="output"
                    type="select"
                    value={values.output}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.output && errors.output}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addClassificationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Print'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowAssignAccountModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </section>
  );
};
export default withFilters(ApplicationPrinting);
