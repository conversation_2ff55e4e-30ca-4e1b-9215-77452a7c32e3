import axiosInstance from '../../Config/axiosConfig';
import { buildFormData } from '../../Utils/Utils';



// CREATE
export const createPdcrVoucher = async (formData) => {
   const payload = new FormData();
    buildFormData(payload, formData);
  try {
    const response = await axiosInstance.post(
      '/user-api/pdcp-issues',
      formData
    );
    const {
      data: { message, status, detail },
    } = response;
    return { message, status, detail }; // Assume this returns the success object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

export const getPdcrListing = async (params) => {
  try {
    console.log('params',params)
    const { data } = await axiosInstance.get(`/user-api/pdcp-issues`, {
      params,
    });

    return data.detail; // Assume this returns success obj

  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};