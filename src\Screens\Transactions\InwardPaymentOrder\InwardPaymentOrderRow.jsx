import React from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { serialNum } from '../../../Utils/Utils';
import { FaMagnifyingGlass } from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';

const InwardPaymentOrderRow = ({
  row,
  index,
  isDisabled,
  updateField,
  handleDeleteRow,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect
}) => {
  const currencyOptions = [
    { label: 'DHS', value: 'DHS' },
    { label: 'USD', value: 'USD' },
    { label: 'EUR', value: 'EUR' },
    { label: 'ETH', value: 'ETH' }
  ];

  const payTypeOptions = [
    { label: 'Cash', value: 'cash' },
    { label: 'Bank', value: 'bank' },
    { label: 'Card', value: 'card' }
  ];

  const bankOptions = [
    { label: 'Bank A', value: 'bank_a' },
    { label: 'Bank B', value: 'bank_b' },
    { label: 'Bank C', value: 'bank_c' }
  ];
  const navigate = useNavigate();
  return (
    <tr>
      <td>{serialNum(index + 1)}</td>
      <td>
        <CustomInput
          type="text"
          value={row.refNo}
          placeholder="Enter Ref No"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'refNo', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={payTypeOptions}
          value={row.payType}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'payType', selected.value)}
          placeholder="Select Pay Type"
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.beneficiary}
          placeholder="Enter Beneficiary"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'beneficiary', e.target.value)}
          borderRadius={10}
          rightIcon={FaMagnifyingGlass}
          onButtonClick={() => {
            navigate('/transactions/beneficiary-search')
          }}
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.sender}
          placeholder="Enter Sender"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'sender', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.idNumber}
          placeholder="Enter ID Number"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'idNumber', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.contactNo}
          placeholder="Enter Contact No"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'contactNo', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={currencyOptions}
          value={row.currency}
          isDisabled={isDisabled}
          onChange={(selected) => {
            if (selected.value.toLowerCase() === 'eth') {
              setShowMissingCurrencyRateModal(true);
              setCurrencyToSelect('eth');
            } else {
              updateField(row.id, 'currency', selected.value);
            }
          }}
          placeholder="Select Currency"
        />
      </td>
      <td>
        <CustomInput
          type="number"
          value={row.fcAmount}
          placeholder="Enter FC Amount"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'fcAmount', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type="number"
          value={row.commission}
          placeholder="Enter Commission"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'commission', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type="date"
          value={row.payDate}
          placeholder="Select Pay Date"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'payDate', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td style={{ minWidth: '120px' }}>
        <SearchableSelect
          options={bankOptions}
          value={row.bankName}
          isDisabled={isDisabled}
          onChange={(selected) => updateField(row.id, 'bankName', selected.value)}
          placeholder="Select Bank"
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.bankAc}
          placeholder="Enter Bank A/c"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'bankAc', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <CustomInput
          type="text"
          value={row.narration}
          placeholder="Enter Narration"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <TableActionDropDown
          actions={[
            {
              name: 'Delete',
              icon: HiOutlineTrash,
              onClick: () => handleDeleteRow(row.id),
              className: 'delete',
            },
          ]}
        />
      </td>
    </tr>
  );
};

export default InwardPaymentOrderRow;
