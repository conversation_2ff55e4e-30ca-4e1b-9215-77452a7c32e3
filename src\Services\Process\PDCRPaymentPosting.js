import axiosInstance from '../../Config/axiosConfig';
import { pdcrPaymentPostingData } from '../../Mocks/MockData';

// GET Receivables
export const getPDCRPaymentPosting = async (params) => {
  try {
    // const { data } = await axiosInstance.get('/user-api/....', {
    //   params,
    // });
    return pdcrPaymentPostingData.detail;
    return data.detail; // Assume this returns the listing object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
