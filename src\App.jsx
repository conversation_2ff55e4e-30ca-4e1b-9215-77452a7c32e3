import { lazy, Suspense } from 'react';
import 'react-phone-number-input/style.css';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import 'react-toastify/dist/ReactToastify.css';
import './App.css';
import SkeletonLoader from './Components/SkeletonLoader/SkeletonLoader';
import Toast from './Components/Toast/Toast';
import AppLayout from './Layout/AppLayout';
import PublicRoutes from './Router/PublicRoutes';
import useUserStore from './Stores/UserStore';

// Freelance work: move all these imports to their respective files -> Lazy load module wise
import AccountEnquiry from './Screens/Reports/AccountReports/AccountEnquiry';
import AccountTurnOverReport from './Screens/Reports/AccountReports/AccountTurnOverReport';
import GenerateExpenseJournal from './Screens/Reports/AccountReports/ExpenseJournal/GenerateExpenseJournal';
import GeneratedExpenseJournal from './Screens/Reports/AccountReports/ExpenseJournal/GeneratedExpenseJournal';
import GenerateJournalReport from './Screens/Reports/AccountReports/JournalReport/GenerateJournalReport';
import GeneratedJounralReport from './Screens/Reports/AccountReports/JournalReport/GeneratedJounralReport';
import OutstandingBalance from './Screens/Reports/AccountReports/OutstandingBalance/OutstandingBalance';
import GeneratedPostDatedChequeReport from './Screens/Reports/AccountReports/PostDatedChequeReport/GeneratedPostDatedChequeReport';
import GenerateStatementOfAccounts from './Screens/Reports/AccountReports/StatementOfAccounts/GenerateStatementOfAccounts';
import GeneratedStatemenOfAccount from './Screens/Reports/AccountReports/StatementOfAccounts/GeneratedStatemenOfAccount';
import GeneratedWalkInCustomerOutstandingBalance from './Screens/Reports/AccountReports/WalkInCustomerOutstandingBalance/GeneratedWalkInCustomerOutstandingBalance';
import GenerateWalkInCustomerStatement from './Screens/Reports/AccountReports/WalkInCustomerStatement/GenerateWalkInCustomerStatement';
import GeneratedWalkInCustomerStatement from './Screens/Reports/AccountReports/WalkInCustomerStatement/GeneratedWalkInCustomerStatement';
import WalkInCustomerAccountJournal from './Screens/Reports/AccountReports/WalkInCustomerStatement/WalkInCustomerAccountJournal';
import BudgetingForecastingReport from './Screens/Reports/Budgeting&ForecastingReport/Budgeting&ForecastingReport';
import BalanceSheet from './Screens/Reports/FinancialReports/BalanceSheet';
import CashAndBankBalance from './Screens/Reports/FinancialReports/CashAndBankBalance';
import ExchangeProfitLossReport from './Screens/Reports/FinancialReports/ExchangeProfitLossReport/ExchangeProfitLossReport';
import GeneratedExchangeProfitLossReport from './Screens/Reports/FinancialReports/ExchangeProfitLossReport/GeneratedExchangeProfitLossReport';
import ProfitLossStatement from './Screens/Reports/FinancialReports/ProfitLossStatement';
import TrialBalance from './Screens/Reports/FinancialReports/TrialBalance';
import CurrencyTransferRegisterReport from './Screens/Reports/RemittanceReports/CurrencyTransferRegisterReport';
import DealRegisterReport from './Screens/Reports/RemittanceReports/DealRegisterReport';
import InwardRemittanceReport from './Screens/Reports/RemittanceReports/InwardRemittanceReport';
import OutwardRemittanceEnquiry from './Screens/Reports/RemittanceReports/OutwardRemittanceEnquiry';
import OutwardRemittanceReport from './Screens/Reports/RemittanceReports/OutwardRemittanceReport';
import VATTaxReport from './Screens/Reports/TaxReports/VATTaxReport/VATTaxReport';
import SubscriptionLogs from './Screens/SubscriptionLogs/SubscriptionLogs';

// User auth components
const UserAuthScreens = lazy(() => import('./Screens/Auth/index'));

// Admin auth components loaded together as a group
const AdminAuthScreens = lazy(() => import('./Screens/Admin/Auth/index'));

// Admin screens should be loaded separately from admin auth screens
const AdminScreens = lazy(() => import('./Screens/Admin/index'));

// Grouped lazy loaded Modules
const ProcessScreens = lazy(() => import('./Screens/Process/index'));
const TransactionScreens = lazy(() => import('./Screens/Transactions/index'));
const AdministrationScreens = lazy(() => import('./Screens/Administration/index'));
const MasterScreens = lazy(() => import('./Screens/Master/index'));

// Other individual components
const NewBranchManagement = lazy(() => import('./Screens/Administration/BranchManagement/NewBranchManagement'));
const Dashboard = lazy(() => import('./Screens/Dashboard/Dashboard'));
const Payment = lazy(() => import('./Screens/Payment/Payment'));
const AdminProfile = lazy(() => import('./Screens/Profile/AdminProfile'));
const Profile = lazy(() => import('./Screens/Profile/Profile'));
const ChangeSubscription = lazy(() => import('./Screens/SubscriptionLogs/ChangeSubscription'));
const Notifications = lazy(() => import('./Screens/Admin/Notifications/Notifications'));
const Support = lazy(() => import('./Screens/Support/Support'));
const Preferences = lazy(() => import('./Screens/Theme/Preferences'));

function App() {
  const { user, role = 'guest' } = useUserStore();
  const isAuthenticated = !!user; // Checks if user is logged in
  const isSubscribed = user?.is_subscribed ?? false;
  const completeProfile = user?.complete_profile ?? false;
  const selectedBranch = user?.selected_branch ?? false;

  // Determine if user action is required
  let userActionRequired = role !== 'admin' ? !isSubscribed || !completeProfile || !selectedBranch : false;
  if (role != 'admin') {
    userActionRequired = !isSubscribed || !completeProfile || !selectedBranch;
  } else {
    userActionRequired = false;
  }

  // Function to get the redirect path based on the status of the user
  const getRedirectPath = () => {
    if (role != 'admin') {
      // If user is business or employee check if he has met the following conditions only then let them go to other routes
      if (!isSubscribed) return '/buy-subscription';
      if (!completeProfile) return '/complete-profile';
      if (!selectedBranch) return '/administration/branch-selection';
    }
    return null;
  };
  // return <SkeletonLoader />;

  return (
    <Suspense fallback={<SkeletonLoader />}>
      <Router basename="/milestone-frontend">
        <Routes>
          {/* Public Routes */}
          <Route
            element={
              <PublicRoutes
                isAuthenticated={isAuthenticated}
                redirectTo={role === 'admin' ? (isAuthenticated ? '/admin' : '/admin/login') : isAuthenticated ? '/dashboard' : '/login'}
              />
            }
          >
            {/* User auth routes - loaded together */}
            <Route path="login" element={<UserAuthScreens screen="login" />} />
            <Route path="forget-id" element={<UserAuthScreens screen="forget-id" />} />
            <Route path="forget-id2" element={<UserAuthScreens screen="forget-id2" />} />
            <Route path="forget-password" element={<UserAuthScreens screen="forget-password" />} />
            <Route path="forget-password2" element={<UserAuthScreens screen="forget-password2" />} />
            <Route path="forget-password3" element={<UserAuthScreens screen="forget-password3" />} />
            <Route path="signup" element={<UserAuthScreens screen="signup" />} />

            {/* Admin auth routes - loaded together */}
            <Route path="admin/login" element={<AdminAuthScreens screen="login" />} />
            <Route path="admin/forget-password" element={<AdminAuthScreens screen="forget-password" />} />
            <Route path="admin/forget-password2" element={<AdminAuthScreens screen="forget-password2" />} />
            <Route path="admin/forget-password3" element={<AdminAuthScreens screen="forget-password3" />} />
          </Route>

          {/* Authenticated Routes */}
          <Route element={isAuthenticated ? <AppLayout disableSidebar={userActionRequired} redirectPath={getRedirectPath()} /> : <Navigate to="/login" />}>
            <Route path="complete-profile" element={<NewBranchManagement />} />
            <Route path="buy-subscription" element={<ChangeSubscription firstTimeLanding={true} />} />

            {/* Common Routes for logged in users irrespective of role */}
            <Route path="preferences" element={<Preferences />} />
            <Route path="profile" element={<Profile />} />
            <Route path="admin/profile" element={<AdminProfile />} />
            <Route path="notifications" element={<Notifications />} />
            <Route path="payment" element={<Payment />} />

            {/* Business Routes */}
            {(role === 'user' || role === 'employee') && (
              <>
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="subscription-logs" element={<SubscriptionLogs />} />

                {/* Master Routes */}
                <Route path="masters/*" element={<MasterScreens />} />

                {/* Administration Routes */}
                <Route path="administration/*" element={<AdministrationScreens />} />

                {/* Process Routes */}
                <Route path="process/*" element={<ProcessScreens />} />

                {/* Transaction Routes */}
                <Route path="transactions/*" element={<TransactionScreens />} />

                {/* Freelance work: Need to import whole module screens from one file like above (transaction and process done*/}
                <Route
                  path="reports/*"
                  element={
                    <Routes>
                      {/* Account Reports */}
                      <Route path="statement-of-accounts" element={<GenerateStatementOfAccounts />} />
                      <Route path="statement-of-accounts/generated" element={<GeneratedStatemenOfAccount />} />
                      <Route path="outstanding-balance-report" element={<OutstandingBalance />} />
                      <Route path="journal-report" element={<GenerateJournalReport />} />
                      <Route path="journal-report/generated" element={<GeneratedJounralReport />} />
                      <Route path="walk-in-customer-statement" element={<GenerateWalkInCustomerStatement />} />
                      <Route path="walk-in-customer-statement/generated" element={<GeneratedWalkInCustomerStatement />} />
                      <Route path="walk-in-customer-statement/generated/:id" element={<WalkInCustomerAccountJournal />} />
                      <Route path="walk-in-customer-outstanding-balance-report" element={<GeneratedWalkInCustomerOutstandingBalance />} />
                      <Route path="expense-journal-report" element={<GenerateExpenseJournal />} />
                      <Route path="expense-journal-report/generated" element={<GeneratedExpenseJournal />} />
                      <Route path="post-dated-cheque-report" element={<GeneratedPostDatedChequeReport />} />
                      <Route path="account-enquiry" element={<AccountEnquiry />} />
                      <Route path="account-turnover-report" element={<AccountTurnOverReport />} />
                      {/* Account Reports End */}

                      {/* Tax Reports */}
                      <Route path="vat-tax-report" element={<VATTaxReport />} />
                      <Route path="corporate-tax-report" element={<h1 className="screen-title">Corporate Tax Report</h1>} />
                      {/* Tax Reports End */}

                      {/* Remittance Reports */}
                      <Route path="inward-remittance-report" element={<InwardRemittanceReport />} />
                      <Route path="outward-remittance-report" element={<OutwardRemittanceReport />} />
                      <Route path="currency-transfer-register-report" element={<CurrencyTransferRegisterReport />} />
                      <Route path="deal-register-report" element={<DealRegisterReport />} />
                      <Route path="outward-remittance-enquiry" element={<OutwardRemittanceEnquiry />} />
                      {/* Remittance Reports End */}

                      {/* Financial Reports */}
                      <Route path="trial-balance" element={<TrialBalance />} />
                      <Route path="profit-loss-statement" element={<ProfitLossStatement />} />
                      <Route path="balance-sheet" element={<BalanceSheet />} />
                      <Route path="exchange-profit-loss-report" element={<ExchangeProfitLossReport />} />
                      <Route path="exchange-profit-loss-report/generated" element={<GeneratedExchangeProfitLossReport />} />
                      <Route path="cash-bank-balance-position" element={<CashAndBankBalance />} />
                      {/* Financial Reports End */}

                      {/* Budgeting & Forecasting Report */}
                      <Route path="budgeting-forecasting-report" element={<BudgetingForecastingReport />} />
                      {/* Budgeting & Forecasting Report End */}

                      <Route path="*" element={<h1>404</h1>} />
                    </Routes>
                  }
                />

                <Route path="support" element={<Support />} />
                <Route path="/" element={<Navigate to="/dashboard" />} />
                <Route path="*" element={<Navigate to="/dashboard" />} />
              </>
            )}

            {/* Admin Routes - use the grouped component for admin routes */}
            {role === 'admin' && (
              <>
                <Route path="admin/*" element={<AdminScreens />} />
                <Route path="/" element={<Navigate to="/admin/dashboard" />} />
              </>
            )}
            <Route path="/" element={<Navigate to={'dashboard'} />} />
          </Route>

          {/* Optional: Catch all for 404 */}
          <Route path="*" element={isAuthenticated ? role === 'admin' ? <Navigate to="/admin" /> : <Navigate to="/dashboard" /> : <Navigate to="/login" />} />
        </Routes>
      </Router>
      <Toast />
    </Suspense>
  );
}

export default App;
