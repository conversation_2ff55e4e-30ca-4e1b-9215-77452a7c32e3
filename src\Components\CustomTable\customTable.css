.customTable {
  overflow-x: auto;
  position: relative;
  min-height: 370px;
}
.customTable .tabel-select {
  padding: 0px 14px 0 0;
}

.customTable table {
  font-size: 14px;
  width: 100%;
}

.customTable table thead tr th:last-child {
  text-align: center;
}
.inputTable.noActions table thead tr th:last-child {
  text-align: left;
}
.customTable tr td:first-child {
  border-radius: 5px 0 0 5px !important;
}
.customTable tr td:last-child {
  text-align: center;
  padding: 10px 35px !important;
  border-radius: 0 5px 5px 0 !important;
}
.customTable tr td:last-child div {
  min-width: max-content;
}
.inputTable.noActions tr td:last-child div {
  min-width: 100%;
}

.customTable th:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.customTable td {
  font-size: 14px;
  vertical-align: middle;
  color: var(--table-td-color);
  padding: 20px 16px !important;
}

.customTable.inputTable td,
.customTable.inputTable.noActions td {
  padding: 10px 12px !important;
}
.customTable.inputTable tbody tr:hover {
  background-color: var(--content-bg-color);
}
.customTable tbody tr {
  transition: 0.2s ease-out;
}
.customTable tbody tr:hover {
  background-color: color-mix(
    in srgb,
    var(--primary-color) 4%,
    transparent 96%
  );
  transition: none;
}
.customTable td a {
  color: var(--table-td-color);
  transition: all 0.15s ease-out;
}
.customTable td a:hover {
  /* color: var(--secondary-color); */
  transition: none;
}
.customTable tr:not(:last-child) {
  border-right: 0 !important;
  border-left: 0 !important;
  border: 1px solid #f2f2f2;
}
.customTable table th,
.customTable table td {
  padding: 0.8rem 1rem;
  vertical-align: middle;
  white-space: nowrap;
}

.customTable table th {
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
}

.customTable .thumbnail {
  width: 25px;
  aspect-ratio: 1/1;
  object-fit: cover;
  object-position: center;
  border-radius: 99px;
  margin-right: 0.5rem;
}

.tableDropdownMenu {
  padding: 0;
  border-radius: 0.3rem;
  min-width: auto;
  text-align: left;
  overflow: hidden;
  /* border: 1px solid #e0e0e0; */
}
.dropdownMenu {
  padding: 0;
  border-radius: 12px;
  min-width: 168px;
  padding: 10px;
  text-align: left;
  overflow: hidden;
  /* border: 1px solid #e0e0e0; */
}
.tableAction {
  font-size: 15px;
  font-weight: 600;
  display: flex;
  justify-content: center;
  text-align: left;
  text-decoration: none;
  padding: 0.5rem 1rem;
  /* color: #333; */
  /* background: #fff0; */
  border: none;
  outline: none;
  width: 100%;
  transition: 0.15s ease-out;
}
.tableAction svg:hover {
  /* color: var(--primary-color); */
  transition: none;
}
.tableAction .view:hover,
.tableAction .view.with-color {
  color: #28a745;
}
.tableAction .edit:hover,
.tableAction .edit.with-color {
  /* color: #ffc107; */
  color: #d39e00;
}
.tableAction .delete:hover,
.tableAction .delete.with-color {
  color: #dc3545;
}
.tableAction .attachments:hover {
  color: #007bff;
}

.tableActionIcon {
  font-size: 15px;
  margin-right: 0.5rem;
  width: 1rem;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}
/* Table Action Tooltip */
.tooltip-toggle {
  cursor: pointer;
  position: relative;
  /* Tooltip text container */
  /* Tooltip arrow */
  /* Setting up the transition */
  /* Triggering the transition */
}
.tooltip-toggle::before {
  position: absolute;
  top: -39px;
  left: -10px;
  background-color: #2b222a;
  border-radius: 5px;
  color: #fff;
  content: attr(aria-label);
  padding: 6px;
  text-transform: none;
  width: fit-content;
}
.tooltip-toggle::after {
  position: absolute;
  top: -6px;
  left: 10px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #2b222a;
  content: ' ';
  font-size: 0;
  line-height: 0;
  margin-left: -5px;
  width: 0;
}
.tooltip-toggle::before,
.tooltip-toggle::after {
  color: #efefef;
  font-size: 14px;
  opacity: 0;
  pointer-events: none;
  text-align: center;
}
.tooltip-toggle:focus::before,
.tooltip-toggle:focus::after,
.tooltip-toggle:hover::before,
.tooltip-toggle:hover::after {
  opacity: 1;
  transition-delay: 0.3s;
}

.customTable::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.customTable::-webkit-scrollbar-track {
  cursor: grab;
}

.customTable::-webkit-scrollbar-thumb {
  border: 2px solid var(--content-bg-color);
  background-color: color-mix(
    in srgb,
    var(--primary-color) 10%,
    rgb(169, 169, 169) 80%
  ) !important;
  border-radius: 8px;
  cursor: grab;
}
.customTable::-webkit-scrollbar-thumb:active {
  cursor: grabbing;
}

.customTable .inputWrapper {
  margin: 0;
  min-width: 100%;
  width: max-content !important;
}
