import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import {
  inwardRemittanceReportData,
  outwardRemittanceReportData,
} from '../../../Mocks/MockData';
import {
  inwardRemittanceReportHeaders,
  outwardRemittanceReportHeaders,
} from '../../../Utils/Constants/TableHeaders';

const InwardRemittanceReport = ({ filters, setFilters, pagination }) => {
  const tableData = inwardRemittanceReportData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">Inward Remittance Report</h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={inwardRemittanceReportHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'Ledger', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'From Account',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'To Account',
                options: [{ value: 'All', label: 'All' }],
              },
              { title: 'FCy', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'Showing',
                options: [{ value: 'All', label: 'All' }],
              },
            ]}
            rangeFilters={[{ title: 'FCy Amount Range' }]}
            dateFilters={[{ title: 'Date Range' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={inwardRemittanceReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.tran_no}</td>
                    <td>{item.tran_date}</td>
                    <td>{item.account}</td>
                    <td>{item.nationality}</td>
                    <td>{item.beneficiary_name}</td>
                    <td>{item.beneficiary_place_of_work}</td>
                    <td>{item.beneficiary_nationality}</td>
                    <td>{item.beneficiary_id_no}</td>
                    <td>{item.contact_number}</td>
                    <td>{item.country_of_origin}</td>
                    <td>{item.purpose}</td>
                    <td>{item.fcy}</td>
                    <td>{item.fc_amount}</td>
                    <td>{item.lc_amount}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(InwardRemittanceReport);
