import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { FaLock, FaLockOpen } from 'react-icons/fa6';
import { HiOutlineTrash } from 'react-icons/hi2';
import { useLocation, useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { showToast } from '../../../Components/Toast/Toast';
import withFilters from '../../../HOC/withFilters ';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import {
  createRemittanceRates,
  deleteRemittanceRate,
  getRemittanceRates,
} from '../../../Services/Transaction/RemittanceRate';
import { remittanceRateOfExchangeHeaders } from '../../../Utils/Constants/TableHeaders';
import {
  getCurrencyOptions,
  isNullOrEmpty,
  showErrorToast,
} from '../../../Utils/Utils';
import BackButton from '../../../Components/BackButton';

const removePartiallyFilledRows = (data) => {
  return Object.fromEntries(
    Object.entries(data).filter(([key, value]) => {
      if (
        typeof value === 'object' &&
        'buy_rate' in value &&
        'buy_from' in value &&
        'buy_upto' in value &&
        'sell_rate' in value &&
        'sell_from' in value &&
        'sell_upto' in value &&
        'ag_fcy_id' in value &&
        'currency_id' in value
      ) {
        return !(
          value.buy_rate === '' ||
          value.buy_from === '' ||
          value.buy_upto === '' ||
          value.sell_rate === '' ||
          value.sell_from === '' ||
          value.sell_upto === '' ||
          !value.ag_fcy_id ||
          !value.currency_id
        );
      }
      return true;
    })
  );
};

const RemittanceRateOfExchange = () => {
  usePageTitle('Rate Of Exchange');
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { state } = useLocation();
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [currency, setCurrency] = useState('');
  const [agFc, setAgFc] = useState('');
  const [editableRows, setEditableRows] = useState({});
  const [editedData, setEditedData] = useState({});
  const [newRows, setNewRows] = useState([
    state?.currencyToSelect
      ? {
          id: crypto.randomUUID(),
          action: 'unlock',
          currency_id: { id: state?.currencyToSelect } || null,
          ag_fcy_id: null,
          buy_rate: '',
          buy_from: '',
          buy_upto: '',
          sell_rate: '',
          sell_from: '',
          sell_upto: '',
          is_new: 1,
          date: date,
        }
      : {
          id: crypto.randomUUID(),
          action: 'unlock',
          currency_id: null,
          ag_fcy_id: null,
          buy_rate: '',
          buy_from: '',
          buy_upto: '',
          sell_rate: '',
          sell_from: '',
          sell_upto: '',
          is_new: 1,
          date: date,
        },
  ]); // Track custom added rows
  const currencyOptions = getCurrencyOptions();

  const currencyOptionsFilter = [
    { label: 'All', value: '' },
    ...currencyOptions,
  ];

  // Mutation
  const createRemittanceRateMutation = useMutation({
    mutationFn: createRemittanceRates,
    onSuccess: () => {
      showToast('Rate updated successfully', 'success');
      queryClient.invalidateQueries(['remittanceRates']);
      setEditedData({});
      setNewRows([]);
      setEditableRows({});
    },
    onError: (error) => {
      console.error('Error adding remittance rates', error);
      showErrorToast(error);
    },
  });

  const {
    data: remittanceRates = [],
    isLoading: isLoadingRemittance,
    isError: isErrorRemittance,
    error: errorRemittance,
  } = useQuery({
    queryKey: ['remittanceRates', currency, agFc, date],
    queryFn: () => getRemittanceRates(currency, agFc, date),
  });

  const handleDelete = async (item) => {
    // Store the current state for rollback if needed
    const previousEditedData = { ...editedData };
    const previousEditableRows = { ...editableRows };
    const previousNewRows = [...newRows];

    try {
      if (!item.id.toString().startsWith('new')) {
        // Optimistically update UI state
        if (editedData[item.id]) {
          setEditedData((prev) => {
            const newData = { ...prev };
            delete newData[item.id];
            return newData;
          });
        }

        if (editableRows[item.id]) {
          setEditableRows((prev) => {
            const newData = { ...prev };
            delete newData[item.id];
            return newData;
          });
        }

        // Optimistically update the query cache
        queryClient.setQueryData(
          ['remittanceRates', currency, agFc, date],
          (oldData) => {
            if (!oldData) return [];
            return oldData.filter((rate) => rate.id !== item.id);
          }
        );

        // Make the API call
        await deleteRemittanceRate(item.id);
        // Not showing success message as optimistically deleting the entry. Only error will be shown
        // showToast('Rate deleted successfully', 'success');

        // After successful deletion, invalidate to ensure sync with server
        queryClient.invalidateQueries({ queryKey: ['remittanceRates'] });
      } else {
        // For new rows, just update the local state
        setNewRows((prev) => prev.filter((row) => row.id !== item.id));

        // Also remove from editedData
        setEditedData((prev) => {
          const newData = { ...prev };
          delete newData[item.id];
          return newData;
        });

        // Also remove from editableRows if present
        if (editableRows[item.id]) {
          setEditableRows((prev) => {
            const newData = { ...prev };
            delete newData[item.id];
            return newData;
          });
        }
      }
    } catch (error) {
      // Revert all optimistic updates on error
      setEditedData(previousEditedData);
      setEditableRows(previousEditableRows);
      setNewRows(previousNewRows);

      // Revert the query cache
      queryClient.setQueryData(
        ['remittanceRates', currency, agFc, date],
        (oldData) => {
          if (!oldData) return [item];
          return [...oldData, item];
        }
      );

      showErrorToast(error || 'Failed to delete rate');
    }
  };

  const handleEdit = (item) => {
    setEditableRows((prev) => ({
      ...prev,
      [item.id]: !prev[item.id],
    }));

    // Initialize edited data for this row if not exists
    if (!editedData[item.id]) {
      setEditedData((prev) => ({
        ...prev,
        [item.id]: {
          ...item,
          action: item.action === 'lock' ? 'unlock' : 'lock',
          buy_rate: item.buy_rate,
          buy_from: item.buy_from,
          buy_upto: item.buy_upto,
          sell_rate: item.sell_rate,
          sell_from: item.sell_from,
          sell_upto: item.sell_upto,
        },
      }));
    } else {
      setEditedData((prev) => ({
        ...prev,
        [item.id]: {
          ...editedData[item.id],
          action: editedData[item.id].action === 'lock' ? 'unlock' : 'lock',
        },
      }));
    }
  };

  const handleInputChange = (itemId, field, value) => {
    setEditedData((prev) => {
      // Find the original item from remittanceRates or newRows
      const originalItem = [...(remittanceRates || []), ...newRows].find(
        (item) => item.id === itemId
      );

      // Ensure the original item exists
      if (!originalItem) return prev;
      return {
        ...prev,
        [itemId]: {
          ...(prev[itemId] || originalItem), // Use previous edits if they exist, otherwise start with originalItem
          [field]: value, // Update only the specific field
        },
      };
    });
  };

  const handleAddRows = () => {
    const newRows = Array.from({ length: 10 }, (_, index) => {
      const id = `new-${index}`; // Ensure unique IDs
      return {
        id,
        action: 'unlock',
        currency_id: '',
        ag_fcy_id: 'null',
        buy_rate: '',
        buy_from: '',
        buy_upto: '',
        sell_rate: '',
        sell_from: '',
        sell_upto: '',
        is_new: 1, // Flag to identify new rows
        date: date,
      };
    });

    setNewRows((prev) => [...prev, ...newRows]);

    // Make all new rows editable by default
    setEditableRows((prev) => ({
      ...prev,
      ...Object.fromEntries(newRows.map((row) => [row.id, true])),
    }));

    // Initialize edited data for all new rows
    setEditedData((prev) => ({
      ...prev,
      ...Object.fromEntries(
        newRows.map((row) => [
          row.id,
          {
            action: 'unlock',
            currency_id: '',
            ag_fcy_id: '',
            buy_rate: '',
            buy_from: '',
            buy_upto: '',
            sell_rate: '',
            sell_from: '',
            sell_upto: '',
            is_new: 1,
            date: date,
          },
        ])
      ),
    }));
  };

  const handleCurrencyChange = (itemId, field, selected) => {
    setEditedData((prev) => {
      const originalItem = [...(remittanceRates || []), ...newRows].find(
        (item) => item.id === itemId
      );

      if (!originalItem) return prev;

      return {
        ...prev,
        [itemId]: {
          ...(prev[itemId] || originalItem), // Use previous edits if they exist, otherwise start with originalItem
          [field]: selected,
          action: 'unlock',
        },
      };
    });
  };

  function handleCancel() {
    navigate(-1);
  }

  if (errorRemittance) {
    showErrorToast(errorRemittance);
  }
  return (
    <>
      <div className="d-flex gap-3 justify-content-between flex-wrap mb-4">
        <div className="d-flex flex-column">
          <BackButton />
          <h2 className="screen-title mb-0">Rate Of Exchange</h2>
        </div>
        <div className="d-flex gap-2">
          <CustomButton
            text={'Get Online Rates'}
            onClick={() => {
              console.log('Online rates');
            }}
          />
          <CustomButton
            text={'Get Special Rates'}
            onClick={() => {
              navigate('/transactions/special-rate-currency');
            }}
          />
        </div>
      </div>

      {/* Table Filters and Date picker */}
      {/* Using separate filters here because of additional functionality */}
      <div className="d-flex align-items-start justify-content-between flex-wrap-reverse">
        <div className="d-flex gap-3 align-items-end mt-3">
          <SearchableSelect
            label={'Currency'}
            options={currencyOptionsFilter}
            placeholder=""
            value={currency}
            onChange={(selected) => {
              setCurrency(selected.value);
            }}
            borderRadius={10}
            minWidth={100}
          />
          <SearchableSelect
            label={'Ag. FCy'}
            options={currencyOptionsFilter}
            placeholder=""
            value={agFc}
            onChange={(selected) => {
              setAgFc(selected.value);
            }}
            borderRadius={10}
            minWidth={100}
          />
        </div>
        <div className="d-flex gap-3 align-items-end ">
          <CustomCheckbox
            style={{ border: 'none', marginBottom: 0 }}
            label={'Auto Currency Pair Definition'}
          />
          <CustomInput
            name="Date"
            label={'Date'}
            type="date"
            showBorders={false}
            error={false}
            borderRadius={10}
            value={date}
            onChange={(e) => {
              setDate(e.target.value);
            }}
          />
        </div>
      </div>

      <CustomTable
        headers={remittanceRateOfExchangeHeaders}
        isLoading={isLoadingRemittance}
        isPaginated={false}
        className={'inputTable'}
        hideSearch
        hideItemsPerPage
      >
        {(remittanceRates?.length || newRows.length || isErrorRemittance) && (
          <tbody>
            {isErrorRemittance && !newRows.length && (
              <tr>
                <td colSpan={remittanceRateOfExchangeHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {[...(remittanceRates || []), ...newRows].map((item, i) => {
              const editedItem = editedData[item?.id];
              const isLocked =
                (!editableRows[item.id] && item.action === 'lock') ||
                editedData[item.id]?.action === 'lock';
              return (
                <tr id={`${item.id}-${i}`} key={`${item.id}-${i}`}>
                  <td>
                    <SearchableSelect
                      name="currency_id"
                      isDisabled={isLocked}
                      options={currencyOptions}
                      value={editedItem?.currency_id ?? item.currency_id ?? ''}
                      onChange={(selected) => {
                        handleCurrencyChange(
                          item.id,
                          'currency_id',
                          selected.value
                        );
                      }}
                      borderRadius={10}
                    />
                  </td>
                  <td>
                    <SearchableSelect
                      name="ag_fcy_id"
                      isDisabled={isLocked}
                      options={currencyOptions}
                      value={editedItem?.ag_fcy_id ?? item.ag_fcy_id}
                      onChange={(selected) =>
                        handleCurrencyChange(
                          item.id,
                          'ag_fcy_id',
                          selected.value
                        )
                      }
                      borderRadius={10}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'buy_rate'}
                      type={'number'}
                      value={editedItem?.buy_rate ?? item.buy_rate}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'buy_rate', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'buy_from'}
                      type={'number'}
                      value={editedItem?.buy_from ?? item.buy_from}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'buy_from', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'buy_upto'}
                      type={'number'}
                      value={editedItem?.buy_upto ?? item.buy_upto}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'buy_upto', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'sell_rate'}
                      type={'number'}
                      value={editedItem?.sell_rate ?? item.sell_rate}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'sell_rate', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'sell_from'}
                      type={'number'}
                      value={editedItem?.sell_from ?? item.sell_from}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'sell_from', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <CustomInput
                      name={'sell_upto'}
                      type={'number'}
                      value={editedItem?.sell_upto ?? item.sell_upto}
                      disabled={isLocked}
                      onChange={(e) =>
                        handleInputChange(item.id, 'sell_upto', e.target.value)
                      }
                      borderRadius={10}
                      style={{ maxWidth: 140 }}
                    />
                  </td>
                  <td>
                    <TableActionDropDown
                      actions={[
                        {
                          name: editableRows[item.id] ? 'Lock' : 'Unlock',
                          icon: editedItem
                            ? editedItem.action === 'lock'
                              ? FaLock
                              : FaLockOpen
                            : item.action === 'lock'
                            ? FaLock
                            : FaLockOpen,
                          onClick: () => handleEdit(item),
                          className: editableRows[item.id] ? 'view' : 'delete',
                        },
                        {
                          name: 'Delete',
                          icon: HiOutlineTrash,
                          onClick: () => handleDelete(item),
                          className: 'delete',
                        },
                      ]}
                    />
                  </td>
                </tr>
              );
            })}
          </tbody>
        )}
      </CustomTable>
      <div className="d-flex gap-3 flex-wrap mb-5 pb-5">
        <CustomButton
          text={'Add Rows'}
          loading={createRemittanceRateMutation.isPending}
          disabled={createRemittanceRateMutation.isPending}
          onClick={handleAddRows}
        />
        <CustomButton
          variant={'secondaryButton'}
          text={'Save'}
          loading={createRemittanceRateMutation.isPending}
          disabled={createRemittanceRateMutation.isPending}
          onClick={() => {
            let formData = {
              rate: [
                ...Object.values(removePartiallyFilledRows(editedData)),
              ].map((item) => {
                if (item.id?.toString().startsWith('new')) {
                  const { id, ...rest } = item;
                  return { ...rest, date };
                }
                return { ...item, date };
              }),
            };
            if (!isNullOrEmpty(formData.rate)) {
              createRemittanceRateMutation.mutate(formData);
            }
          }}
        />
        <CustomButton
          variant={'secondaryButton'}
          text={'Cancel'}
          loading={createRemittanceRateMutation.isPending}
          disabled={createRemittanceRateMutation.isPending}
          onClick={handleCancel}
        />
      </div>
    </>
  );
};

export default withFilters(RemittanceRateOfExchange);
