import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import CustomButton from '../../../Components/CustomButton';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import {
  getAccountToAccountListing,
  deleteAccountToAccount,
  getAccountToAccountAttachments,
  addAccountToAccountAttachment,
  deleteAccountToAccountAttachment,
  getAccountBalance,
} from '../../../Services/Transaction/AccountToAccount';
import { showToast } from '../../../Components/Toast/Toast';
import { useNavigate } from 'react-router-dom';
import { formatDate, isNullOrEmpty } from '../../../Utils/Utils';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import {
  MOCK_CURRENT_ACCOUNT,
  MOCK_EXCHANGE_RATES,
  MOCK_SAVINGS_ACCOUNT,
} from '../../../Mocks/MockData';

const ViewAccountToAccount = ({
  searchTerm,
  setSearchTerm,
  setPageState,
  lastVoucherNumbers,
}) => {
  const queryClient = useQueryClient();
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Fetch main A2A data
  const {
    data: { data: [accountToAccountData] = [] } = {},
    isLoading,
    isError,
    isFetching,
  } = useQuery({
    queryKey: ['accountToAccount', searchTerm],
    queryFn: () => getAccountToAccountListing({ search: searchTerm }),
  });
  // Helper function to map ledger to account type
  const mapLedgerToAccountType = (ledger) => {
    switch (ledger?.toLowerCase()) {
      case 'gl':
        return 'general';
      case 'pl':
        return 'party';
      case 'wic':
        return 'walkin';
      default:
        return ledger?.toLowerCase() || 'general';
    }
  };

  // Fetch account balances for debit and credit accounts
  const { data: debitAccountBalance, error: debitBalanceError } = useQuery({
    queryKey: [
      'accountBalance',
      accountToAccountData?.debit_account_details?.id,
    ],
    queryFn: () =>
      getAccountBalance(
        accountToAccountData?.debit_account_details?.id,
        mapLedgerToAccountType(accountToAccountData?.debit_ledger)
      ),
    enabled:
      !!accountToAccountData?.debit_account_details?.id &&
      !!accountToAccountData?.debit_ledger,
  });
  const { data: creditAccountBalance, error: creditBalanceError } = useQuery({
    queryKey: [
      'accountBalance',
      accountToAccountData?.credit_account_details?.id,
    ],
    queryFn: () =>
      getAccountBalance(
        accountToAccountData?.credit_account_details?.id,
        mapLedgerToAccountType(accountToAccountData?.credit_ledger)
      ),
    enabled:
      !!accountToAccountData?.credit_account_details?.id &&
      !!accountToAccountData?.credit_ledger,
  });
  if (debitBalanceError) {
    console.error('Debit Balance Error:', debitBalanceError);
  }
  if (creditBalanceError) {
    console.error('Credit Balance Error:', creditBalanceError);
  }

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (id) => deleteAccountToAccount(id),
    onSuccess: () => {
      showToast('Voucher deleted successfully!', 'success');
      queryClient.invalidateQueries(['accountToAccount', searchTerm]);
      setShowDeleteModal(false);
      setPageState('new');
      setSearchTerm('');
    },
    onError: (error) => {
      setShowDeleteModal(false);
      showToast(error.message || 'Error deleting Voucher', 'error');
    },
  });

  if (isLoading) {
    return (
      <>
        <div className="d-card mt-3">
          <div className="row">
            {/* Left: Details */}
            <div className="col-xxl-9 col-12">
              <div style={{ maxWidth: 780 }}>
                <div className="row">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="col-md-6 mb-4">
                      <div className="mb-2">
                        <Skeleton
                          duration={1}
                          width={'40%'}
                          baseColor="#ddd"
                          height={16}
                        />
                      </div>
                      <div>
                        <Skeleton
                          duration={1}
                          width={'80%'}
                          baseColor="#ddd"
                          height={20}
                        />
                      </div>
                    </div>
                  ))}
                  <div className="col-12 mb-4">
                    <div className="mb-2">
                      <Skeleton
                        duration={1}
                        width={'20%'}
                        baseColor="#ddd"
                        height={16}
                      />
                    </div>
                    <div>
                      <Skeleton
                        duration={1}
                        width={'100%'}
                        baseColor="#ddd"
                        height={20}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Right: Account Balance Skeletons */}
            <div className="col-xxl-3 col-12">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="mb-4">
                  <Skeleton
                    duration={1}
                    width={'60%'}
                    baseColor="#ddd"
                    height={20}
                    className="mb-2"
                  />
                  <div className="d-card account-balance-card">
                    <Skeleton
                      duration={1}
                      width={'80%'}
                      baseColor="#ddd"
                      height={16}
                      className="mb-3"
                    />
                    <div className="mb-2">
                      <Skeleton
                        duration={1}
                        width={'100%'}
                        baseColor="#ddd"
                        height={30}
                      />
                    </div>
                    {Array.from({ length: 3 }).map((_, j) => (
                      <Skeleton
                        key={j}
                        duration={1}
                        width={'20%'}
                        baseColor="#ddd"
                        height={25}
                        className="mb-1"
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </>
    );
  }
  if (isError || !accountToAccountData) {
    return (
      <div className="d-card">
        <p className="text-danger mb-0">
          Unable to fetch Account to Account data
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="d-card mt-3">
        <div className="row">
          {/* Left: Details */}
          <div className="col-xxl-9 col-12">
            <div style={{ maxWidth: 780 }}>
              <div className="row">
                {[
                  {
                    label: 'Debit Account',
                    value:
                      accountToAccountData?.account_to_account
                        ?.debit_account_details?.title,
                    col: 'col-md-6',
                  },
                  {
                    label: 'Credit Account',
                    value:
                      accountToAccountData?.account_to_account
                        ?.credit_account_details?.title,
                    col: 'col-md-6',
                  },
                  {
                    label: 'Account Title',
                    value:
                      accountToAccountData?.account_to_account?.account_title,
                    col: 'col-md-6',
                  },
                  {
                    label: 'Cheque Number',
                    value:
                      accountToAccountData?.account_to_account?.cheque
                        ?.cheque_number,
                    col: 'col-md-6',
                    condition: accountToAccountData?.account_to_account?.cheque,
                  },
                  {
                    label: 'Currency',
                    value:
                      accountToAccountData?.account_to_account?.currency
                        ?.currency_code,
                    col: 'col-md-6',
                  },
                  {
                    label: 'FC Amount',
                    value: accountToAccountData?.account_to_account?.fc_amount,
                    col: 'col-md-6',
                  },
                  {
                    label: 'Debit Account Narration',
                    value:
                      accountToAccountData?.account_to_account
                        ?.debit_account_narration,
                    col: 'col-md-6',
                  },
                  {
                    label: 'Credit Account Narration',
                    value:
                      accountToAccountData?.account_to_account
                        ?.credit_account_narration,
                    col: 'col-md-6',
                  },
                  {
                    label: 'Comment',
                    value: accountToAccountData?.account_to_account?.comment,
                    col: 'col-12',
                  },
                ]
                  .filter(
                    (field) =>
                      field.condition !== false && !isNullOrEmpty(field.value)
                  )
                  .map((field, index) => (
                    <div key={index} className={field.col}>
                      <div className="mb-4">
                        <label className="text-muted mb-2">{field.label}</label>
                        <div>{field.value}</div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
          <div className="col-xxl-3 col-12">
            {/* Account Balance Cards */}
            <div>
              {/* Current Account */}
              <div>
                <h6 className="mb-2">Account Balance</h6>
                <div className="d-card mb-4 account-balance-card">
                  <div className="mb-3 account-name w-100">
                    {MOCK_CURRENT_ACCOUNT.name}
                  </div>
                  <table className="w-100">
                    <thead>
                      <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                        <th
                          style={{
                            padding: '8px 0',
                            color: '#6B7280',
                            fontWeight: '500',
                          }}
                        >
                          FCy
                        </th>
                        <th
                          style={{
                            padding: '8px 0',
                            color: '#6B7280',
                            fontWeight: '500',
                          }}
                        >
                          Balance
                        </th>
                        <th
                          style={{
                            padding: '8px 0',
                            color: '#6B7280',
                            fontWeight: '500',
                          }}
                        ></th>
                      </tr>
                    </thead>
                    <tbody>
                      {MOCK_CURRENT_ACCOUNT.balances.map((balance, index) => (
                        <tr key={index}>
                          <td
                            style={{
                              padding: '8px 0',
                              color: balance.color,
                              fontWeight: '500',
                            }}
                          >
                            {balance.currency}
                          </td>
                          <td style={{ padding: '8px 0' }}>{balance.amount}</td>
                          <td style={{ padding: '8px 0' }}>{balance.type}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Savings Account */}
              <div>
                <h6 className="mb-2">Account Balance</h6>
                <div className="d-card mb-4 account-balance-card">
                  <div className="mb-3 account-name w-100">
                    {MOCK_SAVINGS_ACCOUNT.name}
                  </div>
                  <table className="w-100">
                    <thead>
                      <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                        <th
                          style={{
                            padding: '8px 0',
                            color: '#6B7280',
                            fontWeight: '500',
                          }}
                        >
                          FCy
                        </th>
                        <th
                          style={{
                            padding: '8px 0',
                            color: '#6B7280',
                            fontWeight: '500',
                          }}
                        >
                          Balance
                        </th>
                        <th
                          style={{
                            padding: '8px 0',
                            color: '#6B7280',
                            fontWeight: '500',
                          }}
                        ></th>
                      </tr>
                    </thead>
                    <tbody>
                      {MOCK_SAVINGS_ACCOUNT.balances.map((balance, index) => (
                        <tr key={index}>
                          <td
                            style={{
                              padding: '8px 0',
                              color: balance.color,
                              fontWeight: '500',
                            }}
                          >
                            {balance.currency}
                          </td>
                          <td style={{ padding: '8px 0' }}>{balance.amount}</td>
                          <td style={{ padding: '8px 0' }}>{balance.type}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Exchange Rates Card */}
            <h6 className="mb-2">Live Exchange Rates Against Base Currency</h6>
            <div className="d-card account-balance-card">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <div className="d-flex align-items-center account-name w-100">
                  <span className="me-2" style={{ color: '#6B7280' }}>
                    Inverse
                  </span>
                  <div className="form-check form-switch">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      style={{ cursor: 'pointer' }}
                    />
                  </div>
                </div>
              </div>
              <table className="w-100">
                <thead>
                  <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                    <th
                      style={{
                        padding: '8px 0',
                        color: '#6B7280',
                        fontWeight: '500',
                      }}
                    >
                      FCy
                    </th>
                    <th
                      style={{
                        padding: '8px 0',
                        color: '#6B7280',
                        fontWeight: '500',
                      }}
                    >
                      Rates
                    </th>
                    <th
                      style={{
                        padding: '8px 0',
                        color: '#6B7280',
                        fontWeight: '500',
                      }}
                    >
                      Change (24h)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {MOCK_EXCHANGE_RATES.map((rate, index) => (
                    <tr key={index}>
                      <td style={{ padding: '8px 0' }}>{rate.currency}</td>
                      <td style={{ padding: '8px 0' }}>{rate.rate}</td>
                      <td
                        style={{
                          padding: '8px 0',
                          color: rate.isPositive ? '#22C55E' : '#EF4444',
                        }}
                      >
                        {rate.change}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* Right: Account Balances */}
          {/* <div className="col-xxl-3 col-12">
            <AccountBalanceCard
              heading="Debit Account Balance"
              accountName={
                accountToAccountData?.debit_account_details?.title ||
                'Debit Account'
              }
              balances={debitAccountBalance?.balances || []}
              loading={debitAccountBalance === undefined}
            />
            <AccountBalanceCard
              heading="Credit Account Balance"
              accountName={
                accountToAccountData?.credit_account_details?.title ||
                'Credit Account'
              }
              balances={creditAccountBalance?.balances || []}
              loading={creditAccountBalance === undefined}
            />
          </div> */}
        </div>
      </div>
      <VoucherNavigationBar
        isDisabled={isLoading || isError || isNullOrEmpty(accountToAccountData)}
        actionButtons={[
          { text: 'Edit', onClick: () => setPageState('edit') },
          {
            text: 'Delete',
            onClick: () => setShowDeleteModal(true),
            variant: 'secondaryButton',
          },
          {
            text: 'print',
            onClick: () => {
              if (accountToAccountData?.pdf_url) {
                window.open(accountToAccountData?.pdf_url, '_blank');
              }
            },
            variant: 'secondaryButton',
          },
        ]}
        loading={isLoading || isFetching}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Attachments Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={accountToAccountData}
          deleteService={deleteAccountToAccountAttachment}
          uploadService={addAccountToAccountAttachment}
          getAttachmentsService={getAccountToAccountAttachments}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          viewOnly
          queryToInvalidate={['accountToAccount', searchTerm]}
        />
      </CustomModal>
      {/* Delete Modal */}
      <CustomModal
        show={showDeleteModal}
        close={() => setShowDeleteModal(false)}
        action={() => {
          if (accountToAccountData) {
            deleteMutation.mutate(accountToAccountData?.voucher_no);
          }
        }}
        title="Delete"
        description={`Are you sure you want to delete A2A Number ${accountToAccountData?.voucher?.voucher_no}?`}
        disableClick={deleteMutation.isPending}
      />
    </>
  );
};

export default ViewAccountToAccount;
