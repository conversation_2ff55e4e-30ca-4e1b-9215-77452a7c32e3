import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiOutlineEye } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../Components/StatusChip/StatusChip';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import withModal from '../../../HOC/withModal';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { unlockRequestData } from '../../../Mocks/MockData';
import { statusFiltersConfig } from '../../../Utils/Constants/TableFilter';
import { unlockRequestHeaders } from '../../../Utils/Constants/TableHeaders';
import { showErrorToast } from '../../../Utils/Utils';

const UnlockRequestManagement = ({
  showModal,
  closeModal,
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Unlock Request Management');
  const navigate = useNavigate();

  const unlockRequestManagement = unlockRequestData || [];

  //  --- MUTATIONS ---
  // Change Status Mutation
  //  --- MUTATIONS END ---

  let data, isLoading, isError, error;

  if (isError) {
    showErrorToast(error);
  }
  return (
    <>
      <section>
        <div className="d-flex justify-content-between flex-wrap mb-3">
          <h2 className="screen-title mb-0">Unlock Request Management</h2>
        </div>
        <Row>
          <Col xs={12}>
            <CustomTable
              filters={filters}
              setFilters={setFilters}
              headers={unlockRequestHeaders}
              pagination={pagination}
              isLoading={isLoading}
              selectOptions={[
                {
                  title: 'status',
                  options: statusFiltersConfig,
                },
              ]}
            >
              {(unlockRequestManagement?.length || isError) && (
                <tbody>
                  {isError && (
                    <tr>
                      <td colSpan={unlockRequestHeaders.length}>
                        <p className="text-danger mb-0">
                          Unable to fetch data at this time
                        </p>
                      </td>
                    </tr>
                  )}
                  {unlockRequestManagement?.map((item) => (
                    <tr key={item.id}>
                      <td>{item?.request_date_time}</td>
                      <td>{item?.requestor_name}</td>
                      <td>{item?.approval_rejection_date_time}</td>
                      <td>
                        <StatusChip status={item.status} />
                      </td>
                      <td>
                        <TableActionDropDown
                          actions={[
                            {
                              name: 'View',
                              icon: HiOutlineEye,
                              onClick: () => navigate(`${item.id}`),
                              className: 'view',
                            },
                          ]}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </CustomTable>
          </Col>
        </Row>
      </section>
    </>
  );
};

export default withModal(withFilters(UnlockRequestManagement));
