import React, { useState } from 'react';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { MOCK_CURRENCY_TRANSFER_DATA } from '../../../Mocks/MockData';
import { currencyTransferTableHeaders } from '../../../Utils/Constants/TableHeaders';
import { statusClassMap } from '../../../Utils/Constants/SelectOptions';

const CurrencyTransferTable = ({ setPageState }) => {
  const [tableData, setTableData] = useState(MOCK_CURRENCY_TRANSFER_DATA);

  return (
    <CustomTable
      headers={currencyTransferTableHeaders}
      isPaginated={false}
      hideSearch
      hideItemsPerPage
    >
      <tbody>
        {tableData?.map((x, i) => (
          <tr key={i}>
            <td>{x.date}</td>
            <td
              onClick={() => {
                setPageState('view');
              }}
            >
              <p className="text-link text-decoration-underline cp mb-0">
                {x.trq_no}
              </p>
            </td>
            <td>{x.debit_ledger}</td>
            <td>{x.debit_account}</td>
            <td>{x.credit_ledger}</td>
            <td>{x.credit_account}</td>
            <td>{x.doc_type}</td>
            <td>{x.doc_no}</td>
            <td>{x.bank}</td>
            <td>{x.city}</td>
            <td>{x.fcy}</td>
            <td>{x.fc_amount}</td>
            <td>{x.user_id}</td>
            <td>{x.time}</td>
            <td>{x.attachment}</td>
          </tr>
        ))}
      </tbody>
    </CustomTable>
  );
};

export default CurrencyTransferTable;
