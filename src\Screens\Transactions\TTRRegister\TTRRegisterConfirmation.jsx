import React, { useState } from 'react';
import { HiOutlinePencilSquare, HiPrinter } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import { MOCK_TTR_REGISTER_CONFIRMATION_DATA } from '../../../Mocks/MockData';
import { ttrRegisterConfirmationHeaders } from '../../../Utils/Constants/TableHeaders';

const TTRRegisterConfirmation = ({ filters, setFilters, pagination }) => {
  const navigate = useNavigate();

  const tableData = MOCK_TTR_REGISTER_CONFIRMATION_DATA;
  const isLoading = false;
  const isError = false;

  return (
    <>
      <CustomTable
        filters={filters}
        setFilters={setFilters}
        headers={ttrRegisterConfirmationHeaders}
        pagination={pagination}
        isLoading={isLoading}
      >
        {(tableData.length || isError) && (
          <tbody>
            {isError && (
              <tr>
                <td colSpan={ttrRegisterConfirmationHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {tableData?.map((item) => (
              <tr key={item.id}>
                <td>{item.date}</td>
                <td>{item.debit_party}</td>
                <td>{item.credit_party}</td>
                <td>{item.bank_name}</td>
                <td>{item.bank_account}</td>
                <td>{item.remarks}</td>
                <td>{item.allocated}</td>
                <td>{item.confirmed}</td>
                <td>{item.unconfirmed}</td>
                <td>
                  <TableActionDropDown
                    actions={[
                      {
                        name: 'Print',
                        icon: HiPrinter,
                        onClick: () => {
                          console.log(item.id, 'print');
                        },
                        className: 'attachments',
                      },

                      {
                        name: 'Edit',
                        icon: HiOutlinePencilSquare,
                        onClick: () => {
                          navigate(
                            `/transactions/ttr-register/confirmation/${item.id}/edit`
                          );
                        },
                        className: 'edit',
                      },
                    ]}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        )}
      </CustomTable>
    </>
  );
};

export default withFilters(TTRRegisterConfirmation);
