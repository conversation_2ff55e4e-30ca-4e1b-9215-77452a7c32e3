import React, { useState, useRef } from 'react';
import {
  FaChevronLeft,
  FaChevronRight,
  FaMagnifyingGlass,
  FaPaperclip,
} from 'react-icons/fa6';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import { useNavigate } from 'react-router-dom';
import {
  MOCK_DEPOSIT_DATA,
  MOCK_INWARD_TT_DATA,
  MOCK_EXCHANGE_RATES,
  MOCK_TRANSACTIONS,
  MOCK_WITHDRAWAL_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import withModal from '../../../HOC/withModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import {
  depositTableHeaders,
  inwardTTTableHeaders,
  withdrawalTableHeaders,
} from '../../../Utils/Constants/TableHeaders';
import { MOCK_CURRENT_ACCOUNT } from '../../../Mocks/MockData';
import { MOCK_SAVINGS_ACCOUNT } from '../../../Mocks/MockData';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import BackButton from '../../../Components/BackButton';
import withFilters from '../../../HOC/withFilters ';

const validationSchema = Yup.object().shape({
  transactionType: Yup.string().required('Transaction type is required'),
  chequeNumber: Yup.string().required('Cheque number is required'),
  fromAccount: Yup.string().required('From account is required'),
  toAccount: Yup.string().when('transactionType', {
    is: (type) => type !== 'inward_tt',
    then: Yup.string().required('To account is required'),
  }),
  amount: Yup.number()
    .required('Amount is required')
    .positive('Amount must be positive'),
  currency: Yup.string().required('Currency is required'),
  narration: Yup.string().required('Narration is required'),

  // Inward TT specific validations
  ledger: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Ledger is required'),
  }),
  bank: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Bank is required'),
  }),
  commissionType: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Commission type is required'),
  }),
  commissionPercentage: Yup.number().when('transactionType', {
    is: 'inward_tt',
    then: Yup.number().min(0, 'Commission percentage must be positive'),
  }),
  commissionAmount: Yup.number().when('transactionType', {
    is: 'inward_tt',
    then: Yup.number().min(0, 'Commission amount must be positive'),
  }),
  country: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: Yup.string().required('Country is required'),
  }),
});

const BankTransactions = ({
  showModal,
  closeModal,
  filters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Bank Transactions');
  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [transactionType, setTransactionType] = useState('deposit');
  const [chequeNumber, setChequeNumber] = useState('');
  const [fromAccount, setFromAccount] = useState('');
  const [toAccount, setToAccount] = useState('');
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState('DHS');
  const [narration, setNarration] = useState('');
  const [headerTransactionType, setHeaderTransactionType] = useState('deposit');
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);

  // Add new state for showing the right cards
  const [showRightCards, setShowRightCards] = useState(false);

  // Add new states for inward TT specific fields
  const [bank, setBank] = useState('');
  const [commissionType, setCommissionType] = useState('');
  const [commissionPercentage, setCommissionPercentage] = useState('');
  const [commissionAmount, setCommissionAmount] = useState('');
  const [country, setCountry] = useState('');

  // Add new state for ledger
  const [ledger, setLedger] = useState('');

  // Update errors state to include new fields and ledger
  const [errors, setErrors] = useState({
    transactionType: '',
    chequeNumber: '',
    fromAccount: '',
    toAccount: '',
    amount: '',
    narration: '',
    bank: '',
    commissionType: '',
    commissionPercentage: '',
    commissionAmount: '',
    country: '',
    ledger: '',
  });

  const initialValues = {
    transactionType: 'deposit',
    chequeNumber: '',
    fromAccount: '',
    toAccount: '',
    amount: '',
    currency: 'DHS',
    narration: '',
    ledger: '',
    bank: '',
    commissionType: '',
    commissionPercentage: '',
    commissionAmount: '',
    country: '',
    date: new Date().toISOString().split('T')[0],
  };

  // Add new state for showing transaction details
  const [showTransactionDetails, setShowTransactionDetails] = useState(false);
  const [currentTransaction, setCurrentTransaction] = useState(null);

  // Add these new states
  const [showDepositTable, setShowDepositTable] = useState(false);
  const [depositFilters, setDepositFilters] = useState({
    page: 1,
    per_page: 10,
  });
  const [sortKey, setSortKey] = useState('');
  const [sortOrder, setSortOrder] = useState('asc');

  // Add this handler function
  const handleSort = (key) => {
    setSortKey(key);
    setSortOrder((current) => (current === 'asc' ? 'desc' : 'asc'));
  };

  const getSearchPlaceholder = (type) => {
    switch (type) {
      case 'withdrawal':
        return 'Search BWV';
      case 'inward_tt':
        return 'Search BITTV';
      default:
        return 'Search BDV';
    }
  };

  const getLastNumberText = (type) => {
    switch (type) {
      case 'withdrawal':
        return 'Last BWV Number: 05';
      case 'inward_tt':
        return 'Last BITTV Number: 05';
      default:
        return 'Last BDV Number: 05';
    }
  };

  // Modify the New button click handler
  const handleNewClick = () => {
    setIsDisabled(false);
    setShowRightCards(true);
  };

  // Validation function
  const validateForm = () => {
    let isValid = true;
    const newErrors = {};

    if (!transactionType) {
      newErrors.transactionType = 'Transaction type is required';
      isValid = false;
    }

    if (!chequeNumber) {
      newErrors.chequeNumber = 'Cheque number is required';
      isValid = false;
    }

    if (!fromAccount) {
      newErrors.fromAccount = 'From account is required';
      isValid = false;
    }

    if (!toAccount) {
      newErrors.toAccount = 'To account is required';
      isValid = false;
    }

    if (!amount) {
      newErrors.amount = 'Amount is required';
      isValid = false;
    } else if (isNaN(amount) || parseFloat(amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount';
      isValid = false;
    }

    if (!narration.trim()) {
      newErrors.narration = 'Narration is required';
      isValid = false;
    }

    if (!ledger) {
      newErrors.ledger = 'Ledger is required';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Modify the submit handler
  const handleSubmit = () => {
    if (validateForm()) {
      // Add your submission logic here
      console.log({
        date,
        transactionType,
        chequeNumber,
        fromAccount,
        toAccount,
        amount,
        currency,
        narration,
      });

      // Reset form after successful submission
      resetForm();
    }
  };
  const navigate = useNavigate();

  // Modify the from account options based on transaction type
  const getFromAccountOptions = () => {
    const baseOptions = [
      { label: 'Account Abc', value: 'account_abc' },
      { label: 'Account Xyz', value: 'account_xyz' },
    ];

    if (transactionType === 'inward_tt') {
      if (!ledger) return baseOptions;

      switch (ledger) {
        case 'party':
          return [...baseOptions, { label: 'Add New PL', value: 'add_new_pl' }];
        case 'general':
          return [...baseOptions, { label: 'Add New GL', value: 'add_new_gl' }];
        case 'walkin':
          return [
            ...baseOptions,
            { label: 'Add New WIC', value: 'add_new_wic' },
          ];
        default:
          return baseOptions;
      }
    } else {
      // For deposit or withdrawal, show Add New GL option
      return [...baseOptions, { label: 'Add New GL', value: 'add_new_gl' }];
    }
  };

  // Modify the from account onChange handler
  const handleFromAccountChange = (selected) => {
    if (selected.value === 'add_new_gl') {
      setShowAddLedgerModal('add new gl');
    } else if (selected.value?.includes('add_new')) {
      // Map the selected value to the correct modal type
      const modalTypes = {
        add_new_pl: 'add new pl',
        add_new_wic: 'add new wic',
      };
      setShowAddLedgerModal(modalTypes[selected.value] || '');
    } else {
      setFromAccount(selected.value);
      setErrors({ ...errors, fromAccount: '' });
    }
  };

  // Modify the search term onChange handler
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Show table when search is empty
    if (!value) {
      setShowDepositTable(true);
      setShowTransactionDetails(false);
      setCurrentTransaction(null);
      setIsDisabled(true);
      setShowRightCards(false);
    } else {
      setShowDepositTable(false);
    }
  };

  // Modify the search button click handler
  const handleSearchButtonClick = () => {
    if (!searchTerm) {
      setShowDepositTable(true);
      return;
    }

    // Find matching transaction in mock data
    const transaction = MOCK_TRANSACTIONS[headerTransactionType]?.['03'];
    if (transaction || showTransactionDetails) {
      setCurrentTransaction(transaction);
      setShowTransactionDetails(true);
      setIsDisabled(false);
      setShowRightCards(true);
    } else {
      setShowTransactionDetails(false);
      setCurrentTransaction(null);
      setIsDisabled(true);
      setShowRightCards(false);
    }
  };

  // Modify the form content based on transaction type
  const renderFormContent = ({
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
  }) => {
    if (values.transactionType === 'inward_tt') {
      return (
        <div>
          {/* Transaction Type and Bank */}
          <div className="row">
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Transaction Type"
                name="transactionType"
                options={[
                  { label: 'Deposit', value: 'deposit' },
                  { label: 'Inward TT', value: 'inward_tt' },
                  { label: 'Withdrawal', value: 'withdrawal' },
                ]}
                value={values.transactionType}
                onChange={(selected) => {
                  setFieldValue('transactionType', selected.value);
                  setHeaderTransactionType(selected.value);
                }}
                placeholder="Select Transaction Type"
                isDisabled={isDisabled}
                error={touched.transactionType && errors.transactionType}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Bank"
                name="bank"
                options={[
                  { label: 'Bank A', value: 'bank_a' },
                  { label: 'Bank B', value: 'bank_b' },
                ]}
                value={values.bank}
                onChange={(selected) => setFieldValue('bank', selected.value)}
                placeholder="Select Bank"
                isDisabled={isDisabled}
                error={touched.bank && errors.bank}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <div className="combined-select-container">
                <label>From Account</label>
                <div
                  className={`combined-select-input ${
                    isDisabled ? 'disabled-combined-select' : ''
                  }`}
                >
                  <div className="combined-select-left">
                    <SearchableSelect
                      name="ledger"
                      options={[
                        { label: 'PL', value: 'party' },
                        { label: 'GL', value: 'general' },
                        { label: 'WIC', value: 'walkin' },
                      ]}
                      className="ledger-select__control"
                      value={values.ledger}
                      onChange={(selected) =>
                        setFieldValue('ledger', selected.value)
                      }
                      placeholder="Ledger"
                      isDisabled={isDisabled}
                      error={touched.ledger && errors.ledger}
                    />
                  </div>
                  <div class="separator-between-selects">|</div>
                  <div className="combined-select-right">
                    <SearchableSelect
                      name="fromAccount"
                      className="ledger-select__control"
                      options={getFromAccountOptions(values.ledger)}
                      value={values.fromAccount}
                      onChange={(selected) => {
                        if (selected.value?.includes('add_new')) {
                          setShowAddLedgerModal(
                            selected.value.replace('add_new_', 'add new ')
                          );
                        } else {
                          setFieldValue('fromAccount', selected.value);
                        }
                      }}
                      placeholder="Select From Account"
                      isDisabled={isDisabled || !values.ledger}
                      error={touched.fromAccount && errors.fromAccount}
                    />
                  </div>
                </div>
              </div>
            </div>
            {/* <div className="col-12 col-sm-6 mb-45">
              <CustomInput
                label="Cheque Number"
                name="chequeNumber"
                type="text"
                placeholder="Enter Cheque Number"
                value={values.chequeNumber}
                style={{ marginBottom: 0 }}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={isDisabled}
                error={touched.chequeNumber && errors.chequeNumber}
              />
            </div> */}
            <div className="col-12 col-sm-6 mb-45">
              <div className="combined-select-container">
                <label>Amount</label>
                <div
                  className={`combined-select-input ${
                    isDisabled ? 'disabled-combined-select' : ''
                  }`}
                >
                  <div className="combined-select-left mt-1">
                    <SearchableSelect
                      name="currency"
                      options={[
                        { label: 'DHS', value: 'DHS' },
                        { label: 'BTC', value: 'BTC' },
                        { label: 'CAD', value: 'CAD' },
                        { label: 'ETH', value: 'ETH' },
                        { label: 'EUR', value: 'EUR' },
                        { label: 'GBP', value: 'GBP' },
                        { label: 'PKR', value: 'PKR' },
                        { label: 'INR', value: 'INR' },
                      ]}
                      value={values.currency}
                      onChange={(selected) =>
                        setFieldValue('currency', selected.value)
                      }
                      isDisabled={isDisabled}
                      style={{ width: '100px' }}
                      className="ledger-select__control"
                      error={touched.currency && errors.currency}
                    />
                  </div>
                  <div class="separator-between-selects">|</div>
                  <div className="combined-select-right mt-3">
                    <CustomInput
                      name="amount"
                      type="number"
                      inputClass="ledger-select__control"
                      placeholder="Enter Amount"
                      value={values.amount}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      disabled={isDisabled}
                      error={touched.amount && errors.amount}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Commission Type"
                name="commissionType"
                options={[
                  { label: 'Commission Income', value: 'commission_income' },
                  { label: 'Commission Expense', value: 'commission_expense' },
                ]}
                value={values.commissionType}
                onChange={(selected) =>
                  setFieldValue('commissionType', selected.value)
                }
                placeholder="Commission Income"
                isDisabled={isDisabled}
                error={touched.commissionType && errors.commissionType}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <CustomInput
                label="Commission %"
                name="commissionPercentage"
                type="number"
                placeholder="Enter commission %"
                value={values.commissionPercentage}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={isDisabled}
                error={
                  touched.commissionPercentage && errors.commissionPercentage
                }
                style={{ marginBottom: 0 }}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <CustomInput
                label="Commission in DHS"
                name="commissionAmount"
                type="number"
                value={values.commissionAmount}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={isDisabled}
                error={touched.commissionAmount && errors.commissionAmount}
                style={{ marginBottom: 0 }}
              />
            </div>
            <div className="col-12 col-sm-6 mb-45">
              <SearchableSelect
                label="Country"
                name="country"
                options={[
                  { label: 'UAE', value: 'uae' },
                  { label: 'USA', value: 'usa' },
                ]}
                value={values.country}
                onChange={(selected) =>
                  setFieldValue('country', selected.value)
                }
                placeholder="Select Country"
                isDisabled={isDisabled}
                error={touched.country && errors.country}
              />
            </div>
          </div>
          {/* Narration */}
          <div>
            <label>Narration</label>
            <CustomInput
              name="narration"
              type="textarea"
              placeholder="Enter Narration"
              value={values.narration}
              onChange={handleChange}
              onBlur={handleBlur}
              disabled={isDisabled}
              rows={5}
              style={{ height: '100px' }}
              error={touched.narration && errors.narration}
            />
          </div>
        </div>
      );
    }

    // Regular deposit/withdrawal form
    return (
      <div>
        {/* Transaction Type and Cheque Number */}
        <div className="row">
          <div className="col-12 col-sm-6 mb-45">
            <SearchableSelect
              label="Transaction Type"
              name="transactionType"
              options={[
                { label: 'Deposit', value: 'deposit' },
                { label: 'Inward TT', value: 'inward_tt' },
                { label: 'Withdrawal', value: 'withdrawal' },
              ]}
              value={values.transactionType}
              onChange={(selected) => {
                setFieldValue('transactionType', selected.value);
                setHeaderTransactionType(selected.value);
              }}
              placeholder="Select Transaction Type"
              isDisabled={isDisabled}
              error={touched.transactionType && errors.transactionType}
            />
          </div>
          <div className="col-12 col-sm-6 mb-45">
            <SearchableSelect
              label="Cheque Number"
              name="chequeNumber"
              options={[
                { label: '0000001', value: '0000001' },
                { label: '0000002', value: '0000002' },
              ]}
              value={values.chequeNumber}
              onChange={(selected) =>
                setFieldValue('chequeNumber', selected.value)
              }
              placeholder="Select Cheque Number"
              isDisabled={isDisabled}
              error={touched.chequeNumber && errors.chequeNumber}
            />
          </div>
          <div className="col-12 mb-45">
            <div className="d-flex gap-3 flex-md-nowrap flex-wrap">
              <div style={{ width: '100%' }}>
                <SearchableSelect
                  label="From Account"
                  name="fromAccount"
                  options={getFromAccountOptions()}
                  value={values.fromAccount}
                  onChange={(selected) => {
                    if (selected.value === 'add_new_gl') {
                      setShowAddLedgerModal('add new gl');
                    } else {
                      setFieldValue('fromAccount', selected.value);
                    }
                  }}
                  placeholder="Select From Account"
                  isDisabled={isDisabled}
                  error={touched.fromAccount && errors.fromAccount}
                />
              </div>
              <div style={{ width: '100%' }}>
                <SearchableSelect
                  label="To Account"
                  name="toAccount"
                  options={[{ label: 'Account Abc', value: 'account_abc' }]}
                  value={values.toAccount}
                  onChange={(selected) =>
                    setFieldValue('toAccount', selected.value)
                  }
                  placeholder="Select To Account"
                  isDisabled={isDisabled}
                  error={touched.toAccount && errors.toAccount}
                />
              </div>
              <div className="flex-shrink-0 d-flex align-items-end">
                <CustomButton
                  text="Switch Account"
                  onClick={() => {
                    const tempFrom = values.fromAccount;
                    setFieldValue('fromAccount', values.toAccount);
                    setFieldValue('toAccount', tempFrom);
                  }}
                  disabled={isDisabled}
                />
              </div>
            </div>
          </div>
          <div className="col-12 col-sm-6 mb-45">
            <div className="combined-select-container">
              <label>Amount</label>
              <div
                className={`combined-select-input ${
                  isDisabled ? 'disabled-combined-select' : ''
                }`}
              >
                <div className="combined-select-left mt-1">
                  <SearchableSelect
                    name="currency"
                    options={[
                      { label: 'DHS', value: 'DHS' },
                      { label: 'BTC', value: 'BTC' },
                      { label: 'CAD', value: 'CAD' },
                      { label: 'ETH', value: 'ETH' },
                      { label: 'EUR', value: 'EUR' },
                      { label: 'GBP', value: 'GBP' },
                      { label: 'PKR', value: 'PKR' },
                      { label: 'INR', value: 'INR' },
                    ]}
                    value={values.currency}
                    onChange={(selected) => {
                      if (selected.label?.toLowerCase()?.startsWith('eth')) {
                        setShowMissingCurrencyRateModal(true);
                      } else {
                        setFieldValue('currency', selected.value);
                      }
                    }}
                    isDisabled={isDisabled}
                    style={{ width: '100px' }}
                    className="ledger-select__control"
                    error={touched.currency && errors.currency}
                  />
                </div>
                <div class="separator-between-selects">|</div>
                <div className="combined-select-right mt-3">
                  <CustomInput
                    name="amount"
                    type="number"
                    inputClass="ledger-select__control"
                    placeholder="Enter Amount"
                    value={values.amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    disabled={isDisabled}
                    error={touched.amount && errors.amount}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* From/To Account */}

        {/* Amount */}

        {/* Narration */}
        <div>
          <label>Narration</label>
          <CustomInput
            name="narration"
            type="textarea"
            placeholder="Enter Narration"
            rows={5}
            value={values.narration}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={isDisabled}
            style={{ height: '100px' }}
            error={touched.narration && errors.narration}
          />
        </div>
      </div>
    );
  };

  // Modify the populateFormData function
  const populateFormData = (transaction) => {
    // Set initial form values
    const formValues = {
      transactionType: transaction.transactionType.toLowerCase(),
      chequeNumber: transaction.chequeNumber,
      fromAccount: transaction.fromAccount,
      toAccount: transaction.toAccount,
      amount: transaction.amount,
      currency: transaction.currency,
      narration: transaction.narration,
      date: transaction.date || new Date().toISOString().split('T')[0],
    };

    // Add inward TT specific fields if applicable
    if (transaction.transactionType === 'Inward TT') {
      formValues.bank = transaction.bank;
      formValues.commissionType = transaction.commissionType;
      formValues.commissionPercentage = transaction.commissionPercentage;
      formValues.commissionAmount = transaction.commissionAmount;
      formValues.country = transaction.country;
      formValues.ledger = transaction.ledger;
    }

    // Update all form fields
    Object.entries(formValues).forEach(([key, value]) => {
      if (value !== undefined) {
        setFieldValue(key, value);
      }
    });

    // Update header transaction type
    setHeaderTransactionType(transaction.transactionType.toLowerCase());

    // Enable form editing
    setIsDisabled(false);
    setShowTransactionDetails(false);
    setShowRightCards(true);
  };

  const handleDelete = (item) => {
    showModal(
      'Delete',
      `Are you sure you want to delete ${getLastNumberText(
        headerTransactionType
      )}?`,
      () => {
        console.log(item);
        // Close the first modal before showing the second one
        closeModal();
        // Show success modal after a small delay to ensure smooth transition
        setTimeout(() => {
          showModal(
            'Deleted',
            `Deleted ${getLastNumberText(headerTransactionType)} successfully`,
            false,
            'success'
          );
        }, 100);
      }
    );
  };

  // Update TransactionDetails component
  const TransactionDetails = () => {
    if (!currentTransaction) return null;

    const handleEdit = () => {
      console.log('Current Transaction:', currentTransaction); // Debug log

      // Format the transaction data
      const formattedTransaction = {
        transactionType: headerTransactionType,
        chequeNumber:
          currentTransaction.bwv_no ||
          currentTransaction.bittv_no ||
          currentTransaction.bdv_no ||
          '',
        fromAccount: currentTransaction.from_account || '',
        toAccount: currentTransaction.to_account || '',
        amount: currentTransaction.fc_amount || '',
        currency: currentTransaction.fcy || 'DHS',
        narration: currentTransaction.narration || '',
        date: currentTransaction.date || new Date().toISOString().split('T')[0],
        bank: currentTransaction.bank || '',
        commissionType: currentTransaction.commission_type || '',
        commissionPercentage: currentTransaction.commission_percentage || '',
        commissionAmount: currentTransaction.fc_commission || '',
        country: currentTransaction.country || '',
        ledger: currentTransaction.ledger || '',
      };

      console.log('Formatted Transaction:', formattedTransaction); // Debug log

      // Update all state variables
      setTransactionType(formattedTransaction.transactionType);
      setChequeNumber(formattedTransaction.chequeNumber);
      setFromAccount(formattedTransaction.fromAccount);
      setToAccount(formattedTransaction.toAccount);
      setAmount(formattedTransaction.amount);
      setCurrency(formattedTransaction.currency);
      setNarration(formattedTransaction.narration);
      setDate(formattedTransaction.date);

      if (headerTransactionType === 'inward_tt') {
        setBank(formattedTransaction.bank);
        setCommissionType(formattedTransaction.commissionType);
        setCommissionPercentage(formattedTransaction.commissionPercentage);
        setCommissionAmount(formattedTransaction.commissionAmount);
        setCountry(formattedTransaction.country);
        setLedger(formattedTransaction.ledger);
      }

      // Reset Formik form
      if (formikRef.current) {
        console.log('Setting Formik values:', formattedTransaction); // Debug log
        formikRef.current.setValues(formattedTransaction);
      }

      // Update UI state
      setIsDisabled(false);
      setShowTransactionDetails(false);
      setShowRightCards(true);
    };

    if (headerTransactionType === 'inward_tt') {
      return (
        <div className="transaction-details">
          <div className="row mb-4">
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Transaction Type</label>
                <p className="mb-0">{currentTransaction.transactionType}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Bank</label>
                <p className="mb-0">{currentTransaction.bank}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">From Account</label>
                <p className="mb-0">{currentTransaction.fromAccount}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Amount</label>
                <p className="mb-0">
                  {currentTransaction.currency} {currentTransaction.amount}
                </p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Commission %</label>
                <p className="mb-0">
                  {currentTransaction.commissionPercentage}
                </p>
              </div>
            </div>
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Cheque Number</label>
                <p className="mb-0">{currentTransaction.chequeNumber}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Commission Type</label>
                <p className="mb-0">{currentTransaction.commissionType}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Commission in DHS</label>
                <p className="mb-0">{currentTransaction.commissionAmount}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Country</label>
                <p className="mb-0">{currentTransaction.country}</p>
              </div>
            </div>
          </div>
          <div className="mb-4">
            <label className="text-muted">Narratsssion</label>
            <p className="mb-0">{currentTransaction.narration}</p>
          </div>
          
          {/* <div className="d-flex justify-content-between align-items-center mt-4">
            <div className="d-flex gap-2">
              <CustomButton
                text="Edit"
                variant="warning"
                onClick={handleEdit}
              />
              <CustomButton
                text="Delete"
                onClick={() => handleDelete(currentTransaction)}
                className={'secondaryButton'}
              />
              <CustomButton text="Print" className={'secondaryButton'} />
            </div>
            <div className="d-flex gap-3 align-items-center">
              <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
              <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
              <FaPaperclip
                size={20}
                style={{ cursor: 'pointer' }}
                uploadAttachmentsModal
              />
            </div>
          </div> 
          <div className="d-flex justify-content-between align-items-center mt-4">
            <div>Last Internal Payment Voucher Number: 05</div>
          </div>*/}
        </div>
      );
    }

    return (
      <>
        <div className="transaction-details">
          <div className="row mb-4">
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Transaction Type</label>
                <p className="mb-0">{currentTransaction.transactionType}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">From Account</label>
                <p className="mb-0">{currentTransaction.fromAccount}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">Currency</label>
                <p className="mb-0">
                  {currentTransaction.currency} {currentTransaction.amount}
                </p>
              </div>
            </div>
            <div className="col-md-6">
              <div className="mb-3">
                <label className="text-muted">Cheque Number</label>
                <p className="mb-0">{currentTransaction.chequeNumber}</p>
              </div>
              <div className="mb-3">
                <label className="text-muted">To Account</label>
                <p className="mb-0">{currentTransaction.toAccount}</p>
              </div>
            </div>
          </div>
          <div className="mb-4">
            <label className="text-muted">Narration</label>
            <p className="mb-0">{currentTransaction.narration}</p>
          </div>

          {/* <div className="d-flex justify-content-between align-items-center mt-4">
          <div className="d-flex gap-2">
            <CustomButton text="Edit" variant="warning" onClick={handleEdit} />
            <CustomButton
              text="Delete"
              onClick={() => handleDelete(currentTransaction)}
              className={'secondaryButton'}
            />
            <CustomButton text="Print" className={'secondaryButton'} />
          </div>
          <div className="d-flex gap-3 align-items-center">
            <FaChevronLeft size={20} style={{ cursor: 'pointer' }} />
            <FaChevronRight size={20} style={{ cursor: 'pointer' }} />
            <FaPaperclip
              size={20}
              style={{ cursor: 'pointer' }}
              uploadAttachmentsModal
            />
          </div>
        </div> */}
          {/* <div className="d-flex justify-content-between align-items-center mt-4">
          <div>Last BDV Number: 05</div>
        </div> */}
        </div>
        <VoucherNavigationBar
          actionButtons={[
            { text: 'Edit', onClick: () => {} },
            { text: 'Delete', onClick: () => {} },
            { text: 'Print', onClick: () => {} },
          ]}
          lastVoucherHeading="Last BDV Number"
        />
      </>
    );
  };

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  // Add formikRef near the top of the component
  const formikRef = useRef();

  return (
    <>
      <section className="position-relative">
        <div className="d-flex gap-3 justify-content-between flex-wrap">
          <div className="">
            {(showDepositTable || showTransactionDetails || !isDisabled) && (
              <BackButton
                handleBack={() => {
                  setIsDisabled(true);
                  setShowRightCards(false);
                  setShowTransactionDetails(false);
                  setShowDepositTable(false);
                }}
              />
            )}
            <h2 className="screen-title mb-0">Bank Transactions</h2>
          </div>
          {isDisabled && !showDepositTable && (
            <CustomButton text={'New'} onClick={handleNewClick} />
          )}
        </div>
        <div className="d-flex justify-content-between align-items-end gap-2 flex-wrap mb-2">
          <div className="combined-select-container">
            <div className={`combined-select-input`}>
              <div className="combined-select-left mt-1">
                <SearchableSelect
                  options={[
                    { label: 'Deposit', value: 'deposit' },
                    { label: 'Inward TT', value: 'inward_tt' },
                    { label: 'Withdrawal', value: 'withdrawal' },
                  ]}
                  value={headerTransactionType}
                  className="ledger-select__control"
                  onChange={(selected) =>
                    setHeaderTransactionType(selected.value)
                  }
                  borderRadius={10}
                />
              </div>
              <div className="separator-between-selects">|</div>
              <div className="combined-select-right mt-3">
                <CustomInput
                  type="text"
                  inputClass="ledger-select__control"
                  placeholder={getSearchPlaceholder(headerTransactionType)}
                  value={searchTerm}
                  onChange={handleSearchChange}
                  rightIcon={FaMagnifyingGlass}
                  borderRadius={10}
                  onButtonClick={handleSearchButtonClick}
                />
              </div>
            </div>
          </div>
          <div>
            <CustomInput
              label="Date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              borderRadius={10}
            />
          </div>
        </div>
        <div className="d-card">
          {showDepositTable ? (
            <CustomTable
              hasFilters={false}
              setFilters={false}
              headers={
                headerTransactionType === 'withdrawal'
                  ? withdrawalTableHeaders
                  : headerTransactionType === 'inward_tt'
                  ? inwardTTTableHeaders
                  : depositTableHeaders
              }
              pagination={pagination}
              updatePagination={updatePagination}
              isLoading={false}
              sortKey={false}
              sortOrder={false}
              handleSort={false}
              // isPaginated={false}
            >
              <tbody>
                {(headerTransactionType === 'withdrawal'
                  ? MOCK_WITHDRAWAL_DATA
                  : headerTransactionType === 'inward_tt'
                  ? MOCK_INWARD_TT_DATA
                  : MOCK_DEPOSIT_DATA
                ).map((item) => (
                  <tr key={item.id}>
                    <td>{item.date}</td>
                    <td
                      onClick={() => {
                        setShowDepositTable(false);
                        setShowTransactionDetails(true);
                        const transaction =
                          MOCK_TRANSACTIONS[headerTransactionType]?.['03'];
                        if (transaction) {
                          setCurrentTransaction(transaction);
                          setShowTransactionDetails(true);
                          setIsDisabled(false);
                          setShowRightCards(true);
                        }
                      }}
                    >
                      <p className="text-link text-decoration-underline cp mb-0">
                        {headerTransactionType === 'withdrawal'
                          ? item.bwv_no
                          : headerTransactionType === 'inward_tt'
                          ? item.bittv_no
                          : item.bdv_no}
                      </p>
                    </td>
                    {headerTransactionType === 'inward_tt' && (
                      <>
                        <td>{item.bank}</td>
                        <td>{item.ledger}</td>
                      </>
                    )}
                    <td>{item.from_account}</td>
                    <td>{item.to_account}</td>
                    <td>{item.fcy}</td>
                    <td>{item.fc_amount}</td>
                    <td>{item.lc_amount}</td>
                    {headerTransactionType === 'inward_tt' && (
                      <td
                        style={{
                          color:
                            item.fc_commission === '100'
                              ? '#EF4444'
                              : '#22C55E',
                        }}
                      >
                        {item.fc_commission}
                      </td>
                    )}
                    <td>{item.user_id}</td>
                    <td>{item.time}</td>
                    <td>{item.has_attachment}</td>
                  </tr>
                ))}
              </tbody>
            </CustomTable>
          ) : // Original form view
          showTransactionDetails ? (
            <TransactionDetails />
          ) : (
            <Formik
              innerRef={formikRef}
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {(formikProps) => (
                <Form>
                  <div className="row justify-content-between">
                    <div className="col-12 col-xxl-9">
                      {renderFormContent(formikProps)}
                    </div>
                    <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                      {showRightCards && (
                        <div>
                          {/* Account Balance Cards */}
                          <div>
                            {/* Current Account */}
                            <div>
                              <h6 className="mb-2">Account Balance</h6>
                              <div className="d-card mb-4 account-balance-card">
                                <div className="mb-3 account-name w-100">
                                  {MOCK_CURRENT_ACCOUNT.name}
                                </div>
                                <table className="w-100">
                                  <thead>
                                    <tr
                                      style={{
                                        borderBottom: '1px solid #E5E7EB',
                                      }}
                                    >
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        FCy
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        Balance
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      ></th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {MOCK_CURRENT_ACCOUNT.balances.map(
                                      (balance, index) => (
                                        <tr key={index}>
                                          <td
                                            style={{
                                              padding: '8px 0',
                                              color: balance.color,
                                              fontWeight: '500',
                                            }}
                                          >
                                            {balance.currency}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.amount}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.type}
                                          </td>
                                        </tr>
                                      )
                                    )}
                                  </tbody>
                                </table>
                              </div>
                            </div>

                            {/* Savings Account */}
                            <div>
                              <h6 className="mb-2">Account Balance</h6>
                              <div className="d-card mb-4 account-balance-card">
                                <div className="mb-3 account-name w-100">
                                  {MOCK_SAVINGS_ACCOUNT.name}
                                </div>
                                <table className="w-100">
                                  <thead>
                                    <tr
                                      style={{
                                        borderBottom: '1px solid #E5E7EB',
                                      }}
                                    >
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        FCy
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      >
                                        Balance
                                      </th>
                                      <th
                                        style={{
                                          padding: '8px 0',
                                          color: '#6B7280',
                                          fontWeight: '500',
                                        }}
                                      ></th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {MOCK_SAVINGS_ACCOUNT.balances.map(
                                      (balance, index) => (
                                        <tr key={index}>
                                          <td
                                            style={{
                                              padding: '8px 0',
                                              color: balance.color,
                                              fontWeight: '500',
                                            }}
                                          >
                                            {balance.currency}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.amount}
                                          </td>
                                          <td style={{ padding: '8px 0' }}>
                                            {balance.type}
                                          </td>
                                        </tr>
                                      )
                                    )}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>

                          {/* Exchange Rates Card */}
                          <h6 className="mb-2">
                            Live Exchange Rates Against Base Currency
                          </h6>
                          <div className="d-card account-balance-card">
                            <div className="d-flex justify-content-between align-items-center mb-3">
                              <div className="d-flex align-items-center account-name w-100">
                                <span
                                  className="me-2"
                                  style={{ color: '#6B7280' }}
                                >
                                  Inverse
                                </span>
                                <div className="form-check form-switch">
                                  <input
                                    className="form-check-input"
                                    type="checkbox"
                                    style={{ cursor: 'pointer' }}
                                  />
                                </div>
                              </div>
                            </div>
                            <table className="w-100">
                              <thead>
                                <tr
                                  style={{ borderBottom: '1px solid #E5E7EB' }}
                                >
                                  <th
                                    style={{
                                      padding: '8px 0',
                                      color: '#6B7280',
                                      fontWeight: '500',
                                    }}
                                  >
                                    FCy
                                  </th>
                                  <th
                                    style={{
                                      padding: '8px 0',
                                      color: '#6B7280',
                                      fontWeight: '500',
                                    }}
                                  >
                                    Rates
                                  </th>
                                  <th
                                    style={{
                                      padding: '8px 0',
                                      color: '#6B7280',
                                      fontWeight: '500',
                                    }}
                                  >
                                    Change (24h)
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {MOCK_EXCHANGE_RATES.map((rate, index) => (
                                  <tr key={index}>
                                    <td style={{ padding: '8px 0' }}>
                                      {rate.currency}
                                    </td>
                                    <td style={{ padding: '8px 0' }}>
                                      {rate.rate}
                                    </td>
                                    <td
                                      style={{
                                        padding: '8px 0',
                                        color: rate.isPositive
                                          ? '#22C55E'
                                          : '#EF4444',
                                      }}
                                    >
                                      {rate.change}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Form Footer */}
                  {/* <div className="d-flex mt-4 flex-xxl-nowrap flex-wrap gap-3 justify-content-between align-items-center">
                      {!isDisabled && (
                        <div className="d-flex gap-2">
                          <CustomButton
                            type="submit"
                            text="Save"
                          />
                          <CustomButton
                            text="Cancel"
                            variant="secondary"
                            onClick={() => {
                              formikProps.resetForm();
                              setIsDisabled(true);
                              setShowRightCards(false);
                            }}
                          />
                        </div>
                      )}
                      <div className="d-flex gap-3 align-items-center">
                        <div className="d-flex gap-2">
                          <FaChevronLeft size={20} style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', opacity: isDisabled ? 0.5 : 1 }} />
                          <FaChevronRight size={20} style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', opacity: isDisabled ? 0.5 : 1 }} />
                          <FaPaperclip
                            onClick={() => !isDisabled && setUploadAttachmentsModal(true)}
                            size={20}
                            style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', opacity: isDisabled ? 0.5 : 1 }}
                          />
                        </div>
                      </div>
                    </div> */}
                  <VoucherNavigationBar
                    isDisabled={isDisabled}
                    actionButtons={[
                      { text: 'Save', onClick: handleSubmit },
                      {
                        text: 'Cancel',
                        onClick: () => {
                          formikProps.resetForm();
                          setIsDisabled(true);
                          setShowRightCards(false);
                        },
                        variant: 'secondaryButton',
                      },
                    ]}
                    onAttachmentClick={() => setUploadAttachmentsModal(true)}
                    lastVoucherHeading={getLastNumberText(
                      headerTransactionType
                    )}
                    lastVoucherNumber={''}
                  />

                  {/* Checkboxes */}
                  <div className="mt-3">
                    <div></div>
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        style={{ border: 'none' }}
                        label="Account Balance"
                        disabled={isDisabled}
                      />
                      <CustomCheckbox
                        style={{ border: 'none' }}
                        label="Print"
                        disabled={isDisabled}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
        <VoucherNavigationBar
            actionButtons={[
              { text: 'Edit', onClick: () => {} },
              { text: 'Delete', onClick: () => {} },
              { text: 'Print', onClick: () => {} },
            ]}
            lastVoucherHeading="Last IPV Number"
          />
      </section>
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          item={supportLogsData[0]}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          navigate('/transactions/remittance-rate-of-exchange');
        }}
      />
    </>
  );
};

export default withFilters(withModal(BankTransactions));
