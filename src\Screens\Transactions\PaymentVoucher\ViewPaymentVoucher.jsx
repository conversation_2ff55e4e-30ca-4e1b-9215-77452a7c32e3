import React, { useEffect, useState } from 'react';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomModal from '../../../Components/CustomModal';
import ExchangeRatesCard from '../../../Components/ExchangeRatesCard/ExchangeRatesCard';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  MOCK_EXCHANGE_RATES,
  MOCK_PAYMENT_VOUCHER_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { deletePaymentVoucher, getPaymentVoucherListing } from '../../../Services/Transaction/PaymentVoucher.js';
import { showToast } from '../../../Components/Toast/Toast.jsx';
import Skeleton from 'react-loading-skeleton';

const ViewPaymentVoucher = ({
                              searchTerm,
                              setDate,
                              setWriteTerm,
                              setSearchTerm,
                              setPageState,
                              lastVoucherNumbers,
                            }) => {
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const apiBaseUrl = import.meta.env.VITE_MILESTONE_BASE_URL;

  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '';
    return typeof value === 'number' ? value.toFixed(2) : value;
  };

  const queryClient = useQueryClient();
  const {
    data: { data: [paymentVoucherData] = [] } = {},
    isLoading,
    isFetching,
    isError,
    error,
  } = useQuery({
    queryKey: ['paymentVoucher', searchTerm],
    queryFn: () => getPaymentVoucherListing({ search: searchTerm }),
    staleTime: 1000 * 60 * 5,
  });

  const paymentVoucher = paymentVoucherData?.payment_vouchers;

  console.log('1231',paymentVoucher);


  useEffect(() => {
    if (paymentVoucherData?.voucher_no) {
      setDate(paymentVoucherData.date);
      setWriteTerm(paymentVoucherData.voucher_no);
    }
  }, [paymentVoucherData?.voucher_no]);

  // Mutation for delete
  const deletePaymentVoucherMutation = useMutation({
    mutationFn: (id) => deletePaymentVoucher(id),
    onSuccess: () => {
      showToast('Payment Voucher deleted successfully!', 'success');
      queryClient.invalidateQueries(['paymentVoucher', searchTerm]);
      setShowDeleteModal(false);
      setPageState('new');
      setWriteTerm('');
      setSearchTerm('');
      setDate(new Date().toISOString().split('T')[0]);
    },
    onError: (error) => {
      setShowDeleteModal(false);
      showErrorToast(error);
    },
  });

  // Navigation Actions
  const handleEdit = () => {
    setPageState('edit');
  };
  const handleDelete = () => {
    setShowDeleteModal(true);
  };
  const handlePrint = () => {
    if (paymentVoucherData?.pdf_url) {
      window.open(paymentVoucherData?.pdf_url, '_blank');
    }
  };


  if (isLoading) {
    return (
      <div className="d-card ">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <div className="row mb-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <>
        <div className="d-card">
          <p className="text-danger">{error.message}</p>
        </div>
      </>
    );
  }
  if (isNullOrEmpty(paymentVoucher)) {
    return (
      <>
        <div className="d-card">
          <p className="text-danger">
            No Payment Voucher found for ID {searchTerm}
          </p>
        </div>
      </>
    );
  }


  return (
    <>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7 mb-4">
            <div className="row">
              {[
                {
                  label: 'Ledger',
                  value: paymentVoucher?.new_ledger,
                },
                {
                  label: 'Account',
                  value: paymentVoucher?.account_details?.title,
                },
                {
                  label: 'Mode',
                  value: paymentVoucher?.mode,
                },
                {
                  label: 'Paid To Account',
                  value: paymentVoucher?.mode_account_id?.account_name,
                },
                {
                  label: 'Cheque Number',
                  value: paymentVoucher?.cheque_number,
                },
                {
                  label: 'Due Date',
                  value: paymentVoucher?.due_date,
                },
                {
                  label: 'Narration',
                  value: paymentVoucher?.narration,
                },
                {
                  label: 'Currency',
                  value: paymentVoucher?.currency?.currency_code,
                },
                {
                  label: 'Amount',
                  value: formatCurrency(paymentVoucher?.amount),
                },
                {
                  label: 'Commission Type',
                  value: paymentVoucher?.commission_type,
                },
                {
                  label: 'Commission Percentage',
                  value: paymentVoucher?.commission,
                },
                {
                  label: 'VAT Terms',
                  value: paymentVoucher?.vat_terms,
                },
                {
                  label: 'VAT Amount',
                  value: formatCurrency(paymentVoucher?.vat_amount),
                },
                {
                  label: 'Net Total',
                  value: formatCurrency(paymentVoucher?.net_total),
                },
                {
                  label: 'Comment',
                  value: paymentVoucher?.comment,
                },
                {
                  label: 'Signature',
                  value: paymentVoucher?.signature,
                },
              ].map((x, i) => {
                if (isNullOrEmpty(x.value)) return null;
                return (
                  <div
                    key={i}
                    className={`col-12 ${
                      x.label === 'Signature' ||
                      x.label === 'Comment' ||
                      x.label === 'Narration'
                        ? ''
                        : 'col-sm-6'
                    } mb-4`}
                  >
                    <p className="detail-title detail-label-color mb-1">
                      {x.label}
                    </p>

                    {x.label === 'Signature' ? (
                      <img
                        src={apiBaseUrl + `/${x.value}`}
                        alt="Signature"
                        style={{ maxWidth: '100%', height: 'auto' }}
                      />
                    ) : (
                      <p className="detail-text wrapText mb-0">{x.value}</p>
                    )}

                  </div>
                );
              })}
            </div>
          </div>
          <div className="col-0 col-xxl-2" />
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
            <div className="row">
              {/* Right side cards */}
              <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
                {/*<AccountBalanceCard />*/}
                {/*<ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} />*/}
              </div>
            </div>
          </div>
          <p className="wrapText mb-0">
            {paymentVoucher?.commission_type === 'Income' ? (
              <span className="text-success">
      5.5% receivable commission of DHS 275.00 on DHS 5,000.00
    </span>
            ) : (
              <span className="text-danger">
      5.5% payable commission of DHS 275.00 on DHS 5,000.00
    </span>
            )}
          </p>
        </div>
      </div>
      <VoucherNavigationBar
        actionButtons={[
          { text: 'Edit', onClick: handleEdit },
          { text: 'Delete', onClick: handleDelete, variant: 'secondaryButton' },
          ...(paymentVoucherData?.pdf_url
            ? [
              {
                text: 'Print',
                onClick: handlePrint,
                variant: 'secondaryButton',
              },
            ]
            : []),
        ]}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        loading={isLoading || isFetching}
        lastVoucherHeading="Last PV Number"
        // lastVoucherNumber={23}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          items={supportLogsData[0]}
          closeUploader={() => setShowAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Delete Modal */}
      <CustomModal
        show={showDeleteModal}
        close={() => {
          setShowDeleteModal(false); // Close the modal on cancel
        }}
        action={() => {
          if (paymentVoucherData) {
            deletePaymentVoucherMutation.mutate(paymentVoucherData.voucher_no);
          }
        }}
        title="Delete"
        description={`Are you sure you want to delete Payment Voucher ${searchTerm}?`}
        // disableClick={deletePackageMutation.isLoading} // Disable modal actions while loading
      />
    </>
  );
};

export default ViewPaymentVoucher;
