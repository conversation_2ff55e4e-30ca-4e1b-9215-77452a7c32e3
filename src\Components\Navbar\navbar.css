.customHeader {
  background: var(--content-bg-color);
  position: fixed !important;
  margin-left: 270px;
  top: 0;
  width: calc(100% - 270px);
  min-width: 200px;
  z-index: 3;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  height: 78px;
  color: white;
  transition: all 0.3s ease-out;
  padding-inline: 36px !important;
}
.customHeader.collapsed {
  margin-left: 60px;
  width: calc(100% - 60px);
}

.toggleSidebarButton {
  z-index: 3;
  transition: all 0.3s;
  color: var(--primary-color);
  cursor: pointer;
}
.toggleSidebarButton.collapsed {
  left: 46px;
}

.dropdown-toggle::after {
  display: none !important;
}
.nav-icons {
  margin-inline: 8px;
  margin-right: 10px;
  color: #399cd3;
  transition: all 0.15s;
}
.nav-icons:hover,
.nav-icons:hover + .badge {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}
.tableAction:has(.userImage) {
  width: fit-content;
}
.userImage {
  background-color: var(--primary-color);
  width: 43px;
  max-width: 100%;
  border-radius: 100px;
  aspect-ratio: 1/1;
  transition: all 0.3s ease-out;
}
.userImage h6 {
  font-size: 18px;
  margin-bottom: 0;
  color: var(--contrast-text-color);
}
.userName {
  font-weight: 700;
  color: #f82c2c;
}

.notiDropdown {
  width: 44px;
  height: 44px;
  border-radius: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
  cursor: pointer;
}
.notification-bell-icon {
  min-width: 26px;
  path {
    fill: var(--primary-text-color);
    transition: all 0.15s ease-out;
  }
}

/*----- Notifcation Start-----*/
/* .notificationBellIcon {
  color: var(--primary-color);
} */
.notification-btn {
  background: none;
  border: none;
  padding: 0;
  font-family: 'Poppins';
  cursor: pointer;
  outline: inherit;
  color: #333;
  font-weight: 600;
  text-align: right;
  text-decoration: none;
  flex-grow: 1;
}
.notification-btn:hover {
  text-decoration: underline;
}

.notifi-btn:hover,
.notifi-btn:focus,
.notifi-btn:focus-visible,
.notifi-btn:active,
.notifi-btn:focus-visible {
  color: #ffa412 !important;
  /* background-color: none !important;
  border-color: none !important; */
  outline: 0 !important;
  box-shadow: none !important;
}
.btn:first-child:active {
  color: #ffa412;
  box-shadow: none !important;
  border-color: none !important;
}

.notiMenu {
  width: 330px !important;
  /* min-width: 400px; */
  width: 100%;
  height: 400px;
  padding: 0 !important;
  border: 1px solid var(--primary-color) !important;
  background: #fff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
  overflow-y: auto;
}
.notiMenu-dark {
  background: var(--eljo-blue);
  /* box-shadow: ; */
}
/* width */
.notiMenu::-webkit-scrollbar {
  width: 6px;
  border-radius: 6px;
}
/* Track */
.notiMenu::-webkit-scrollbar-track {
  background: #fff;
}
/* Handle */
.notiMenu::-webkit-scrollbar-thumb {
  background: #036cc2;
  border-radius: 6px;
}
/* Handle on hover */
.notiMenu::-webkit-scrollbar-thumb:hover {
  background: #df0606;
}

.notificationsBody * {
  /* border: 0.1px solid green; */
}
.notificationsBody {
  overscroll-behavior: contain;
  height: 350px;
  overflow-y: auto;
}
.notificationsBodyHeader {
  display: flex;
  justify-content: space-between;
  margin-inline: 12px;
  cursor: default;
}
.newNotificationCount {
  background: var(--secondary-color);
  padding-inline: 8px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.newNotificationCount p {
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 400;
  background: var(--primary-color);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.notificationsBody::-webkit-scrollbar {
  width: 4px;
}
.Clock-icon,
.date-icon {
  font-size: 16px;
}
.Clock-icon {
  color: #ff518d;
}
.date-icon {
  color: #ff9f6c;
}
.notificationsBody::-webkit-scrollbar-track {
  background-color: var(--body-bg-color);
}

.notificationsBody::-webkit-scrollbar-thumb {
  background: var(--primary-color) !important;
  height: 10px;
}
.notificationBell {
  color: var(--secondary-color);
  border-radius: 5px;
  background-color: var(--primaryColor);
  display: grid;
  place-content: center;
  width: 30px;
  height: 32px;
  min-width: 30px;
}
.notificationBell svg {
  rotate: -15deg;
}
.singleNoti {
  display: flex;
  text-decoration: none;
  min-height: 70px;
  /* height: 100%; */
  padding: 12px 12px;
  transition: 0.3s linear;
  align-items: start;
  border-bottom: 1px solid #ebebeb;
}
.singleNoti .notiTitle {
  color: var(--titleBlack);
}
.singleNoti.unread {
  background-color: color-mix(
    in srgb,
    var(--secondary-color) 15%,
    transparent 80%
  );
}
.singleNoti .notificationBell {
  background-color: var(--secondary-color);
  color: var(--primary-color);
}
.singleNoti.unread .notiDateTime {
  color: #333;
}

.singleNoti.unread .single-notifi-img {
  color: #b8792a;
}
.unread .singleNotiIcon {
  border: 1px solid #b8792a;
}
.singleNoti.read .notiDateTime {
  color: #999;
}
.singleNoti.read .notiUser {
  color: var(--titleBlack);
}
.unread-btn,
.read-btn {
  font-size: 12px;
}
.menuButton {
  color: #000;
  font-size: 22px;
}
.bottom-line {
  text-decoration: none;
}
.singleNotiIcon {
  flex-shrink: 0;
  padding: 6px;
  border: 1px solid #fff;
  color: #fff;
  border-radius: 8px;
  padding: 7px 10px;
}

.single-notifi-img {
  transform: rotate(-25deg);
  font-size: 20px;
  transform-origin: center;
}
.singleNotiContent {
  /* flex-grow: 0; */
  height: 100%;
  /* padding: 10px 15px; */
  /* border-radius: 18px; */
}
.notiTitle {
  color: #191919;
  font-family: 'Poppins';
  font-size: 13px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0px;
}
.notiText {
  font-size: 14px;
  color: #000;
  margin-bottom: 0rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.notiDateTime {
  font-size: 12px;
  margin: 0;
  color: #333;
  font-weight: 500;
}

.notiFooter {
  font-size: 15px;
  font-weight: 600;
  padding: 10px;
  text-align: center;
}
.notiFooter a {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  color: var(--primary-text-color);
  text-decoration: none;
}
.notiFooter a:hover {
  text-decoration-line: underline;
  color: var(--primary-color);
}
.notify-bg {
  background-color: rgba(255, 255, 255, 0.12);
  color: var(--primaryColor);
  padding: 2px 15px 2px 15px;
}
.notiDropdown {
  width: 44px;
  height: 44px;
  border-radius: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s;
  cursor: pointer;
}
.notiDropdown:hover .notification-bell-icon {
  path {
    fill: var(--secondary-color);
    transition: all 0.15s ease-out;
  }
}

.notiDropdown span.badge {
  background: white;
  border: 1px solid var(--secondary-color);
  color: var(--dash-icon-color);
  height: 18px;
  width: 18px;
  padding: 3px;
  font-size: 8px;
  transition: all 0.3s;
  border-radius: 50px;
  position: absolute;
  right: -3px;
  top: -6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}
.notiDropdown .dropdown-toggle::after {
  display: none;
}
.dropdown-toggle::after {
  display: none;
}
.navbar .tableAction {
  padding: 0;
}
@media screen and (max-width: 767px) {
  .sidebarToggle {
    display: block;
  }
  .customHeader {
    height: 64px;
    padding: 0px 16px !important;
  }
  .customHeader.collapsed {
    margin-left: 0px;
    width: 100%;
  }
  .customCollapse {
    background: #fff;
    position: absolute;
    top: 4rem;
    left: 0;
    width: 100%;
    box-shadow: 0px 2px 3px rgb(0 0 0 / 10%);
  }

  .customCollapse .navbar-nav {
    flex-direction: row;
    justify-content: end;
    padding: 15px 10px;
  }

  .customCollapse .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .notifi-btn {
    color: var(--primary-color);
  }
}
@media screen and (max-width: 575px) {
  .notiMenu {
    width: 280px !important;
  }
  .customHeader {
    padding: 0px 10px !important;
  }
}
@media screen and (max-width: 400px) {
  .customCollapse .navbar-nav .dropdown-menu {
    right: -70px;
  }
}
/* ----------- */
.notification-bell-icon-wrapper {
  position: relative;
}

.notification-bell-icon {
  cursor: pointer;
  path {
    fill: var(--primary-color);
  }
}

.badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  padding: 0.2em 0.6em;
  border-radius: 50%;
}

.no-notifications {
  color: #888;
  padding: 10px;
  text-align: center;
}
