import axiosInstance from '../../Config/axiosConfig';

// GET SPECIAL RATE CURRENCY
export const getSpecialRateCurrency = async () => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/remittance-rate/special-rates`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
