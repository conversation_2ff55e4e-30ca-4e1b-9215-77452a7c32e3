import { Form, Formik } from 'formik';
import React from 'react';
import CustomInput from '../CustomInput';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import CustomButton from '../CustomButton';

const ChequeDetailForm = ({ inPopup = false, onSuccess, onCancel }) => {
  const handleSubmit = (values) => {
    console.log(values);
  };
  return (
    <>
      <div className={`${inPopup ? 'px-4 pt-2' : 'd-card'}`}>
        {inPopup ? <h2 className="screen-title-body">Cheque Detail</h2> : null}
        <div className="row">
          <div
            className={`${
              inPopup ? 'col-12' : 'col-12 col-lg-10 col-xl-9 col-xxl-7'
            }`}
          >
            <Formik
              initialValues={{
                bank: '',
                cheque_number: '',
                due_date: '',
              }}
              onSubmit={handleSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name={'bank'}
                        label={'Bank'}
                        options={[
                          {
                            label: 'Bank 1',
                            value: 'b1',
                          },
                          {
                            label: 'Bank 2',
                            value: 'b2',
                          },
                        ]}
                        placeholder={'Select Bank'}
                        value={values.bank}
                        onChange={(v) => {
                          setFieldValue('bank', v.value);
                        }}
                        onBlur={handleBlur}
                        error={touched.bank && errors.bank}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name={'cheque_number'}
                        label={'Cheque Number'}
                        options={[
                          {
                            label: 'Cheque 1',
                            value: 'b1',
                          },
                          {
                            label: 'Cheque 2',
                            value: 'b2',
                          },
                        ]}
                        placeholder={'Select Bank'}
                        value={values.bank}
                        onChange={(v) => {
                          setFieldValue('cheque_number', v.value);
                        }}
                        onBlur={handleBlur}
                        error={touched.cheque_number && errors.cheque_number}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'due_date'}
                        type={'date'}
                        label={'Due Date'}
                        required
                        placeholder={'Enter Due Date'}
                        value={values.due_date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.due_date && errors.due_date}
                      />
                    </div>
                  </div>
                  <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                    <CustomButton
                      //   loading={addBeneficiaryRegisterMutation.isPending}
                      //   disabled={addBeneficiaryRegisterMutation.isPending}
                      type={'submit'}
                      text={'Save'}
                    />
                    {/* {!addBeneficiaryRegisterMutation.isPending && ( */}
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={onCancel}
                    />
                    {/* )} */}
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChequeDetailForm;
