.tableFilters {
  font-size: 15px;
}
.separator {
  display: flex;
  align-items: end;
  justify-content: center;
  margin-top: 26px;
}

.entries-input {
  width: 70px !important;
  height: 33px;
  padding: 0 10px !important;
}
.filterWrapper {
  margin-bottom: 0.5rem;
  position: relative;
}
.filterLabel {
  color: #000;
  font-weight: 500;
  margin-bottom: 0.2rem;
  white-space: nowrap;
}
.filterInput {
  fill: #fff;
  stroke-width: 1px;
  stroke: var(--add-1, #39ae94);
  filter: drop-shadow(0px 3.739px 13.088px rgba(0, 0, 0, 0.08));
  max-width: 300px;
}
.searchWrapper {
  position: relative;
  display: inline-block;
  max-width: 300px;
}
.searchButton {
  font-size: 18px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  color: #666;
  width: 33px;
  height: 100%;
}
.filter-title {
  color: var(--blackColor);
  font-size: 18px;
  font-weight: 700;
  text-transform: capitalize;
}
.searchInput {
  padding: 10px 30px;
  font-size: 18px !important;
  background: transparent;
}
.search_icon {
  color: var(--primary-color) !important;
}
.searchInput::placeholder {
  color: #666 !important;
}
.btn_filter {
  height: 50px;
  width: 50px;
  border-radius: 8px;
  background: var(--primaryColor);
  border-color: var(--primaryColor);
}
.btn_filter:hover {
  border-color: #1a8c1a !important;
  color: #fff !important;
}
.btn_filter:hover,
.btn_filter.btn.show {
  border-color: #1a8c1a !important;
  color: #fff !important;
}
.tableDropdown .fa-ellipsis-vertical {
  color: var(--primaryColor);
}
.btn_filter::after {
  display: none !important;
}

.border-btm {
  border: 0.8px solid var(--add-1, #39ae94);
}
.filter-dropdown .dropdown-menu {
  min-width: 280px;
  border: 0;
  border-radius: 8px;
  border: 0.8px solid var(--add-1, #39ae94);
  background: #fff;
  backdrop-filter: blur(37px);
  padding: 0;
  height: 405px;
  overflow-y: scroll;
}
.filter-dropdown .dropdown-menu::-webkit-scrollbar {
  width: 8px;
}
.fiter-main .select-container,
.select-inner-wrapper .select-container {
  display: block;
}
.filter-dropdown .dropdown-menu::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  box-shadow: inset 0 0 6px rgba(57, 174, 148, 0.6);
  border-radius: 4px;
}

.filter-dropdown .dropdown-menu::-webkit-scrollbar-thumb {
  background-color: #39ae94;
  border-radius: 4px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}
.fiter-main {
  padding: 20px;
}
.select-inner-wrapper {
  padding: 0px;
}

.filter-select-wrapper.inputWrapper {
  min-width: unset !important;
}
