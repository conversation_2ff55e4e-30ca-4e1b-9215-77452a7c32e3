import React from 'react';
import { Col, Row } from 'react-bootstrap';
import BackButton from '../../../../Components/BackButton';
import CustomButton from '../../../../Components/CustomButton';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import withFilters from '../../../../HOC/withFilters ';
import { expenseJournalData } from '../../../../Mocks/MockData';
import { expenseJournalHeaders } from '../../../../Utils/Constants/TableHeaders';

const GeneratedExpenseJournal = ({ filters, setFilters, pagination }) => {
  const tableData = expenseJournalData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <div className="d-flex flex-column gap-2 mb-4">
          <BackButton />
          <h2 className="screen-title m-0 d-inline">Expense Journal</h2>
        </div>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={expenseJournalHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'Account', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'Transaction Type',
                options: [{ value: 'All', label: 'All' }],
              },
              { title: 'FCy', options: [{ value: 'All', label: 'All' }] },
              { title: 'User ID', options: [{ value: 'All', label: 'All' }] },
            ]}
            rangeFilters={[{ title: 'FCY Amount Range' }]}
            dateFilters={[{ title: 'Transaction Date' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={expenseJournalHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.type}</td>
                    <td>{item.tran_no}</td>
                    <td>{item.date}</td>
                    <td>{item.account_title}</td>
                    <td>{item.narration}</td>
                    <td>{item.fcy}</td>
                    <td>{item.fcy_amount}</td>
                    <td>{item.base_currency_value}</td>
                    <td>{item.user_id}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(GeneratedExpenseJournal);
