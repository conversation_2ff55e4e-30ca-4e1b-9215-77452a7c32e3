import { ErrorMessage, Form, Formik } from 'formik';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { PDCProcessOpenTypeOptions } from '../../../Utils/Constants/SelectOptions';
import { formatDate, isNullOrEmpty } from '../../../Utils/Utils';
import BackButton from '../../../Components/BackButton';

const ReceivableOpen = () => {
  usePageTitle('PDC Processes');
  const navigate = useNavigate();

  const { id } = useParams();
  const location = useLocation();
  const cheque = location?.state?.cheque || {};
  const [selectedType, setSelectedType] = useState('settled');

  useEffect(() => {
    if (isNullOrEmpty(cheque)) {
      navigate(`/process/pdc-processing`);
    }
    if (cheque?.status && cheque?.status.toLowerCase() !== 'open') {
      navigate(`/process/pdc-processing/${id}/receivable/open`);
    }
  }, [cheque]);

  const handleSubmit = (values) => {
    console.log(values);
  };

  console.log('cheque', cheque);

  const renderForm = () => {
    switch (selectedType) {
      case 'settled':
        return (
          <>
            <Formik
              initialValues={{
                date: '',
                bank_account: '',
              }}
              // validationSchema={passwordResetValidationSchema}
              onSubmit={handleSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'date'}
                        type={'date'}
                        required
                        label={'Date'}
                        placeholder={'Enter Date'}
                        value={values.date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.date && errors.date}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name="bank_account"
                        label="Bank Account"
                        placeholder="Select Bank Account"
                        options={[
                          { label: 'Bank 1', value: 1 },
                          { label: 'Bank 2', value: 2 },
                        ]}
                        value={values.bank_account}
                        onChange={(v) => setFieldValue('bank_account', v.value)}
                      />
                      <ErrorMessage
                        name="bank_account"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    <div className="mb-4">
                      <p className="mb-0">Remarks:</p>
                      <p className="muted-text">
                        PDCR # {cheque?.cheque_no} Dated:{' '}
                        {formatDate(cheque?.posting_date)} From{' '}
                        {cheque?.title_of_account} for {cheque?.fcy}{' '}
                        {cheque?.fc_amount}.
                      </p>
                    </div>
                    <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'submit'}
                        text={'Process'}
                      />
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'button'}
                        text={'Cancel'}
                        variant="secondaryButton"
                        onClick={() => navigate(-1)}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </>
        );
      case 'cancelled':
        return (
          <>
            <Formik
              initialValues={{
                date: '',
                bank_account: '',
              }}
              // validationSchema={passwordResetValidationSchema}
              onSubmit={handleSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'date'}
                        type={'date'}
                        required
                        label={'Date'}
                        placeholder={'Enter Date'}
                        value={values.date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.date && errors.date}
                      />
                    </div>
                    <div className="mb-4">
                      <p className="mb-0">Remarks:</p>
                      <p className="muted-text">
                        PDCR # {cheque?.cheque_no} Dated:{' '}
                        {formatDate(cheque?.posting_date)} From{' '}
                        {cheque?.title_of_account} for {cheque?.fcy}{' '}
                        {cheque?.fc_amount}.
                      </p>
                    </div>
                    <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'submit'}
                        text={'Process'}
                      />
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'button'}
                        text={'Cancel'}
                        variant="secondaryButton"
                        onClick={() => navigate(-1)}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </>
        );
      case 'discount':
        return (
          <>
            <Formik
              initialValues={{
                date: '',
                bank_account: '',
              }}
              // validationSchema={passwordResetValidationSchema}
              onSubmit={handleSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    {/* Date */}
                    <div className="col-12 mb-3">
                      <div className="row">
                        <div className="col-12 col-sm-6">
                          <CustomInput
                            name={'date'}
                            type={'date'}
                            required
                            label={'Date'}
                            placeholder={'Enter Date'}
                            value={values.date}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.date && errors.date}
                          />
                        </div>
                      </div>
                    </div>
                    {/* Bank Account */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name="bank_account"
                        label="Bank Account"
                        placeholder="Select Bank Account"
                        options={[
                          { label: 'Bank 1', value: 1 },
                          { label: 'Bank 2', value: 2 },
                        ]}
                        value={values.bank_account}
                        onChange={(v) => setFieldValue('bank_account', v.value)}
                      />
                      <ErrorMessage
                        name="bank_account"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    {/* Discounted Amount */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'discounted_amount'}
                        type={'number'}
                        required
                        label={'Discounted Amount'}
                        placeholder={'Enter Discounted Amount'}
                        value={values.discounted_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.discounted_amount && errors.discounted_amount
                        }
                      />
                    </div>
                    {/* Collection account */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name="collection_account"
                        label="Collection account"
                        placeholder="Select Collection account"
                        options={[
                          { label: 'Account 1', value: 1 },
                          { label: 'Account 2', value: 2 },
                        ]}
                        value={values.collection_account}
                        onChange={(v) =>
                          setFieldValue('collection_account', v.value)
                        }
                      />
                      <ErrorMessage
                        name="collection_account"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    {/* Collection Amount */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'collection_amount'}
                        type={'number'}
                        required
                        label={'Collection Amount'}
                        placeholder={'Enter Collection Amount'}
                        value={values.collection_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.collection_amount && errors.collection_amount
                        }
                      />
                    </div>
                    {/* Commission account */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name="commission_account"
                        label="Commission account"
                        placeholder="Select Commission account"
                        options={[
                          { label: 'Account 1', value: 0 },
                          { label: 'Account 2', value: 1 },
                          { label: 'Account 3', value: 2 },
                        ]}
                        value={values.commission_account}
                        onChange={(v) =>
                          setFieldValue('commission_account', v.value)
                        }
                      />
                      <ErrorMessage
                        name="commission_account"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    {/* Commission Amount */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'commission_amount'}
                        type={'number'}
                        required
                        label={'Commission Amount'}
                        placeholder={'Enter Commission Amount'}
                        value={values.commission_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.commission_amount && errors.commission_amount
                        }
                      />
                    </div>
                    {/* LBD Number */}
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'lbd_number'}
                        type={'number'}
                        required
                        label={'LBD Number'}
                        placeholder={'Enter LBD Number'}
                        value={values.lbd_number}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.lbd_number && errors.lbd_number}
                      />
                    </div>

                    <div className="mb-4">
                      <p className="mb-0">Remarks:</p>
                      <p className="muted-text">
                        PDCR # {cheque?.cheque_no} Dated:{' '}
                        {formatDate(cheque?.posting_date)} From{' '}
                        {cheque?.title_of_account} for {cheque?.fcy}{' '}
                        {cheque?.fc_amount}.
                      </p>
                    </div>
                    <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'submit'}
                        text={'Process'}
                      />
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'button'}
                        text={'Cancel'}
                        variant="secondaryButton"
                        onClick={() => navigate(-1)}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </>
        );
      case 'collection':
        return (
          <>
            <Formik
              initialValues={{
                date: '',
                bank_account: '',
              }}
              // validationSchema={passwordResetValidationSchema}
              onSubmit={handleSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'date'}
                        type={'date'}
                        required
                        label={'Date'}
                        placeholder={'Enter Date'}
                        value={values.date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.date && errors.date}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name="bank_account"
                        label="Bank Account"
                        placeholder="Select Bank Account"
                        options={[
                          { label: 'Bank 1', value: 1 },
                          { label: 'Bank 2', value: 2 },
                        ]}
                        value={values.bank_account}
                        onChange={(v) => setFieldValue('bank_account', v.value)}
                      />
                      <ErrorMessage
                        name="bank_account"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    <div className="mb-4">
                      <p className="mb-0">Remarks:</p>
                      <p className="muted-text">
                        PDCR # {cheque?.cheque_no} Dated:{' '}
                        {formatDate(cheque?.posting_date)} From{' '}
                        {cheque?.title_of_account} for {cheque?.fcy}{' '}
                        {cheque?.fc_amount}.
                      </p>
                    </div>
                    <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'submit'}
                        text={'Process'}
                      />
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'button'}
                        text={'Cancel'}
                        variant="secondaryButton"
                        onClick={() => navigate(-1)}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </>
        );
      default:
        break;
    }
  };
  return (
    <>
      <div className="mb-3">
        <BackButton url={`/process/pdc-processing?tab=receivables`} />
        <h2 className="screen-title mb-0">PDC Processes</h2>
      </div>
      <div className="d-card py-45 mb-45">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <div className="row mb-3">
              <div className="col-12 col-sm-6 mb-3">
                <SearchableSelect
                  name="process_type"
                  label="Process Type"
                  placeholder="Select Process Type"
                  options={PDCProcessOpenTypeOptions}
                  value={selectedType}
                  onChange={(v) => setSelectedType(v.value)}
                />
              </div>
            </div>
            {renderForm()}
          </div>
        </div>
      </div>
    </>
  );
};

export default ReceivableOpen;
