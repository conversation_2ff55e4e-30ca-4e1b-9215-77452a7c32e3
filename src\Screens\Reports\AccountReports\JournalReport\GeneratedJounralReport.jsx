import React, { useState } from 'react';
import BackButton from '../../../../Components/BackButton';
import CustomButton from '../../../../Components/CustomButton';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import { Col, Row } from 'react-bootstrap';
import { journalReportHeaders } from '../../../../Utils/Constants/TableHeaders';
import { journalReportData, supportLogsData } from '../../../../Mocks/MockData';
import withFilters from '../../../../HOC/withFilters ';
import CustomModal from '../../../../Components/CustomModal';
import AttachmentsView from '../../../../Components/AttachmentsView/AttachmentsView';

const GeneratedJounralReport = ({ filters, setFilters, pagination }) => {
  const tableData = journalReportData.detail;
  const isLoading = false;
  const isError = false;
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <div className="d-flex flex-column gap-2 mb-4">
          <BackButton />
          <h2 className="screen-title m-0 d-inline">Journal Report</h2>
        </div>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Mark/Unmark'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Mark/Unmark');
            }}
          />
          <CustomButton
            text={'View Attachment'}
            variant={'secondaryButton'}
            onClick={() => {
              setShowAttachmentsModal(true);
              console.log('View Attachment');
            }}
          />
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={journalReportHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'Mark type', options: [{ value: 'All', label: 'All' }] },
            ]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={journalReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.type}</td>
                    <td>{item.transition_no}</td>
                    <td>{item.date}</td>
                    <td>{item.title_of_account}</td>
                    <td>{item.narration}</td>
                    <td>{item.fcy}</td>
                    <td>{item.debit}</td>
                    <td>{item.credit}</td>
                    <td>{item.base_amount}</td>
                    <td>{item.cost_center}</td>
                    <td>{item.user_id}</td>
                    <td>{item.updated_on}</td>
                    <td>{item.attachment}</td>
                    {/* <td>
                      <TableActionDropDown
                        actions={[
                          {
                            name: 'Edit',
                            icon: HiOutlinePencilSquare,
                            onClick: () => navigate(`${item.id}/edit`),
                            className: 'edit',
                          },
                          {
                            name: 'Delete',
                            icon: HiOutlineTrash,
                            onClick: () => handleDelete(item),
                            className: 'delete',
                          },
                        ]}
                      />
                    </td> */}
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          item={supportLogsData[0]}
          queryToInvalidate={'attachmentsss'}
          // deleteService={deleteDocumentRegisterAttachment}
          // uploadService={addDocumentRegisterAttachment}
          closeUploader={setShowAttachmentsModal}
        />
      </CustomModal>
      ;
    </section>
  );
};

export default withFilters(GeneratedJounralReport);
