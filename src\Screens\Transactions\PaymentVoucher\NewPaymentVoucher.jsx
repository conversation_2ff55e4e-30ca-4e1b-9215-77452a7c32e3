import { Form, Formik } from 'formik';
import React, { useEffect, useRef, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import ExchangeRatesCard from '../../../Components/ExchangeRatesCard/ExchangeRatesCard';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { MOCK_EXCHANGE_RATES } from '../../../Mocks/MockData';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import useFormStore from '../../../Stores/FormStore.js';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs.jsx';

import {
  getChequeNumberByBank,
  getPaymentVoucherMode,
} from '../../../Services/Transaction/JournalVoucher.js';
import useCurrencyRate from '../../../Hooks/useCurrencyRate';
import SignatureCanvas from 'react-signature-canvas';
import { getBeneficiaryRegisterListing } from '../../../Services/Masters/BeneficiaryRegister.js';
import { createPaymentVoucher } from '../../../Services/Transaction/PaymentVoucher.js';
import { showToast } from '../../../Components/Toast/Toast.jsx';
import { showErrorToast } from '../../../Utils/Utils.jsx';
import useSettingsStore from '../../../Stores/SettingsStore.js';
import { getBenefeciariesByAccount } from '../../../Services/Transaction/ReceiptVoucher.js';

const NewPaymentVoucher = ({
  isDisabled = false,
  setIsDisabled,
  setPageState,
  setSearchTerm,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  newlyCreatedBeneficiary,
  accountData,
  modesData,
  date,
  vatData,
  lastVoucherNumbers,
  updatePrintSetting,
  onFormDataChange,
  restoreValuesFromStore,
  currencyOptions,
  setCurrencyToSelect,
  setShowMissingCurrencyRateModal,
}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const formikRef = useRef();
  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [selectedBank, setSelectedBank] = useState(null);
  const [hasShownModal, setHasShownModal] = useState(false);
  const [voucherDate, setVoucherDate] = useState(
    new Date().toISOString().split('T')[0]
  );
  const [dueDate, setDueDate] = useState('');
  const [isDueDateEditable, setIsDueDateEditable] = useState(false);
  const [isChequeFieldEnabled, setIsChequeFieldEnabled] = useState(false);
  const [addedAttachments, setAddedAttachments] = useState(null);
  const [outOfScope, setOutOfScope] = useState('');
  const [specialCommissionValues, setSpecialCommissionValues] = useState({});
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);
  const sigCanvas = useRef(null);
  const [trimmedDataURL, setTrimmedDataURL] = useState(null);
  const [selectedLedgerAccount, setSelectedLedgerAccount] = useState(null);
  const [errorSignature,setErrorSignature] = useState(false)
  function clear() {
    sigCanvas.current.clear();
  }

  function trim() {
    setTrimmedDataURL(sigCanvas.current.toDataURL());
    setErrorSignature(false)
  }

  // Access the form store
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'payment-voucher'; // Unique identifier for this form

  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  // load saved form if present
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);

    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);
      let specialCommissionData = {};

      specialCommissionData.commission_type = savedValues?.commission_type;
      specialCommissionData.amount = savedValues?.amount;
      specialCommissionData.ledger = [
        { label: 'PL', value: 'party' },
        { label: 'GL', value: 'general' },
        { label: 'WIC', value: 'walkin' },
      ].find((x) => savedValues.ledger == x.value);
      specialCommissionData.account_id = getAccountsByTypeOptions(
        specialCommissionData?.ledger?.value
      ).find((x) => x.value == savedValues?.account_id);

      specialCommissionData.currency = currencyOptions.find(
        (x) => x.value == savedValues?.currency_id
      );

      setSpecialCommissionValues(specialCommissionData);
      formikRef.current.setValues(savedValues);
      setIsDisabled(false);

      if (hasFormValues('special-commission')) {
        setAddedSpecialCommissionValues(getFormValues('special-commission'));
      }
      // Clear lastVisitedPage so it doesn't persist beyond one use
      clearLastVisitedPage(formId);
    }
  }, []);

  useEffect(() => {
    if (lastVoucherNumbers?.current) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        current: lastVoucherNumbers?.current,
      }));
    }
    if (date) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        date: date,
      }));

      // Set due_date to date if mode is Bank
      if (formikRef.current?.values.mode === 'Bank') {
        formikRef.current.setFieldValue('due_date', date);
      }
    }
  }, [lastVoucherNumbers?.current, date]);

  useEffect(() => {
    formikRef.current.setFieldValue('signature', trimmedDataURL);
  }, [trimmedDataURL]);

  // Fetch currency rate for the selected Currency
  const { data: currencyRate, isLoading: isLoadingCurrencyRate } =
    useCurrencyRate(selectedCurrency, date);

  // Fetch cheques for selected bank

  // Modify the useEffect to include hasShownModal in its dependencies
  useEffect(() => {
    if (
      selectedCurrency &&
      currencyRate &&
      !currencyRate?.rate &&
      !hasShownModal
    ) {
      formikRef.current.setFieldValue('currency_id', '');
      setCurrencyToSelect(selectedCurrency);
      setShowMissingCurrencyRateModal(true);
      setHasShownModal(true);
    }
  }, [selectedCurrency, currencyRate?.rate, hasShownModal]);

  const handleResetRows = () => {
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // Clear saved form values when resetting
    clearFormValues(formId);
    clearFormValues('special-commission');
    setAddedSpecialCommissionValues(null);
  };

  // const handleSubmit = () => {
  //   const formValues = formikRef.current.values;
  //   console.log('formValues', formValues);
  //   console.log('selectedFiles', selectedFiles);
  //   let payload = {
  //     ...formValues,
  //     ...selectedFiles,
  //   };
  //
  //   if (payload.mode === 'online') {
  //     setShowPaymentModal(true);
  //   } else {
  //     console.log('submit payload:', payload);
  //   }
  // };

  const createPaymentVoucherMutation = useMutation({
    mutationFn: createPaymentVoucher,
    onSuccess: (data) => {
      showToast('Payment Voucher Created!', 'success');
      if (getPrintSettings('payment_voucher')) {
        window.open(data.detail?.pdf_url, '_blank');
      }
      queryClient.invalidateQueries(['paymentVoucherListing']);
      handleResetRows();
    },
    onError: (error) => {
      console.error('Error creating Payment Voucher', error);
    },
  });

  const handleSubmit = async () => {
    if (!formikRef.current) return;
    // Validate the form
    const errors = await formikRef.current.validateForm();
    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show errors
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return; // Do not submit if there are errors
    }

    const formValues = formikRef.current.values;

    let payload = {
      date,
      ...formValues,
      ...addedAttachments,
      ...(formValues.vat_terms.startsWith('A small popup') && {
        out_of_scope_reason: outOfScope,
      }),
      mode: formValues.mode.charAt(0).toUpperCase() + formValues.mode.slice(1),
      vat_amount: formValues.vat_amount,
      ...(addedSpecialCommissionValues
        ? { special_commission: addedSpecialCommissionValues }
        : {}),
    };

    console.log('values', payload);

    createPaymentVoucherMutation.mutate(payload);
  };

  const handleVatOutOfScope = (values) => {
    setOutOfScope(values.out_of_scope);
    setShowVatOutOfScopeModal(false);
  };

  // Restore form data from store for Rate of Exchange flow
  useEffect(() => {
    if (restoreValuesFromStore) {
      const savedFormData = getFormValues(formId);
      if (savedFormData && formikRef.current) {
        formikRef.current.setValues(savedFormData.values || {});
        setAddedAttachments(savedFormData.addedAttachments || null);
        setIsDisabled(false);
        clearFormValues(formId);
        clearLastVisitedPage(formId);
      }
    }
  }, [restoreValuesFromStore]);
  // ...existing code...

  // Notify parent of form data changes (for saving before navigation)
  useEffect(() => {
    if (onFormDataChange && formikRef.current) {
      onFormDataChange({
        values: formikRef.current.values,
        addedAttachments,
      });
      // formikRef.current.setFieldValue('paid_to_id');
    }
  }, [formikRef.current?.values, addedAttachments, onFormDataChange]);

  const handleCancel = () => {
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  const getAccountsByTypeOptions = (accountType) => {
    //console.log(accountType);
    // console.log(accountData);

    if (!accountType) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
      accountData[accountType] || {};

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    // if (error) {
    //   console.error('Unable to fetch Accounts', errorMessage);
    //   return [{ label: 'Unable to fetch Accounts', value: null }];
    // }

    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];
    switch (accountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: null,
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: null,
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: null,
        });
        break;
      default:
        break;
    }
    return options;
  };

  const getAccountsByTypeMode = (mode) => {
    if (!mode) {
      return [{ label: 'Select Mode', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } = modesData[mode] || {};

    console.log(modesData[mode]);

    if (mode === 'bank') {
      setDueDate(voucherDate);
      setIsDueDateEditable(true);
      setIsChequeFieldEnabled(true);
    } else if (mode === 'pdc') {
      const tomorrow = new Date(voucherDate);
      tomorrow.setDate(tomorrow.getDate() + 1);
      setDueDate(tomorrow.toISOString().split('T')[0]);
      setIsDueDateEditable(true);

      setIsChequeFieldEnabled(true);
    } else {
      setDueDate('');
      setIsDueDateEditable(false);
      setIsChequeFieldEnabled(false);
    }

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Mode', errorMessage);
      return [{ label: 'Unable to fetch Mode', value: null }];
    }
    return (
      data?.map((x) => ({
        value: x?.id,
        label: x?.account_name,
      })) || []
    );
  };

  const getVATTermsOptions = () => {
    if (vatData.isLoadingVatType)
      return [
        {
          label: 'Loading...',
          value: '',
        },
      ];
    if (vatData.isErrorVatType) {
      console.error('Unable to fetch VAT Terms', vatData.errorMessage);
      return [{ label: 'Unable to fetch VAT Terms', value: null }];
    }
    return vatData?.vatType?.vats?.map((item) => ({
      label: `${item.title}${
        !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
      }`,
      value: item.percentage,
    }));
  };

  const handleNavigateToSpecialCommissionPage = () => {
    // Check if required fields are filled
    const requiredFields = [
      'ledger',
      'account_id',
      'amount',
      'currency_id',
      // 'commission_type',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    console.log(missingFields);
    if (missingFields.length > 0) {
      // Set touched for all required fields to show errors
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      return;
    }

    // If all required fields are filled, proceed
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, formikRef.current.values);
      setLastVisitedPage(formId, 'special-commission');
    }

    // Check if we already have special commission data
    const hasExistingCommission = !!addedSpecialCommissionValues;

    console.log(specialCommissionValues);

    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'rv',
        values: {
          rvValues: specialCommissionValues,
          isEdit: hasExistingCommission,
        },
      },
    });
  };

  let getBeneficiariesData = [];

  const {
    data: modeBeneficiaries,
    isLoading: isLoadingBeneficiaries,
    isError: isErrorBeneficiaries,
    error: errorBeneficiaries,
  } = useQuery({
    queryKey: ['per_page', 50],
    queryFn: getBeneficiaryRegisterListing,
    staleTime: 1000 * 60 * 5,
  });

  // Defensive check depending on API structure
  if (Array.isArray(modeBeneficiaries)) {
    getBeneficiariesData = modeBeneficiaries.map((ben) => ({
      label: ben.account,
      value: ben.id,
    }));
  } else if (Array.isArray(modeBeneficiaries?.data)) {
    getBeneficiariesData = modeBeneficiaries.data.map((ben) => ({
      label: ben.account,
      value: ben.id,
    }));
  }

  useEffect(() => {
    modeBeneficiaries;
  }, [modeBeneficiaries]);

  // console.log('ddd', asif);

  const {
    data: modeCheques,
    isLoading: isLoadingCheques,
    isError: isErrorCheques,
    error: errorCheques,
  } = useQuery({
    queryKey: ['bank_id', selectedBank],
    queryFn: () => getChequeNumberByBank(selectedBank),
    staleTime: 1000 * 60 * 5,
  });

  // console.log(modeCheques);

  const chequeOptions =
    modeCheques?.map((cheque) => ({
      label: cheque.cheque_number, // adjust this based on your API response
      value: cheque.cheque_number,
    })) || [];

  useEffect(() => {
    modeCheques;
  }, [modeCheques]);

  // Get Benefeciaries from selected Ledger+Account
  const {
    data: beneficiaryAccounts,
    isLoading: isLoadingBeneficiary,
    isError: isErrorBeneficiary,
    error: errorBeneficiary,
  } = useQuery({
    queryKey: ['beneficiaries', selectedLedgerAccount],
    queryFn: () => getBenefeciariesByAccount(selectedLedgerAccount),
    enabled: !!selectedLedgerAccount,
  });

  // Make options array from the benfeciary queries call
  const getBeneficiaryOptions = (account_id) => {
    if (!account_id) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const data = beneficiaryAccounts;
    const loading = isLoadingBeneficiary;
    const error = isErrorBeneficiary;
    const errorMessage = errorBeneficiary;

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch beneficiaries', errorMessage);
      return [{ label: 'Unable to fetch beneficiaries', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];

    options.push({
      label: `Add New Beneficiary`,
      value: null,
    });

    return options;
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            ledger: '',
            account_id: '',
            paid_to: '',
            paid_to_id: '',
            mode: '',
            mode_account_id: '',
            cheque_number: '',
            due_date: '',
            narration: '',
            currency: '',
            amount: '',
            commission_type: '',
            commission_percentage: '',
            vat_terms: '',
            vat_amount: '',
            net_total: '',
            comment: '',
            signature: '',
          }}
          validate={(values) => {
            const errors = {};

            // Required fields for special commission
            if (!values.ledger) errors.ledger = 'Ledger is required';
            if (!values.account_id) errors.account_id = 'Account is required';
            if (!values.amount) errors.amount = 'Amount is required';
            if (!trimmedDataURL) {
              setErrorSignature(true);
              errors.signature = 'Signature is required';
            }
            if (!values.narration) errors.narration = 'narration is required';
            if (!values.currency_id)
              errors.currency_id = 'Currency is required';
            // if (!values.commission_type)
            //   errors.commission_type = 'Commission Type is required';

            return errors;
          }}
        >
          {({
            values,
            touched,
            errors,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-xxl-9">
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-45">
                      <div className="combined-select-container">
                        <label className="mainLabel">Ledger</label>
                        <div
                          className={`combined-select-input ${
                            isDisabled ? 'disabled-combined-select' : ''
                          }`}
                        >

                          <div className="combined-select-left">
                            <SearchableSelect
                              name={'ledger'}
                              className="ledger-select__control"
                              options={[
                                { label: 'PL', value: 'party' },
                                { label: 'GL', value: 'general' },
                                { label: 'WIC', value: 'walkin' },
                              ]}
                              isDisabled={isDisabled}
                              placeholder={'Ledger'}
                              value={values.ledger}
                              onChange={(selected) => {
                                setFieldValue('ledger', selected.value);
                              }}
                              onBlur={handleBlur}
                              error={touched.ledger && errors.ledger}
                            />
                          </div>
                          <div className="separator-between-selects">|</div>
                          <div className="combined-select-right">
                            <SearchableSelect
                              name={'account_id'}
                              className={'ledger-select__control'}
                              options={getAccountsByTypeOptions(values.ledger)}
                              isDisabled={isDisabled}
                              placeholder={'Select Account'}
                              value={values.account_id}
                              onChange={(selected) => {
                                if (
                                  selected.label
                                    ?.toLowerCase()
                                    ?.startsWith('add new')
                                ) {
                                  setShowAddLedgerModal(
                                    selected.label?.toLowerCase()
                                  );
                                } else {
                                  setFieldValue('account_id', selected.value);
                                  setSelectedLedgerAccount(selected.value);
                                }
                              }}
                              onBlur={handleBlur}
                              error={touched.account_id && errors.account_id}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      {/*<SearchableSelect*/}
                      {/*  name={'paid_to'}*/}
                      {/*  label={'Paid To'}*/}
                      {/*  options={getBeneficiariesData}*/}

                      {/*  isDisabled={isDisabled}*/}
                      {/*  placeholder={'Select Paid To'}*/}
                      {/*  value={values.paid_to}*/}
                      {/*  onChange={(selected) => {*/}
                      {/*    if (*/}
                      {/*      selected.label?.toLowerCase()?.startsWith('add new')*/}
                      {/*    ) {*/}
                      {/*      setShowAddLedgerModal(*/}
                      {/*        selected.label?.toLowerCase(),*/}
                      {/*      );*/}
                      {/*    } else {*/}
                      {/*      setFieldValue('paid_to', selected.value);*/}
                      {/*      setFieldValue('paid_to_id',selected.value);*/}
                      {/*    }*/}
                      {/*  }}*/}
                      {/*  onBlur={handleBlur}*/}
                      {/*/>*/}

                      <SearchableSelect
                        name={'paid_to'}
                        label={'Paid To'}
                        options={getBeneficiaryOptions(values.account_id)}
                        isDisabled={isDisabled}
                        placeholder={'Select Paid To'}
                        value={values.paid_to || newlyCreatedBeneficiary?.id}
                        onChange={(selected) => {
                          if (
                            selected.label?.toLowerCase()?.startsWith('add new')
                          ) {
                            setShowAddLedgerModal(
                              selected.label?.toLowerCase()
                            );
                          } else {
                            setFieldValue('paid_to', selected.value);
                            setFieldValue('paid_to_id', selected.value);
                          }
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    {/* MergeSelect */}
                    <div className="col-12 col-sm-6 mb-45">
                      <div className="combined-select-container">
                        <label className="mainLabel">Mode</label>
                        <div
                          className={`combined-select-input ${
                            isDisabled ? 'disabled-combined-select' : ''
                          }`}
                        >
                          <div className="combined-select-left">
                            <SearchableSelect
                              name={'mode'}
                              className={'mode-select__control'}
                              options={[
                                {
                                  label: 'Cash',
                                  value: 'cash',
                                },
                                {
                                  label: 'Bank',
                                  value: 'bank',
                                },
                                {
                                  label: 'PDC',
                                  value: 'pdc',
                                },
                                {
                                  label: 'Online',
                                  value: 'online',
                                },
                              ]}
                              isDisabled={isDisabled}
                              placeholder={'Mode'}
                              value={values.mode}
                              onChange={(selected) => {
                                setFieldValue('mode', selected.value);
                              }}
                              onBlur={handleBlur}
                            />
                          </div>
                          <div className="separator-between-selects">|</div>
                          <div className="combined-select-right">
                            <SearchableSelect
                              name={'mode_account_id'}
                              className={'account-select__control'}
                              options={getAccountsByTypeMode(values.mode)}
                              isDisabled={isDisabled}
                              placeholder={'Select Account'}
                              value={values.mode_account_id}
                              onChange={(selected) => {
                                setFieldValue(
                                  'mode_account_id',
                                  selected.value
                                );
                                setSelectedBank(selected.value);
                                console.log(selectedBank);
                              }}
                              onBlur={handleBlur}

                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'cheque_number'}
                        label={'Cheque Number'}
                        options={chequeOptions}
                        isDisabled={!isChequeFieldEnabled}
                        placeholder={'Select Cheque Number'}
                        value={values.cheque_number}
                        onChange={(selected) => {
                          setFieldValue('cheque_number', selected.value);
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'due_date'}
                        label={'Due Date'}
                        type={'date'}
                        disabled={!isDueDateEditable}
                        value={dueDate}
                        min={
                          values.mode === 'pdc'
                            ? new Date(
                                new Date(voucherDate).setDate(
                                  new Date(voucherDate).getDate() + 1
                                )
                              )
                                .toISOString()
                                .split('T')[0]
                            : undefined
                        }
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.due_date && errors.due_date}
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <CustomInput
                        name={'narration'}
                        label={'Narration'}
                        type={'textarea'}
                        rows={4}
                        placeholder={'Enter Narration'}
                        disabled={isDisabled}
                        value={values.narration}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.narration && errors.narration}
                      />
                    </div>
                    {/* MergeSelect */}
                    {/*<div className="col-12 col-sm-6 mb-45">*/}
                    {/*  <div className="combined-select-container">*/}
                    {/*    <label className="mainLabel">Amount</label>*/}
                    {/*    <div*/}
                    {/*      className={`combined-select-input ${*/}
                    {/*        isDisabled ? 'disabled-combined-select' : ''*/}
                    {/*      }`}*/}
                    {/*    >*/}
                    {/*      <div className="combined-select-left">*/}
                    {/*        <SearchableSelect*/}
                    {/*          name={'currency'}*/}
                    {/*          className={'ledger-select__control'}*/}
                    {/*          options={[*/}
                    {/*            {*/}
                    {/*              label: 'USD',*/}
                    {/*              value: 'usd',*/}
                    {/*            },*/}
                    {/*            {*/}
                    {/*              label: 'DHS',*/}
                    {/*              value: 'dhs',*/}
                    {/*            },*/}
                    {/*            {*/}
                    {/*              label: 'EUR',*/}
                    {/*              value: 'eur',*/}
                    {/*            },*/}
                    {/*          ]}*/}
                    {/*          isDisabled={isDisabled}*/}
                    {/*          placeholder={'Currency'}*/}
                    {/*          value={values.currency}*/}
                    {/*          onChange={(selected) => {*/}
                    {/*            setFieldValue('currency', selected.value);*/}
                    {/*          }}*/}
                    {/*          onBlur={handleBlur}*/}
                    {/*        />*/}
                    {/*      </div>*/}
                    {/*      <div className="separator-between-selects">|</div>*/}
                    {/*      <div className="combined-select-right">*/}
                    {/*        <CustomInput*/}
                    {/*          name={'amount'}*/}
                    {/*          inputClass={'ledger-select__control mt-3'}*/}
                    {/*          type={'number'}*/}
                    {/*          disabled={isDisabled}*/}
                    {/*          placeholder={'Enter Amount'}*/}
                    {/*          value={values.amount}*/}
                    {/*          onChange={handleChange}*/}
                    {/*          onBlur={handleBlur}*/}
                    {/*        />*/}
                    {/*      </div>*/}
                    {/*    </div>*/}
                    {/*  </div>*/}
                    {/*</div>*/}
                    <div className="col-12 col-sm-6 mb-45">
                      <CombinedInputs
                        label="Currency"
                        type1="select"
                        type2="input"
                        name1="currency_id"
                        name2="amount"
                        value1={values.currency_id}
                        value2={values.amount}
                        options1={currencyOptions}
                        isDisabled={isDisabled}
                        handleBlur={handleBlur}
                        placeholder1="Select Currency"
                        placeholder2="Enter Amount"
                        inputType2="number"
                        className1="currency"
                        className2="amount"
                        onChange1={(selected) => {
                          setSelectedCurrency(selected.value);
                          setHasShownModal(false);
                          setFieldValue('currency_id', selected.value);
                          setSpecialCommissionValues((prev) => ({
                            ...prev,
                            currency: selected,
                          }));
                        }}
                        onChange2={(e) => {
                          handleChange(e);
                          let commission = parseFloat(values.commission || 0);
                          let amount = parseFloat(e.target.value || 0);
                          let commissionAmount = (commission / 100) * amount;
                          let vat = vatData.vatType?.vat_percentage
                            ? (commissionAmount *
                                vatData.vatType?.vat_percentage) /
                              100
                            : !isNaN(values.vat_terms)
                            ? (commissionAmount * values.vat_terms) / 100
                            : 0;

                          let value =
                            Math.round(
                              (amount +
                                commissionAmount +
                                vat +
                                Number.EPSILON) *
                                1000000
                            ) / 1000000;
                          setFieldValue('net_total', value);
                          setSpecialCommissionValues((prev) => ({
                            ...prev,
                            amount,
                          }));
                        }}
                        additionalProps={{
                          isLoadingCurrencyRate: isLoadingCurrencyRate,
                        }}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'commission_type'}
                        label={'Commission Type'}
                        options={[
                          {
                            label: 'Income',
                            value: 'Income',
                          },
                          {
                            label: 'Expense',
                            value: 'Expense',
                          },
                        ]}
                        isDisabled={isDisabled}
                        placeholder={'Select Commission Type'}
                        value={values.commission_type}
                        onChange={(selected) => {
                          setFieldValue('commission_type', selected.value);
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-12 mb-45">
                      <div className="col-12 col-sm-12 mb-45">
                        <CombinedInputs
                          label="Commission Percentage"
                          type1="input"
                          type2="input"
                          name1="commission"
                          name2="commission_amount"
                          value1={values.commission}
                          value2={values.commission_amount}
                          isDisabled={
                            isDisabled ||
                            addedSpecialCommissionValues?.total_commission
                          }
                          handleBlur={handleBlur}
                          placeholder1="Enter Commission %"
                          placeholder2="Commission Amount"
                          inputType1="number"
                          inputType2="text"
                          className1="commission"
                          className2="commission-amount"
                          inputProps1={{
                            min: 0,
                            max: 100,
                          }}
                          inputProps2={
                            {
                              // readOnly: true,
                            }
                          }
                          onChange1={(v) => {
                            handleChange(v);
                            if (v.target.value < 0) {
                              return;
                            }
                            let commission = parseFloat(v.target.value || 0);
                            let amount = parseFloat(values.amount || 0);
                            let commissionAmount = (commission / 100) * amount;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue(
                              'commission_amount',
                              commissionAmount
                            );
                          }}
                          onChange2={(e) => {
                            handleChange(e);
                            let commissionAmount = parseFloat(
                              e.target.value || 0
                            );
                            let amount = parseFloat(values.amount || 0);
                            let commission =
                              amount !== 0
                                ? (commissionAmount / amount) * 100
                                : 0;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue('commission', commission);
                          }}
                        />
                      </div>
                    </div>
                    {vatData?.vatType?.vat_type === 'variable' && (
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'vat_terms'}
                          label={'VAT %'}
                          options={getVATTermsOptions()}
                          isDisabled={isDisabled}
                          placeholder={'Select VAT %'}
                          value={values.vat_terms}
                          onChange={(selected) => {
                            if (
                              selected.value.startsWith(
                                'A small popup will appear'
                              )
                            ) {
                              setShowVatOutOfScopeModal(true);
                            } else {
                              let commission = parseFloat(
                                values.commission || 0
                              );
                              let amount = parseFloat(
                                values.amount ??
                                  getFormValues('special-commission')
                                    ?.total_commission ??
                                  0
                              );
                              let commissionAmount =
                                (commission / 100) * amount;
                              let vat = vatData.vatType?.vat_percentage
                                ? (commissionAmount *
                                    vatData.vatType?.vat_percentage) /
                                  100
                                : !isNaN(selected.value)
                                ? (commissionAmount * selected.value) / 100
                                : '';

                              setFieldValue(
                                'net_total',
                                Math.round(
                                  (amount +
                                    commissionAmount +
                                    (vat || 0) +
                                    Number.EPSILON) *
                                    1000000
                                ) / 1000000
                              );
                            }
                            setFieldValue('vat_terms', selected.value);
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                    )}
                    {vatData?.vatType?.vat_type === 'fixed' && (
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name={'vat_percentage'}
                          label={'VAT %'}
                          type={'number'}
                          disabled={true}
                          placeholder={'Enter VAT Percentage'}
                          value={
                            vatData.vatType?.vat_percentage
                              ? vatData.vatType?.vat_percentage
                              : !isNaN(values.vat_terms)
                              ? values.vat_terms
                              : ''
                          }
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>
                    )}
                    <div className="col-12 col-sm-6 mb-45">
                      <CustomInput
                        name={'vat_amount'}
                        label={'VAT Amount'}
                        type={'text'}
                        disabled={true}
                        placeholder={'Enter VAT Amount'}
                        value={
                          vatData.isLoadingVatType
                            ? 'Loading...'
                            : (() => {
                                const commissionAmount =
                                  addedSpecialCommissionValues?.total_commission ??
                                  values.commission_amount;
                                const vatPercentage =
                                  vatData.vatType?.vat_percentage ||
                                  (!isNaN(values.vat_terms)
                                    ? values.vat_terms
                                    : 0);
                                return commissionAmount && vatPercentage
                                  ? (commissionAmount * vatPercentage) / 100
                                  : '';
                              })()
                        }
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <CustomInput
                        name={'net_total'}
                        label={'Net Total'}
                        type={'number'}
                        disabled={true}
                        placeholder={'Enter Net Total'}
                        value={values.net_total}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/*<div className="col-12 col-sm-6 mb-45">*/}
                    {/*  <SearchableSelect*/}
                    {/*    name={'vat_terms'}*/}
                    {/*    label={'VAT Terms'}*/}
                    {/*    options={[*/}
                    {/*      {*/}
                    {/*        label: 'Standard Rate 5%',*/}
                    {/*        value: 'standard',*/}
                    {/*      },*/}
                    {/*      {*/}
                    {/*        label: 'Out Of Scope',*/}
                    {/*        value: 'out_of_scope',*/}
                    {/*      },*/}
                    {/*    ]}*/}
                    {/*    isDisabled={isDisabled}*/}
                    {/*    placeholder={'Select VAT Terms'}*/}
                    {/*    value={values.vat_terms}*/}
                    {/*    onChange={(selected) => {*/}
                    {/*      if (selected.value === 'out_of_scope') {*/}
                    {/*        setShowVatOutOfScopeModal(true);*/}
                    {/*      }*/}
                    {/*      setFieldValue('vat_terms', selected.value);*/}
                    {/*    }}*/}
                    {/*    onBlur={handleBlur}*/}
                    {/*  />*/}
                    {/*</div>*/}
                    {/*<div className="col-12 col-sm-6 mb-45">*/}
                    {/*  <CustomInput*/}
                    {/*    name={'vat_amount'}*/}
                    {/*    label={'VAT Amount'}*/}
                    {/*    type={'number'}*/}
                    {/*    disabled={isDisabled}*/}
                    {/*    placeholder={'Enter VAT Amount'}*/}
                    {/*    value={values.vat_amount}*/}
                    {/*    onChange={handleChange}*/}
                    {/*    onBlur={handleBlur}*/}
                    {/*  />*/}
                    {/*</div>*/}
                    {/*<div className="col-12 col-sm-6 mb-45">*/}
                    {/*  <CustomInput*/}
                    {/*    name={'net_total'}*/}
                    {/*    label={'Net Total'}*/}
                    {/*    type={'number'}*/}
                    {/*    disabled={'disabled'}*/}
                    {/*    value={values.net_total || 0.0}*/}
                    {/*    onChange={handleChange}*/}
                    {/*    onBlur={handleBlur}*/}
                    {/*  />*/}
                    {/*</div>*/}
                    <div className="col-12 mb-3">
                      <CustomInput
                        name={'comment'}
                        label={'Comment'}
                        type={'textarea'}
                        rows={4}
                        placeholder={'Enter Comment'}
                        disabled={isDisabled}
                        value={values.comment}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.comment && errors.comment}
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <label>Signature</label>
                      <SignatureCanvas
                        ref={sigCanvas}
                        penColor="green"
                        canvasProps={{
                          height: 200,
                          className: 'sigCanvas',
                        }}
                      />
                      <span style={{color:'red'}}>{errorSignature && 'Signature is Required'}</span>
                      <div>
                        <button
                          className="customButton"
                          style={{ width: '20px' }}
                          onClick={clear}
                        >
                          Clear
                        </button>
                        <button
                          className="customButton"
                          style={{ width: '20px' }}
                          onClick={trim}
                        >
                          Trim
                        </button>
                      </div>
                      {trimmedDataURL ? (
                        <img alt="signature" src={trimmedDataURL} />
                      ) : null}

                      {/*<CustomInput*/}
                      {/*  name={'signature'}*/}
                      {/*  label={'Signature'}*/}
                      {/*  type={'textarea'}*/}
                      {/*  rows={4}*/}
                      {/*  disabled={isDisabled}*/}
                      {/*  value={values.signature}*/}
                      {/*  onChange={handleChange}*/}
                      {/*  onBlur={handleBlur}*/}
                      {/*  error={touched.signature && errors.signature}*/}
                      {/*/>*/}
                    </div>
                  </div>
                  <div className="d-flex mb-5">
                    {/*<CustomButton*/}
                    {/*  onClick={() => {*/}
                    {/*    navigate('/transactions/special-comission');*/}
                    {/*  }}*/}
                    {/*  disabled={isDisabled}*/}
                    {/*  text="Add Special Commission"*/}
                    {/*/>*/}

                    <CustomButton
                      type={'button'}
                      onClick={handleNavigateToSpecialCommissionPage}
                      text={`${
                        !!addedSpecialCommissionValues ? 'Edit' : 'Add'
                      } Special Commission`}
                      disabled={!!values.commission || isDisabled}
                    />
                  </div>
                </div>
                {!isDisabled && (
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                    <div className="row">
                      {/* Right side cards */}
                      <div
                        className="col-12 mb-5"
                        style={{ maxWidth: '350px' }}
                      >
                        <AccountBalanceCard />
                        {(values.mode === 'bank' || values.mode === 'cash') && (
                          <AccountBalanceCard />
                        )}
                        <ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} />
                      </div>
                    </div>
                  </div>
                )}

                <div className="d-flex flex-wrap justify-content-start mb-5">
                  <div className="d-inline-block mt-3">
                    <CustomCheckbox
                      label="Account Balance"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                      onChange={() => {}}
                    />
                    <CustomCheckbox
                      label="Print"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      {/*<VoucherNavigationBar*/}
      {/*  isDisabled={isDisabled}*/}
      {/*  actionButtons={[*/}
      {/*    { text: 'Save', onClick: handleSubmit },*/}
      {/*    { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },*/}
      {/*  ]}*/}
      {/*  onAttachmentClick={() => setUploadAttachmentsModal(true)}*/}
      {/*  lastVoucherHeading="Last PV Number"*/}
      {/*  lastVoucherNumber={23}*/}
      {/*/>*/}

      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleResetRows,
            variant: 'secondaryButton',
          },
        ]}
        loading={createPaymentVoucherMutation.isPending}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />

      {/* Upload Attachements Modal */}
      {/*<CustomModal*/}
      {/*  show={uploadAttachmentsModal}*/}
      {/*  close={() => setUploadAttachmentsModal(false)}*/}
      {/*  background={true}*/}
      {/*>*/}
      {/*  <AttachmentsView*/}
      {/*    uploadOnly*/}
      {/*    getUploadedFiles={setSelectedFiles}*/}
      {/*    closeUploader={() => setUploadAttachmentsModal(false)}*/}
      {/*  />*/}
      {/*</CustomModal>*/}

      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachments}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    r
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Submit'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => {
                        setShowVatOutOfScopeModal(false);
                        formikRef.current.values.vat_terms = '';
                      }}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>

      {/* Payment Modal */}
      <CustomModal
        show={showPaymentModal}
        close={() => {
          setShowPaymentModal(false);
        }}
        size="lg"
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Payment</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              cardholderName: '',
              number: '',
              cvv: '',
              exp_month: '',
              bank_account_number: '',
              swift_bic_code: '',
              routing_number: '',
              iban: '',
              account_name: '',
            }}
            // validationSchema={paymentValidationSchema}
            onSubmit={() => {
              console.log('Payment');
              setShowPaymentModal(false);
            }}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <Row className="mb-3">
                  <h4 className="screen-title-body">Payer’s Detail</h4>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="cardholderName"
                      label="Cardholder Name"
                      placeholder="Enter Cardholder Name"
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.cardholderName && errors.cardholderName}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="number"
                      label="Card Number"
                      placeholder="Enter Card Number"
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.number && errors.number}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="cvv"
                      label="CVV Number"
                      placeholder="Enter CVV"
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.cvv && errors.cvv}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name="exp_month"
                      label="Validaty"
                      placeholder="MM/YYYY"
                      required
                      type="month"
                      min="1"
                      max="12"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.exp_month && errors.exp_month}
                    />
                  </Col>
                </Row>
                <Row>
                  <h4 className="screen-title-body">Receiver’s Detail</h4>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'bank_account_number'}
                      type={'text'}
                      label={'Bank Account Number'}
                      placeholder={'Enter Bank Account Number'}
                      value={values.bank_account_number}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched.bank_account_number &&
                        errors.bank_account_number
                      }
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'swift_bic_code'}
                      type={'text'}
                      label={'SWIFT/BIC Code'}
                      placeholder={'Enter SWIFT Code'}
                      value={values.swift_bic_code}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.swift_bic_code && errors.swift_bic_code}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'routing_number'}
                      type={'text'}
                      label={'Routing Number'}
                      placeholder={'Enter Routing Number'}
                      value={values.routing_number}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.routing_number && errors.routing_number}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'iban'}
                      type={'text'}
                      label={'IBAN'}
                      placeholder={'Enter IBAN'}
                      value={values.iban}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.iban && errors.iban}
                    />
                  </Col>
                  <Col xs={12} sm={6} className="mb-3">
                    <CustomInput
                      name={'account_name'}
                      type={'text'}
                      label={'Account Name'}
                      placeholder={'Enter Account Name'}
                      value={values.account_name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.account_name && errors.account_name}
                    />
                  </Col>
                </Row>
                <div className="d-flex justify-content-start gap-3 mt-4">
                  <CustomButton
                    variant="primary"
                    type="submit"
                    text="Pay Now"
                    // loading={paymentMutation.isPending}
                    // disabled={paymentMutation.isPending}
                  />
                  <CustomButton
                    variant="secondaryButton"
                    type="butston"
                    text="Cancel"
                    onClick={() => {
                      setShowPaymentModal(false);
                    }}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default NewPaymentVoucher;
