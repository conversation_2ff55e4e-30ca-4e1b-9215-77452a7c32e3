import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Form, Formik } from 'formik';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import FileDisplayList from '../../../Components/FileDisplayList/FileDisplayList';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { showToast } from '../../../Components/Toast/Toast';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { createSuspenseVoucher } from '../../../Services/Transaction/SuspenseVoucher';
import useFormStore from '../../../Stores/FormStore';
import useSettingsStore from '../../../Stores/SettingsStore';
import { suspenseVoucherNewHeaders } from '../../../Utils/Constants/TableHeaders';
import { showErrorToast } from '../../../Utils/Utils';
import SuspenseVoucherRow from './SuspenseVoucherRow';

const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      narration: '',
      debit: '',
      credit: '',
      status: 'open',
    };
  });
  return rows;
};
const INITIAL_STATE = generateInitialRows(5);

const NewSuspenseVoucher = ({
  date,
  currencyOptions,
  getAccountsByTypeOptions,
  getOfficeLocationOptions,
  isDisabled = false,
  setIsDisabled,
  setShowAddOfficeLocationModal,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  lastVoucherNumbers,
  setPageState,
  setSearchTerm,
  updatePrintSetting,
  onFormDataChange,
  restoreValuesFromStore,
}) => {
  const queryClient = useQueryClient();
  const [rows, setRows] = useState(INITIAL_STATE);
  const [totalDebit, setTotalDebit] = useState(0);
  const [totalCredit, setTotalCredit] = useState(0);
  const [selectedLedger, setSelectedLedger] = useState(null);
  const [addedAttachments, setAddedAttachments] = useState([]);
  const formikRef = useRef();

  // Access the form store
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'suspense-voucher'; // Unique identifier for this form

  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  // Calculate totals whenever rows change
  React.useEffect(() => {
    const debitTotal = Object.values(rows).reduce((sum, row) => {
      return sum + (parseFloat(row.debit) || 0);
    }, 0);
    const creditTotal = Object.values(rows).reduce((sum, row) => {
      return sum + (parseFloat(row.credit) || 0);
    }, 0);
    setTotalDebit(debitTotal);
    setTotalCredit(creditTotal);
  }, [rows]);

  // Restore form data from store for Rate of Exchange flow
  useEffect(() => {
    if (restoreValuesFromStore) {
      const savedFormData = getFormValues(formId);
      if (savedFormData && formikRef.current) {
        formikRef.current.setValues(savedFormData.values || {});
        setAddedAttachments(savedFormData.addedAttachments || []);
        setRows(savedFormData.rows || INITIAL_STATE);
        setIsDisabled(false);
        clearFormValues(formId);
        clearLastVisitedPage(formId);
      }
    }
  }, [restoreValuesFromStore]);

  // Notify parent of form data changes (for saving before navigation)
  useEffect(() => {
    if (onFormDataChange && formikRef.current) {
      onFormDataChange({
        values: formikRef.current.values,
        addedAttachments,
        rows,
      });
    }
  }, [formikRef.current?.values, addedAttachments, rows, onFormDataChange]);

  // Create suspense voucher mutation
  const createSuspenseVoucherMutation = useMutation({
    mutationFn: createSuspenseVoucher,
    onSuccess: (data) => {
      showToast('Suspense Voucher Created!', 'success');
      if (getPrintSettings('suspense_voucher')) {
        window.open(data.detail?.pdf_url, '_blank');
      }
      queryClient.invalidateQueries(['suspenseVoucherListing']);
      handleCancel();
      // Clear saved form values after successful creation
      clearFormValues(formId);
    },
    onError: (error) => {
      console.error('Error creating Suspense Voucher', error);
      if (
        error.message.toLowerCase() ==
        'suspense voucher limit reached for this branch.'
      ) {
        // Show modal for limit reached error
        showToast(
          'Cannot Create: The maximum number of suspense vouchers has been reached. To create new transactions, please increase the transaction number count in the Transaction Number Register.',
          'error'
        );
      } else {
        showErrorToast(error);
      }
    },
  });

  const handleAddRows = () => {
    let count = 5; // Number of rows to add
    const newRows = {};
    Array.from({ length: count }).forEach(() => {
      const id = crypto.randomUUID();
      newRows[id] = {
        id,
        narration: '',
        debit: '',
        credit: '',
        status: 'open',
      };
    });
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;

    // Validate form
    if (
      !formValues.ledger ||
      !formValues.account ||
      !formValues.office ||
      !formValues.currency
    ) {
      showToast('Please fill in all required fields', 'error');
      return;
    }

    // Validate rows - allow rows with only credit or only debit, but require narration
    const validRows = Object.values(rows).filter(
      (row) => row.narration && (row.debit || row.credit)
    );

    if (validRows.length === 0) {
      showToast(
        'Please add at least one row with narration and either debit or credit amount',
        'error'
      );
      return;
    }

    // Transform transactions to match backend expectations
    const transformedTransactions = validRows.reduce((acc, row, index) => {
      acc[`transactions[${index}][narration]`] = row.narration;
      acc[`transactions[${index}][debit]`] = parseFloat(
        row.debit || 0
      ).toString();
      acc[`transactions[${index}][credit]`] = parseFloat(
        row.credit || 0
      ).toString();
      acc[`transactions[${index}][status]`] = row.status === 'open' ? '1' : '0';
      return acc;
    }, {});

    const payload = {
      total_debit: totalDebit.toString(),
      total_credit: totalCredit.toString(),
      ledger: formValues.ledger,
      account_id: formValues.account.toString(),
      office_id: formValues.office.toString(),
      currency_id: formValues.currency.toString(),
      date: date,
      ...transformedTransactions,
      ...addedAttachments,
    };
    createSuspenseVoucherMutation.mutate(payload);
  };

  const handleCancel = () => {
    setRows(generateInitialRows(5));
    setTotalDebit(0);
    setTotalCredit(0);
    setIsDisabled(true);
    setSelectedLedger(null);
    setSelectedFiles([]);
    setAddedAttachments([]);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // Clear saved form values when canceling
    clearFormValues(formId);
  };

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  const handleRemoveFile = (file) => {
    setAddedAttachments((prevFiles) => {
      const updatedFiles = { ...prevFiles };

      for (const key in updatedFiles) {
        if (
          updatedFiles[key]?.name === file.name &&
          updatedFiles[key]?.size === file.size
        ) {
          delete updatedFiles[key];
          break;
        }
      }

      return updatedFiles;
    });
  };

  // Get ledger options
  const getLedgerOptions = () => {
    return [
      { label: 'PL', value: 'party' },
      { label: 'GL', value: 'general' },
      { label: 'WIC', value: 'walkin' },
    ];
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            ledger: '',
            account: '',
            office: '',
            currency: '',
          }}
        >
          {({ values, handleBlur, setFieldValue }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-xxl-9">
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-45">
                      <CombinedInputs
                        label="Ledger"
                        type1="select"
                        type2="select"
                        name1="ledger"
                        name2="account"
                        value1={values.ledger}
                        value2={values.account || newlyCreatedAccount?.id}
                        options1={getLedgerOptions()}
                        options2={getAccountsByTypeOptions(selectedLedger)}
                        isDisabled={isDisabled}
                        handleBlur={handleBlur}
                        placeholder1="Select Ledger"
                        placeholder2="Select Account"
                        className1="ledger"
                        className2="account"
                        onChange1={(selected) => {
                          if (
                            selected.label?.toLowerCase()?.startsWith('add new')
                          ) {
                            setShowAddLedgerModal(
                              selected.label?.toLowerCase()
                            );
                          } else {
                            setFieldValue('ledger', selected.value);
                            setSelectedLedger(selected.value);
                            setFieldValue('account', ''); // Reset account when ledger changes
                          }
                        }}
                        onChange2={(selected) => {
                          if (
                            selected.label?.toLowerCase()?.startsWith('add new')
                          ) {
                            setShowAddLedgerModal(
                              selected.label?.toLowerCase()
                            );
                          } else {
                            setFieldValue('account', selected.value);
                          }
                        }}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'office'}
                        label={'Office'}
                        options={[
                          ...getOfficeLocationOptions(),
                          {
                            label: 'Add New Office',
                            value: null,
                          },
                        ]}
                        isDisabled={isDisabled}
                        placeholder={'Select Office'}
                        value={values.office}
                        onChange={(selected) => {
                          if (
                            selected.label?.toLowerCase()?.startsWith('add new')
                          ) {
                            setShowAddOfficeLocationModal(true);
                          } else {
                            setFieldValue('office', selected.value);
                          }
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'currency'}
                        label={'Currency'}
                        options={currencyOptions}
                        isDisabled={isDisabled}
                        placeholder={'Select Currency'}
                        value={values.currency}
                        onChange={(selected) => {
                          setFieldValue('currency', selected.value);
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                  <div className="row">
                    {/* Right side cards */}
                    <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
                      <AccountBalanceCard />
                    </div>
                  </div>
                </div>
                <CustomTable
                  displayCard={false}
                  headers={suspenseVoucherNewHeaders}
                  isPaginated={false}
                  className={'inputTable'}
                  hideSearch
                  hideItemsPerPage
                >
                  <tbody>
                    {Object.values(rows).map((row, index) => (
                      <SuspenseVoucherRow
                        key={row.id}
                        row={row}
                        index={index}
                        isDisabled={isDisabled}
                        handleDeleteRow={handleDeleteRow}
                        updateField={updateField}
                      />
                    ))}
                  </tbody>
                </CustomTable>

                <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                  <div className="d-flex flex-column gap-2">
                    <CustomCheckbox
                      label="Account Balance"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                      onChange={() => {}}
                    />
                    <CustomCheckbox
                      label="Print"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                      checked={getPrintSettings('suspense_voucher')}
                      onChange={() =>
                        updatePrintSetting(
                          'suspense_voucher',
                          !getPrintSettings('suspense_voucher')
                        )
                      }
                    />
                    <FileDisplayList
                      files={addedAttachments}
                      onRemoveFile={handleRemoveFile}
                    />
                  </div>
                  <div className="d-flex flex-column gap-2 mt-1 debit-credit-inputs">
                    <CustomInput
                      name="totalDebit"
                      label={'Total Debit'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={totalDebit.toFixed(2)}
                      readOnly
                    />
                    <CustomInput
                      name="totalCredit"
                      label={'Total Credit'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={totalCredit.toFixed(2)}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        loading={createSuspenseVoucherMutation.isPending}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachments}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default NewSuspenseVoucher;
