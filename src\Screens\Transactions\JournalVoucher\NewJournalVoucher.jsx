import React, { useCallback, useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { journalVoucherHeaders } from '../../../Utils/Constants/TableHeaders';
import JournalVoucherRow from './JournalVoucherRow';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createJournalVoucher,
  getJournalVoucherListing,
} from '../../../Services/Transaction/JournalVoucher';
import {
  formatFileSize,
  getIcon,
  isNullOrEmpty,
  showErrorToast,
} from '../../../Utils/Utils';
import { showToast } from '../../../Components/Toast/Toast';
import withModal from '../../../HOC/withModal';
import { FaXmark } from 'react-icons/fa6';
import Styles from '../Attachments.module.css';
import useFormStore from '../../../Stores/FormStore';
import useSettingsStore from '../../../Stores/SettingsStore';

const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      ledger: '',
      account_id: '',
      narration: '',
      currency_id: '',
      fc_amount: '',
      rate: '1.**********',
      lc_amount: '',
      sign: 'Debit',
      error: false,
    };
  });
  return rows;
};

const INITIAL_STATE = generateInitialRows(4);

const NewJournalVoucher = ({
  date,
  setDate,
  showModal,
  getAccountsByTypeOptions,
  currencyOptions,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  setAddLedgerRowId,
  setPageState,
  cloneJV,
  setCloneJV,
  setWriteCloneTerm,
  setSearchTerm,
  isDisabled,
  setIsDisabled,
  lastVoucherNumbers,
  onFormDataChange,
  restoreValuesFromStore,
  updatePrintSetting,
}) => {
  const [rows, setRows] = useState(INITIAL_STATE);
  const queryClient = useQueryClient();
  const { getFormValues, clearFormValues, clearLastVisitedPage } =
    useFormStore();
  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachments, setAddedAttachments] = useState([]);
  const [showSubmitError, setShowSubmitError] = useState(false);

  const [totalDebit, setTotalDebit] = useState(0);
  const [totalCredit, setTotalCredit] = useState(0);
  const [showError, setShowError] = useState(false);

  // Table Row Actions
  const handleAddRows = () => {
    let count = 6; // Number of rows to add
    const newRows = {};
    Array.from({ length: count }).forEach(() => {
      const id = crypto.randomUUID();
      newRows[id] = {
        id,
        ledger: '',
        account_id: '',
        narration: '',
        currency_id: '',
        fc_amount: '',
        rate: '1.**********',
        lc_amount: '',
        sign: 'Debit',
        error: false,
      };
    });
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };
  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };
  const handleResetRows = () => {
    setRows(generateInitialRows(4));
    setTotalDebit(0);
    setTotalCredit(0);
    setSearchTerm('');
    setWriteCloneTerm('');
    setCloneJV('');
    setIsDisabled(true);
    setAddedAttachments([]);
  };

  const {
    data: { data: [journalVoucherData] = [] } = {}, // [journalVoucherData] = destructuring array first item
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['journalVoucher', cloneJV],
    queryFn: () => getJournalVoucherListing({ search: cloneJV }),
    enabled: !!cloneJV,
  });

  const createJournalVoucherMutation = useMutation({
    mutationFn: createJournalVoucher,
    onSuccess: (data) => {
      showToast('Journal Voucher Created!', 'success');
      if (getPrintSettings('journal_voucher')) {
        window.open(data.detail?.pdf_url, '_blank');
      }
      queryClient.invalidateQueries(['journalVoucherListing']);
      handleResetRows();
      setDate(new Date().toISOString().split('T')[0]);
    },
    onError: (error) => {
      console.error('Error creating Journal Voucher', error);
      if (
        error.message.toLowerCase() ==
        'journal voucher limit reached for this branch.'
      ) {
        showModal(
          'Cannot Create',
          'The maximum number of journal vouchers has been reached. To create new transactions, please increase the transaction number count in the Transaction Number Register.',
          null,
          'error'
        );
      } else {
        showErrorToast(error);
      }
    },
  });

  useEffect(() => {
    if (!isNullOrEmpty(journalVoucherData?.journal_vouchers)) {
      setRows(() => {
        const editRows = {};
        let totalDebit = 0;
        let totalCredit = 0;

        journalVoucherData?.journal_vouchers.forEach((x) => {
          const amount = parseFloat(x.lc_amount) || 0;
          if (x.sign === 'Debit') {
            totalDebit += amount;
          } else if (x.sign === 'Credit') {
            totalCredit += amount;
          }

          editRows[x.id] = {
            id: x.id,
            ledger: x.ledger,
            account_id: x.account_id,
            narration: x.narration,
            currency_id: x.currency_id,
            fc_amount: x.fc_amount,
            rate: x.rate,
            lc_amount: x.lc_amount,
            sign: x.sign,
          };
        });

        setTotalDebit(totalDebit);
        setTotalCredit(totalCredit);

        return { ...editRows };
      });
    }
  }, [journalVoucherData]);

  // Restore form data if returning from Rate of Exchange page
  useEffect(() => {
    if (restoreValuesFromStore) {
      const savedFormData = getFormValues('journalVoucher');
      setRows(savedFormData.rows);
      setTotalDebit(savedFormData.totalDebit);
      setTotalCredit(savedFormData.totalCredit);
      setAddedAttachments(savedFormData.addedAttachments);
      // Clear the saved data after restoring
      clearFormValues('journalVoucher');
      clearLastVisitedPage('journalVoucher');
    }
  }, [restoreValuesFromStore]);

  // Notify parent of form data changes
  useEffect(() => {
    onFormDataChange?.({
      rows,
      totalDebit,
      totalCredit,
      addedAttachments,
    });
  }, [rows, totalDebit, totalCredit, addedAttachments, onFormDataChange]);

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setShowSubmitError(false);
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      // Calculate total debit whenever lc_amount or sign changes
      if (field === 'lc_amount' || field === 'sign') {
        const totalDebit = Object.values(newRows).reduce((sum, row) => {
          const amount = parseFloat(row.lc_amount) || 0;
          return row.sign === 'Debit' ? sum + amount : sum;
        }, 0);
        setTotalDebit(totalDebit);
        const totalCredit = Object.values(newRows).reduce((sum, row) => {
          const amount = parseFloat(row.lc_amount) || 0;
          return row.sign === 'Credit' ? sum + amount : sum;
        }, 0);
        setTotalCredit(totalCredit);
      }
      return newRows;
    });
  }, []);

  // Expose updateField globally for modal callback
  React.useEffect(() => {
    window.updateJVRowField = updateField;
    return () => {
      delete window.updateJVRowField;
    };
  }, [updateField]);

  const handleSubmit = () => {
    if (totalDebit - totalCredit !== 0) {
      setShowError(true);
      return;
    }
    // Check for any rows with error=true
    const hasErrors = Object.values(rows).some((row) => row.error === true);
    if (hasErrors) {
      showErrorToast({ message: 'Rate is out of allowed variation' });
      return;
    }

    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    // All inputs must be filled except narration
    let transactions = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([key, v]) => {
          // Skip checking narration field
          if (key === 'narration') return true;
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    if (isNullOrEmpty(transactions)) {
      setShowSubmitError(true);
      return;
    }

    transactions = Object.values(transactions).map(({ id, ...rest }) => rest);

    const transformedTransactions = transactions?.reduce((acc, t, index) => {
      Object.entries(t).forEach(([key, value]) => {
        acc[`transactions[${index}][${key}]`] = value;
      });
      return acc;
    }, {});

    payload = {
      total_debit: totalDebit,
      total_credit: totalCredit,
      ...transformedTransactions,
      ...addedAttachments,
      date,
    };

    createJournalVoucherMutation.mutate(payload);
  };
  const handleRemoveFile = (file) => {
    setAddedAttachments((prevFiles) => {
      const updatedFiles = { ...prevFiles };

      for (const key in updatedFiles) {
        if (
          updatedFiles[key]?.name === file.name &&
          updatedFiles[key]?.size === file.size
        ) {
          delete updatedFiles[key];
          break;
        }
      }

      return updatedFiles;
    });
  };

  if (isError) {
    console.log(error);
  }

  useEffect(() => {
    if (!cloneJV) {
      handleResetRows();
    }
  }, [cloneJV]);

  return (
    <>
      <Row>
        <Col>
          <CustomTable
            hasFilters={false}
            headers={journalVoucherHeaders}
            isPaginated={false}
            className={'inputTable'}
            isLoading={isLoading}
            hideSearch
            hideItemsPerPage
          >
            <tbody>
              {isError ? (
                <tr>
                  <td colSpan={journalVoucherHeaders.length}>
                    <p className="text-danger mb-0">
                      Unable to fetch data at this time
                    </p>
                  </td>
                </tr>
              ) : isNullOrEmpty(journalVoucherData?.journal_vouchers) &&
                !!cloneJV ? (
                <tr>
                  <td colSpan={journalVoucherHeaders.length}>
                    <p className="text-danger mb-0">
                      No Journal Voucher found for ID {cloneJV}
                    </p>
                  </td>
                </tr>
              ) : (
                Object.values(rows).map((row, index) => (
                  <JournalVoucherRow
                    date={date}
                    key={row.id}
                    row={row}
                    length={Object.values(rows).length}
                    index={index}
                    isDisabled={isDisabled}
                    getAccountsByTypeOptions={getAccountsByTypeOptions}
                    currencyOptions={currencyOptions}
                    handleDeleteRow={handleDeleteRow}
                    updateField={updateField}
                    setShowMissingCurrencyRateModal={
                      setShowMissingCurrencyRateModal
                    }
                    newlyCreatedAccount={newlyCreatedAccount}
                    setShowAddLedgerModal={(modalType) => {
                      setShowAddLedgerModal(modalType);
                      setAddLedgerRowId(row.id);
                    }}
                    setCurrencyToSelect={setCurrencyToSelect}
                  />
                ))
              )}
            </tbody>
          </CustomTable>
          {showSubmitError && !isDisabled && (
            <p className="text-danger">
              Please fill all fields in a row to save
            </p>
          )}
          <div className="d-flex justify-content-between gap-3 mt-45 mb-5">
            <div className="d-flex flex-column gap-2">
              <CustomCheckbox
                readOnly={isDisabled}
                checked={getPrintSettings('journal_voucher')}
                style={{ border: 'none', userSelect: 'none' }}
                onChange={(e) => {
                  updatePrintSetting('journal_voucher', e.target.checked);
                }}
                label={'Print'}
              />
              {Object.values(addedAttachments)?.map((file, index) => (
                <div key={index} style={{ position: 'relative' }}>
                  {console.log(file)}
                  <div className={Styles.uploadedFiles}>
                    <div className={Styles.nameIconWrapper}>
                      <div className="beechMein" style={{ minWidth: 28 }}>
                        {getIcon(file.type)}
                      </div>
                      <div
                        style={{ width: 126 }}
                        className="d-flex flex-column flex-1"
                      >
                        <p className={Styles.fileName}>{file.name}</p>
                        <p className={Styles.size}>
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      className={Styles.fileRemoveButton}
                      onClick={() => {
                        handleRemoveFile(file);
                      }}
                    >
                      <FaXmark size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
            <div className="d-flex flex-column gap-2 mt-1">
              <CustomInput
                name="totalDebit"
                label={'Total Debit'}
                labelClass={'fw-medium'}
                type="number"
                showBorders={isDisabled}
                disabled={isDisabled}
                error={false}
                borderRadius={10}
                value={totalDebit.toFixed(2)}
                readOnly
              />
              <CustomInput
                name="totalCredit"
                label={'Total Credit'}
                labelClass={'fw-medium'}
                type="number"
                showBorders={isDisabled}
                disabled={isDisabled}
                readOnly
                error={false}
                borderRadius={10}
                value={totalCredit.toFixed(2)}
              />
              <CustomInput
                name="difference"
                label={'Difference'}
                labelClass={'fw-medium'}
                type="number"
                showBorders={isDisabled}
                disabled={isDisabled}
                readOnly
                error={false}
                borderRadius={10}
                value={(totalDebit - totalCredit).toFixed(2)}
                onChange={() => {
                  totalDebit - totalCredit !== 0 && setShowError(false);
                }}
              />
              {totalDebit - totalCredit !== 0 && showError && (
                <p className="text-danger">Difference must be 0</p>
              )}
            </div>
          </div>
        </Col>
      </Row>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleResetRows,
            variant: 'secondaryButton',
          },
        ]}
        loading={createJournalVoucherMutation.isPending}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />

      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachments}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default withModal(NewJournalVoucher);
