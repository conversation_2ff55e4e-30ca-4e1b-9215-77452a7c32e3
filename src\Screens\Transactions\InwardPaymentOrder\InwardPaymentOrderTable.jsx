import React, { useState } from 'react';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { inwardPaymentOrderTableHeaders } from '../../../Utils/Constants/TableHeaders';
import { MOCK_INWARD_PAYMENT_ORDER_DATA } from '../../../Mocks/MockData';

const InwardPaymentOrderTable = ({ setPageState }) => {
  const [tableData, setTableData] = useState(MOCK_INWARD_PAYMENT_ORDER_DATA);

  return (
    <CustomTable
      headers={inwardPaymentOrderTableHeaders}
      isPaginated={false}
      hideSearch
      hideItemsPerPage
    >
      <tbody>
        {tableData?.map((x, i) => (
          <tr key={i}>
            <td>{x.date}</td>
            <td
              onClick={() => {
                setPageState('view');
              }}
            >
              <p className="text-link text-decoration-underline cp mb-0">
                {x.dbv_no}
              </p>
            </td>
            <td>{x.debit_ledger}</td>
            <td>{x.debit_account}</td>
            <td>{x.reference_no}</td>
            <td>{x.pay_type}</td>
            <td>{x.pay_date}</td>
            <td>{x.beneficiary}</td>
            <td>{x.sender}</td>
            <td>{x.fcy}</td>
            <td>{x.fc_amount}</td>
            <td>{x.commission}</td>
            <td>{x.vat}</td>
            <td>{x.net_total}</td>
            <td>{x.user_id}</td>
            <td>{x.time}</td>
            <td>{x.attachment}</td>
          </tr>
        ))}
      </tbody>
    </CustomTable>
  );
};

export default InwardPaymentOrderTable;
