import React, { useEffect, useState } from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import CustomInput from '../../../Components/CustomInput';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import { serialNum } from '../../../Utils/Utils';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';

const InternalPaymentVoucherRow = ({
  row,
  index,
  updateField,
  isDisabled,
  handleDeleteRow,
  accountData,
  setShowAddLedgerModal,
  currencyOptions,
  vatData,
  currency,
}) => {
  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [outOfScope, setOutOfScope] = useState('');
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);

  console.log(currencyOptions);
  console.log('currency', currency);
  const getAccountsByTypeOptions = (accountType) => {
    if (!accountType) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
      accountData[accountType] || {};

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Accounts', errorMessage);
      return [{ label: 'Unable to fetch Accounts', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];
    switch (accountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: null,
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: null,
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: null,
        });
        break;
      default:
        break;
    }
    return options;
  };
  // --- //

  console.log('row data', row);
  console.log('vat data', vatData);
  // const getVATTermsOptions = () => {
  //   if (vatData.isLoadingVatType)
  //     return [
  //       {
  //         label: 'Loading...',
  //         value: '',
  //       },
  //     ];
  //   if (vatData.isErrorVatType) {
  //     console.error('Unable to fetch VAT Terms', vatData.errorMessage);
  //     return [{ label: 'Unable to fetch VAT Terms', value: null }];
  //   }

  //   const vatType = vatData?.vatType;

  //   if (vatType?.vat_type === 'fixed') {
  //     return [
  //       {
  //         label: `Fixed VAT - ${vatType.vat_percentage}%`,
  //         value: parseFloat(vatType.vat_percentage),
  //       },
  //     ];
  //   }
  
  //   return vatType?.vats?.map((item) => ({
  //     label: `${item.title}${
  //       !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
  //     }`,
  //     value: parseFloat(item.percentage),
  //   })) || [];

  
  //   // return vatData?.vatType?.vats?.map((item) => ({
  //   //   label: `${item.title}${
  //   //     !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
  //   //   }`,
  //   //   value: item.percentage,
  //   // }));

    
  // };

  const getVATTermsOptions = () => {
    if (vatData.isLoadingVatType) {
      return {
        options: [{ label: 'Loading...', value: '' }],
        isFixed: false,
        fixedValue: null,
      };
    }
  
    if (vatData.isErrorVatType) {
      console.error('Unable to fetch VAT Terms', vatData.errorMessage);
      return {
        options: [{ label: 'Unable to fetch VAT Terms', value: null }],
        isFixed: false,
        fixedValue: null,
      };
    }
  
    const vatType = vatData?.vatType;
  
    if (vatType?.vat_type === 'fixed') {
      return {
        options: [
          {
            label: `Fixed VAT - ${vatType.vat_percentage}%`,
            value: parseFloat(vatType.vat_percentage),
          },
        ],
        isFixed: true,
        fixedValue: parseFloat(vatType.vat_percentage),
      };
    }
  
    return {
      options:
        vatType?.vats?.map((item) => ({
          label: `${item.title}${
            !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
          }`,
          value: parseFloat(item.percentage),
        })) || [],
      isFixed: false,
      fixedValue: null,
    };
  };


  const { options: vatOptions, isFixed, fixedValue } = getVATTermsOptions();
  
  useEffect(() => {
    console.log('isFixed',isFixed)
    console.log('fixedValue',fixedValue)
    if (isFixed && fixedValue && !row.vat_terms) {
      updateField(row.id, 'vat_terms', fixedValue);
  
      const vat_percentage = fixedValue / 100;
      updateField(row.id, 'vat_percentage', vat_percentage);
  
      const amount = parseFloat(row.amount || 0);
      const vat_amount = amount * vat_percentage;
      updateField(row.id, 'vat_amount', vat_amount);
  
      const validVatAmount = isNaN(vat_amount) ? 0 : vat_amount;
      const net_total = amount + validVatAmount;
      updateField(row.id, 'total', net_total);
    }
  }, [isFixed, fixedValue, row.amount]);


  console.log('vatOptions', vatOptions);

  const handleVatOutOfScope = (row) => {
    setOutOfScope(row.out_of_scope);
    setShowVatOutOfScopeModal(false);
  };

  return (
    <tr>
      <td>{serialNum(index + 1)}</td>
      <td>
        <SearchableSelect
          isDisabled={isDisabled}
          options={[
            { label: 'PL', value: 'party' },
            { label: 'GL', value: 'general' },
            { label: 'WIC', value: 'walkin' },
          ]}
          placeholder="Ledger"
          value={row.ledger}
          onChange={(selected) => {
            updateField(row.id, 'ledger', selected.value);
          }}
          borderRadius={10}
        />
      </td>
      <td>
        <SearchableSelect
          isDisabled={isDisabled}
          options={getAccountsByTypeOptions(row.ledger)}
          placeholder="Account"
          value={row.debit_account_id}
          onChange={(selected) => {
            if (selected.label?.toLowerCase()?.startsWith('add new')) {
              setShowAddLedgerModal(selected.label?.toLowerCase());
            } else {
              updateField(row.id, 'debit_account_id', selected.value);
            }
          }}
          borderRadius={10}
          minWidth={240}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.narration}
          disabled={isDisabled}
          placeholder="Enter Narration"
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
          style={{ minWidth: 300 }}
        />
      </td>
      <td>
        <SearchableSelect
          name={'currency_id'}
          options={currencyOptions}
          placeholder=""
          value={row.currency_id}
          onChange={(selected) => {
            updateField(row.id, 'currency_id', selected.value);
          }}
          borderRadius={10}
          minWidth={100}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          disabled={isDisabled}
          value={row.amount}
          placeholder="Amount"
          onChange={(e) => {
            updateField(row.id, 'amount', parseFloat(e.target.value));

            let vat_percentage = parseFloat(row.vat_percentage || 0);

            let amount = parseFloat(e.target.value || 0);
            let vat_amount = amount * vat_percentage;

            let validVatAmount = isNaN(vat_amount) ? 0 : vat_amount;

            updateField(row.id, 'vat_amount', validVatAmount);

            let net_total = amount + validVatAmount;
            console.log('v', amount);
            console.log('va', validVatAmount);
            updateField(row.id, 'total', net_total ?? '');
          }}
          borderRadius={10}
          style={{ maxWidth: 135 }}
        />
      </td>

      <td>
          <SearchableSelect
            name="vat_terms"
            options={vatOptions}
            isDisabled={isDisabled}
            placeholder="Select VAT %"
            value={parseInt(row.vat_terms)}
            onChange={(selected) => {
              updateField(row.id, 'vat_terms', selected?.value ?? '');

              let vat_percentage = selected?.value / 100;

              updateField(row.id, 'vat_percentage', vat_percentage ?? '');

              let amount = parseFloat(row.amount || 0);
              let vat_amount = amount * vat_percentage;

              updateField(row.id, 'vat_amount', vat_amount ?? '');

              let validVatAmount = isNaN(vat_amount) ? 0 : vat_amount;
              let net_total = amount + validVatAmount;
              updateField(row.id, 'total', net_total ?? '');
            }}
          />
        
      </td>

      <td>
        <CustomInput
          type={'number'}
          value={row.vat_percentage}
          disabled={true}
          placeholder="Enter VAT %"
          onChange={(e) =>
            updateField(row.id, 'vat_percentage', e.target.value)
          }
          borderRadius={10}
          style={{ maxWidth: 135 }}
        />
      </td>

      <td>
        <CustomInput
          name={'vat_amount'}
          type={'text'}
          disabled={true}
          placeholder={'Enter VAT Amount'}
          value={row.vat_amount}
          // onChange={(e) => updateField(row.id, 'vat_amount', e.target.value)}
        />
      </td>

      <td>
        <CustomInput
          type={'text'}
          value={row.total}
          disabled={true}
          placeholder="Net Total"
          onChange={(e) => updateField(row.id, 'total', e.target.value)}
          borderRadius={10}
          style={{ maxWidth: 135 }}
          readOnly
        />
      </td>
      <td>
        <TableActionDropDown
          actions={[
            {
              name: 'Delete',
              icon: HiOutlineTrash,
              onClick: () => handleDeleteRow(row.id),
              className: 'delete',
            },
          ]}
        />
      </td>
    </tr>
  );
};

export default InternalPaymentVoucherRow;
