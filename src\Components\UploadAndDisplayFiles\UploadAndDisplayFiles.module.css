.fileUploader<PERSON>abel {
  font-size: 16px;
  margin-bottom: 6px;
  padding: 0 0 0 6px;
}

.fileUploadArea {
  width: 100%;
  height: 160px;
  border-radius: 10px;
  border: 1px solid rgba(100, 100, 100, 0.22);
  /* box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1); */
  background: var(--input-bg-color);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-block: 2.5rem;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.fileUploadArea p {
  text-align: center;
  margin: 0;
}

.fileUploadArea:hover p {
  text-decoration: underline;
}
.fileRemoveButtonLink,
.fileRemoveButton {
  width: 23px;
  height: 23px;
  z-index: 1;
  border-radius: 340px;
  color: black;
  position: absolute;
  top: -10px;
  right: -28px;
  border-width: 1px;
  background-color: white;
  outline: none !important;
  box-shadow: none !important;
}
.fileRemoveButton {
  position: relative;
  top: unset;
  right: unset;
}

.displayFiles * {
  /* border: 1px solid salmon; */
  /* width: fit-content; */
}
.uploadedFiles {
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  max-width: 200px;
  border: 1px solid rgba(100, 100, 100, 0.22);
}
.nameIconWrapper {
  overflow: hidden;
  display: flex;
  flex: 1;
  gap: 4px;
  width: 150px;
}
.fileName {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-block: auto;
}
.size {
  margin: 0;
  font-size: 12px;
  font-weight: 400;
}
@media screen and (max-width: 991px) {
  .fileUploaderLabel {
    font-size: 14px;
  }
}

@media screen and (max-width: 767px) {
  .fileUploaderLabel {
    font-size: 13px;
  }
}
