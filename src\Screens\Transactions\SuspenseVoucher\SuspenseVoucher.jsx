import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Form, Formik } from 'formik';
import React, { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { FaMagnifyingGlass } from 'react-icons/fa6';
import BackButton from '../../../Components/BackButton';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import { showToast } from '../../../Components/Toast/Toast';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { addOfficeLocationMaster, editOfficeLocationMaster } from '../../../Services/Masters/OfficeLocationMaster';
import {
  getAccountsbyType,
  getOfficeLocations,
  // addOfficeLocation,
  getSuspenseVoucherListing,
  getSVVoucherNumber
} from '../../../Services/Transaction/SuspenseVoucher';
import useFormStore from '../../../Stores/FormStore';
import useSettingsStore from '../../../Stores/SettingsStore';
import { getCurrencyOptions, showErrorToast } from '../../../Utils/Utils';
import { addOfficeLocationValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import '../transactionStyles.css';
import EditSuspenseVoucher from './EditSuspenseVoucher';
import NewSuspenseVoucher from './NewSuspenseVoucher';
import SuspenseVouchersTable from './SuspenseVouchersTable';
import ViewSuspenseVoucher from './ViewSuspenseVoucher';
import { PulseLoader } from 'react-spinners';

const SuspenseVoucher = () => {
  usePageTitle('Suspense Voucher');
  const currencyOptions = getCurrencyOptions();
  const { updatePrintSetting } = useSettingsStore();
  const queryClient = useQueryClient();

  // [new, view, edit, listing]
  // View is for specific Suspense Voucher search and view it's detail
  // Edit is for editing the specific Suspense Voucher's detail
  // Listing is for Suspense Voucher listing table
  const [pageState, setPageState] = useState('new');

  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [showAddOfficeLocationModal, setShowAddOfficeLocationModal] =
    useState(false);
  // Upload Only Modal
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  // Upload And View Modal
  const [attachmentsModal, setAttachmentsModal] = useState(false);
  // Selected files from UploadAttachments Modal
  const [selectedFiles, setSelectedFiles] = useState(null);

  // Last voucher numbers state
  const [lastVoucherNumbers, setLastVoucherNumbers] = useState({
    heading: 'Last SV Number: ',
    current: '',
    previous: '',
    next: '',
    isLoadingVoucherNumber: false,
    isErrorVoucherNumber: false,
    errorVoucherNumber: null,
  });

  // Query for voucher number
  const {
    data: voucherNumber,
    isLoading: isLoadingVoucherNumber,
    isError: isErrorVoucherNumber,
    error: errorVoucherNumber,
  } = useQuery({
    queryKey: ['suspenseVoucherNumber', searchTerm],
    queryFn: () => getSVVoucherNumber(searchTerm),
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Update last voucher numbers
  useEffect(() => {
    setLastVoucherNumbers({
      heading: 'Last SV Number: ',
      last: voucherNumber?.default_voucher_no,
      current: voucherNumber?.current_voucher_no,
      previous: voucherNumber?.previous_voucher_no,
      next: voucherNumber?.next_voucher_no,
      isLoadingVoucherNumber: isLoadingVoucherNumber,
      isErrorVoucherNumber: isErrorVoucherNumber,
      errorVoucherNumber: errorVoucherNumber,
    });
  }, [
    voucherNumber,
    isLoadingVoucherNumber,
    isErrorVoucherNumber,
    errorVoucherNumber,
  ]);

  // Queries for accounts by type
  const {
    data: partyAccounts,
    isLoading: isLoadingPartyAccounts,
    isError: isErrorPartyAccounts,
    error: errorPartyAccounts,
  } = useQuery({
    queryKey: ['suspenseVoucherAccounts', 'party'],
    queryFn: () => getAccountsbyType('party'),
    refetchOnWindowFocus: false,
    retry: 1,
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: walkinAccounts,
    isLoading: isLoadingWalkinAccounts,
    isError: isErrorWalkinAccounts,
    error: errorWalkinAccounts,
  } = useQuery({
    queryKey: ['suspenseVoucherAccounts', 'walkin'],
    queryFn: () => getAccountsbyType('walkin'),
    refetchOnWindowFocus: false,
    retry: 1,
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: generalAccounts,
    isLoading: isLoadingGeneralAccounts,
    isError: isErrorGeneralAccounts,
    error: errorGeneralAccounts,
  } = useQuery({
    queryKey: ['suspenseVoucherAccounts', 'general'],
    queryFn: () => getAccountsbyType('general'),
    refetchOnWindowFocus: false,
    retry: 1,
    staleTime: 1000 * 60 * 5,
  });

  // Query for office locations
  const {
    data: officeLocations,
    isLoading: isLoadingOfficeLocations,
    isError: isErrorOfficeLocations,
    error: errorOfficeLocations,
  } = useQuery({
    queryKey: ['suspenseVoucherOfficeLocations'],
    queryFn: getOfficeLocations,
    refetchOnWindowFocus: false,
    queryInvalidateOnMount: true,
    retry: 1,
    staleTime: 1000 * 60 * 5,
  });

  // Query for suspense voucher data (for edit mode)
  const {
    data: { data: [suspenseVoucherData] = [] } = {},
    isLoading: isLoadingSuspenseVoucher,
    isError: isErrorSuspenseVoucher,
    error: errorSuspenseVoucher,
  } = useQuery({
    queryKey: ['suspenseVoucher', searchTerm],
    queryFn: () => getSuspenseVoucherListing({ search: searchTerm }),
    staleTime: 1000 * 60 * 5,
    enabled: pageState === 'edit' && !!searchTerm,
  });

  // Add Office Location Mutation
  const addOfficeLocationMutation = useMutation({
    mutationFn: addOfficeLocationMaster,
    onSuccess: () => {
      setShowAddOfficeLocationModal(false);
      showToast('New Office Location Added', 'success');
      queryClient.invalidateQueries(['suspenseVoucherOfficeLocations']);
    },
    onError: (error) => {
      setShowAddOfficeLocationModal(false);
      showErrorToast(error);
    },
  });

  const handleAddOfficeLocation = (values) => {
    addOfficeLocationMutation.mutate(values);
  };


  const suspenseVoucher = suspenseVoucherData?.suspense_vouchers;

  // Organize accounts data
  const accountsData = {
    party: {
      data: partyAccounts,
      loading: isLoadingPartyAccounts,
      error: isErrorPartyAccounts,
      errorMessage: errorPartyAccounts,
    },
    walkin: {
      data: walkinAccounts,
      loading: isLoadingWalkinAccounts,
      error: isErrorWalkinAccounts,
      errorMessage: errorWalkinAccounts,
    },
    general: {
      data: generalAccounts,
      loading: isLoadingGeneralAccounts,
      error: isErrorGeneralAccounts,
      errorMessage: errorGeneralAccounts,
    },
  };

  // Helper function to get accounts by type options
  const getAccountsByTypeOptions = (AccountType) => {
    if (!AccountType) {
      return [{ label: 'Select Account', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
      accountsData[AccountType] || {};

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Accounts', errorMessage);
      return [{ label: 'Unable to fetch Accounts', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id?.toString(),
        label: x?.title || x?.account_name,
      })) || [];

    switch (AccountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: null,
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: null,
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: null,
        });
        break;
      default:
        break;
    }

    return options;
  };

  // Helper function to get office location options
  const getOfficeLocationOptions = () => {
    if (isLoadingOfficeLocations) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (isErrorOfficeLocations) {
      console.error('Unable to fetch Office Locations', errorOfficeLocations);
      return [{ label: 'Unable to fetch Office Locations', value: null }];
    }

    let options =
      officeLocations?.map((x) => ({
        value: x?.id?.toString(),
        label: x?.office_location,
      })) || [];

    return options;
  };

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  const renderPageContent = () => {
    const pageComponents = {
      new: (
        <NewSuspenseVoucher
          date={date}
          currencyOptions={currencyOptions}
          getAccountsByTypeOptions={getAccountsByTypeOptions}
          getOfficeLocationOptions={getOfficeLocationOptions}
          isDisabled={isDisabled}
          setIsDisabled={setIsDisabled}
          setShowAddOfficeLocationModal={setShowAddOfficeLocationModal}
          setShowAddLedgerModal={setShowAddLedgerModal}
          newlyCreatedAccount={newlyCreatedAccount}
          uploadAttachmentsModal={uploadAttachmentsModal}
          setUploadAttachmentsModal={setUploadAttachmentsModal}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          lastVoucherNumbers={lastVoucherNumbers}
          setPageState={setPageState}
          setSearchTerm={setSearchTerm} 
          updatePrintSetting={updatePrintSetting}
          onFormDataChange={(formData) => {
            // Save form data to store for potential restoration
            const { saveFormValues } = useFormStore.getState();
            saveFormValues('suspense-voucher', formData);
          }}
          restoreValuesFromStore={false}
        />
      ),
      view: (
        <ViewSuspenseVoucher
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          setPageState={setPageState}
          attachmentsModal={attachmentsModal}
          setAttachmentsModal={setAttachmentsModal}
          lastVoucherNumbers={lastVoucherNumbers}
   
          />
      ),
      listing: (
        <SuspenseVouchersTable 
          date={date}
          setPageState={setPageState} 
          setSearchTerm={setSearchTerm}
        />
      ),
      edit: (
        <EditSuspenseVoucher
          date={date}
          currencyOptions={currencyOptions}
          getAccountsByTypeOptions={getAccountsByTypeOptions}
          getOfficeLocationOptions={getOfficeLocationOptions}
          setPageState={setPageState}
          setShowAddOfficeLocationModal={setShowAddOfficeLocationModal}
          setShowAddLedgerModal={setShowAddLedgerModal}
          newlyCreatedAccount={newlyCreatedAccount}
          setAddLedgerRowId={() => {}}
          lastVoucherNumbers={lastVoucherNumbers}
          setSearchTerm={setSearchTerm}
          searchTerm={searchTerm}
          isDisabled={false}
          setIsDisabled={setIsDisabled}
          updatePrintSetting={updatePrintSetting}
        />
      ),
    };

    return pageComponents[pageState] || null;
  };

  return (
    <>
      <section className="position-relative">
        <div
          style={{ height: 43 }}
          className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-4"
        >
          <div>
            {(pageState == 'listing' || pageState == 'view') && (
              <BackButton
                handleBack={() => {
                  setPageState('new');
                }}
              />
            )}
            <h2 className="screen-title mb-0">Suspense Voucher</h2>
          </div>
          {pageState == 'new' && isDisabled && (
            <div className="d-flex gap-2">
              <CustomButton text={'New'} onClick={() => setIsDisabled(false)} />
            </div>
          )}
        </div>
        <Row>
          <Col xs={12}>
            <div className="d-flex align-items-start justify-content-between flex-wrap-reverse mb-3">
              <div className="d-flex align-items-end mt-3">
                <CustomInput
                  style={{ width: '280px' }}
                  type="text"
                  placeholder="Search"
                  error={false}
                  showBorders={false}
                  borderRadius={10}
                  name="search"
                  rightIcon={FaMagnifyingGlass}
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                  }}
                  onButtonClick={() => {
                    if (searchTerm === '') {
                      setPageState('listing');
                    } else {
                      setPageState('view');
                    }
                    console.log('search for:', searchTerm);
                  }}
                />
              </div>
              <div>
                <CustomInput
                  name="Date"
                  label={'Date'}
                  type="date"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={date}
                  onChange={(e) => {
                    setDate(e.target.value);
                  }}
                />
              </div>
            </div>
            {renderPageContent()}
          </Col>
        </Row>
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>

      {/* Add Office Location Modal  */}
      <CustomModal
        show={showAddOfficeLocationModal}
        close={() => setShowAddOfficeLocationModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">New Office Location</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ office_location: '' }}
            validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handleAddOfficeLocation}
            
            // onSubmit={() => {
            //   setShowAddOfficeLocationModal(false);
            // }}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'office_location'}
                    type={'text'}
                    required
                    label={'Office Location'}
                    placeholder={'Enter Office Location'}
                    value={values.office_location}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.office_location && errors.office_location}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                {!addOfficeLocationMutation.isPending ? (
                  <>
                    <CustomButton type="submit" text={'Save'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowAddOfficeLocationModal(false)}
                    />
                  </>
                  ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} 
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default SuspenseVoucher;
