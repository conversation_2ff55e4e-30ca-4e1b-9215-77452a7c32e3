import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useCallback, useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  addJournalVoucherAttachment,
  deleteJournalVoucherAttachment,
  getJournalVoucherListing,
  updateJournalVoucher,
} from '../../../Services/Transaction/JournalVoucher';
import { journalVoucherHeaders } from '../../../Utils/Constants/TableHeaders';
import JournalVoucherRow from './JournalVoucherRow';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils';
import { showToast } from '../../../Components/Toast/Toast';
import useSettingsStore from '../../../Stores/SettingsStore';

const EditJournalVoucher = ({
  date,
  getAccountsByTypeOptions,
  currencyOptions,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  setAddLedgerRowId,
  setPageState,
  searchTerm,
  setSearchTerm,
  isDisabled,
  setIsDisabled,
  lastVoucherNumbers,
  updatePrintSetting,
}) => {
  const [rows, setRows] = useState({});
  const queryClient = useQueryClient();

  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [totalDebit, setTotalDebit] = useState(0);
  const [totalCredit, setTotalCredit] = useState(0);
  const [showError, setShowError] = useState(false);

  // Table Row Actions
  const handleAddRows = () => {
    let count = 6; // Number of rows to add
    const newRows = {};
    Array.from({ length: count }).forEach(() => {
      const id = crypto.randomUUID();
      newRows[id] = {
        id,
        ledger: '',
        account_id: '',
        narration: '',
        currency_id: '',
        fc_amount: '',
        rate: '1.**********',
        lc_amount: '',
        sign: 'debit',
      };
    });
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };
  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };
  const handleResetRows = () => {
    // setRows(generateInitialRows(4));
    setIsDisabled(true);
    setPageState('view');
  };

  const updateJournalVoucherMutation = useMutation({
    mutationFn: ({ id, formData }) => updateJournalVoucher(id, formData),
    onSuccess: (data) => {
      showToast('Journal Voucher Updated!', 'success');
      if (getPrintSettings('journal_voucher')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      queryClient.invalidateQueries(['journalVoucher', searchTerm]);
      handleResetRows();
    },
    onError: (error) => {
      console.error('Error updating Journal Voucher', error);
      showErrorToast(error);
    },
  });

  const {
    data: { data: [journalVoucherData] = [] } = {}, // [journalVoucherData] = destructuring array first item
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['journalVoucher', searchTerm],
    queryFn: () => getJournalVoucherListing({ search: searchTerm }),
  });

  useEffect(() => {
    if (!isNullOrEmpty(journalVoucherData?.journal_vouchers)) {
      setRows(() => {
        const editRows = {};

        journalVoucherData?.journal_vouchers.forEach((x) => {
          editRows[x.id] = {
            id: x.id,
            ledger: x.ledger,
            account_id: x.account_id,
            narration: x.narration,
            currency_id: x.currency_id,
            fc_amount: x.fc_amount,
            rate: x.rate,
            lc_amount: x.lc_amount,
            sign: x.sign,
          };
        });
        setTotalDebit(parseFloat(journalVoucherData?.total_debit));
        setTotalCredit(parseFloat(journalVoucherData?.total_credit));

        return { ...editRows };
      });
    }
  }, [journalVoucherData]);

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      // Calculate total debit whenever lc_amount or sign changes
      if (field === 'lc_amount' || field === 'sign') {
        const totalDebit = Object.values(newRows).reduce((sum, row) => {
          const amount = parseFloat(row.lc_amount) || 0;
          return row.sign === 'Debit' ? sum + amount : sum;
        }, 0);
        setTotalDebit(totalDebit);
        const totalCredit = Object.values(newRows).reduce((sum, row) => {
          const amount = parseFloat(row.lc_amount) || 0;
          return row.sign === 'Credit' ? sum + amount : sum;
        }, 0);
        setTotalCredit(totalCredit);
      }

      return newRows;
    });
  }, []);

  // Expose updateField globally for modal callback
  React.useEffect(() => {
    window.updateJVRowField = updateField;
    return () => {
      delete window.updateJVRowField;
    };
  }, [updateField]);

  const handleSubmit = () => {
    if (totalDebit - totalCredit !== 0) {
      setShowError(true);
      return;
    }
    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    // All inputs must be filled except narration
    let transactions = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([key, v]) => {
          // Skip checking narration field
          if (key === 'narration') return true;
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    transactions = Object.values(transactions).map(({ id, ...rest }) => rest);

    const transformedTransactions = transactions?.reduce((acc, t, index) => {
      Object.entries(t).forEach(([key, value]) => {
        acc[`transactions[${index}][${key}]`] = value;
      });
      return acc;
    }, {});

    payload = {
      total_debit: totalDebit,
      total_credit: totalCredit,
      ...transformedTransactions,
      date,
    };
    updateJournalVoucherMutation.mutate({
      id: journalVoucherData.voucher_no,
      formData: payload,
    });
  };

  if (isError) {
    console.log(error);
  }

  return (
    <>
      <Row>
        <Col>
          <CustomTable
            headers={journalVoucherHeaders}
            isPaginated={false}
            className={'inputTable'}
            isLoading={isLoading}
            hideSearch
            hideItemsPerPage
          >
            <tbody>
              {isError && (
                <tr>
                  <td colSpan={journalVoucherHeaders.length}>
                    <p className="text-danger mb-0">
                      Unable to fetch data at this time
                    </p>
                  </td>
                </tr>
              )}
              {Object.values(rows).map((row, index) => (
                <JournalVoucherRow
                  key={row.id}
                  row={row}
                  index={index}
                  date={date}
                  isDisabled={isDisabled}
                  getAccountsByTypeOptions={getAccountsByTypeOptions}
                  currencyOptions={currencyOptions}
                  handleDeleteRow={handleDeleteRow}
                  updateField={updateField}
                  setShowMissingCurrencyRateModal={
                    setShowMissingCurrencyRateModal
                  }
                  newlyCreatedAccount={newlyCreatedAccount}
                  setShowAddLedgerModal={(modalType) => {
                    setShowAddLedgerModal(modalType);
                    setAddLedgerRowId(row.id);
                  }}
                  setCurrencyToSelect={setCurrencyToSelect}
                />
              ))}
            </tbody>
          </CustomTable>
          <div className="d-flex justify-content-between gap-3 mt-45 mb-5">
            <div>
              <CustomCheckbox
                readOnly={isDisabled}
                checked={getPrintSettings('journal_voucher')}
                style={{ border: 'none', userSelect: 'none' }}
                onChange={(e) => {
                  updatePrintSetting('journal_voucher', e.target.checked);
                }}
                label={'Print'}
              />
            </div>
            <div className="d-flex flex-column gap-2 mt-1">
              {!isLoading && !isError && (
                <>
                  <CustomInput
                    name="totalDebit"
                    label={'Total Debit'}
                    labelClass={'fw-medium'}
                    type="number"
                    showBorders={false}
                    error={false}
                    borderRadius={10}
                    value={totalDebit.toFixed(2) || ''}
                    readOnly
                  />
                  <CustomInput
                    name="totalCredit"
                    label={'Total Credit'}
                    labelClass={'fw-medium'}
                    type="number"
                    showBorders={false}
                    error={false}
                    borderRadius={10}
                    value={totalCredit.toFixed(2) || ''}
                    readOnly
                  />
                  <CustomInput
                    name="difference"
                    label={'Difference'}
                    labelClass={'fw-medium'}
                    type="number"
                    showBorders={false}
                    error={false}
                    borderRadius={10}
                    value={
                      (
                        parseFloat(totalDebit) - parseFloat(totalCredit)
                      ).toFixed(2) || ''
                    }
                    readOnly
                  />
                  {totalDebit - totalCredit !== 0 && showError && (
                    <p className="text-danger">Difference must be 0</p>
                  )}
                </>
              )}
            </div>
          </div>
        </Col>
      </Row>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Update', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleResetRows,
            variant: 'secondaryButton',
          },
        ]}
        loading={updateJournalVoucherMutation.isPending}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />

      {/* Attachements Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={journalVoucherData}
          deleteService={deleteJournalVoucherAttachment}
          uploadService={addJournalVoucherAttachment}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          queryToInvalidate={['journalVoucher', searchTerm]}
        />
      </CustomModal>
    </>
  );
};

export default EditJournalVoucher;
