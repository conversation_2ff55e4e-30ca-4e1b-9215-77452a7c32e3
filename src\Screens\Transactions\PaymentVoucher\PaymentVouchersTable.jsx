import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
 import { MOCK_PAYMENT_VOUCHER_TABLE_DATA } from '../../../Mocks/MockData';
import { paymentVoucherTableHeaders } from '../../../Utils/Constants/TableHeaders';
import { formatDate } from '../../../Utils/Utils';
import { getPaymentVoucherListing } from '../../../Services/Transaction/PaymentVoucher.js';
import { useFetchTableData } from '../../../Hooks/useTable';

const PaymentVouchersTable = ({
  date,
  filters,
  pagination,
  updatePagination,
  setPageState,
  setSearchTerm,
}) => {
    const {
      data: { data: paymentVoucherData = [] } = {},
      isLoading,
      isError,
      error,
    } = useFetchTableData(
      'paymentVoucherListing',
      {
        ...filters,
         date: date
      },
      updatePagination,
      getPaymentVoucherListing
    );

  // const tableData = MOCK_PAYMENT_VOUCHER_TABLE_DATA;
  // const isLoading = false;
  // const isError = false;
  // const error = null;

  if (isError) {
    console.error(error);
  }
  // if(!isLoading){
  //   console.log('aa',paymentVoucherData);
  // }

  return (
    <Row>
      <Col xs={12}>
        <CustomTable
          headers={paymentVoucherTableHeaders}
          pagination={pagination}
          updatePagination={updatePagination}
          isLoading={isLoading}
          hideItemsPerPage
          hideSearch
        >
          {(paymentVoucherData?.length || isError) && (

            <tbody>
              {isError && (
                <tr>
                  <td colSpan={paymentVoucherTableHeaders.length}>
                    <p className="text-danger mb-0">
                      Unable to fetch data at this time
                    </p>
                  </td>
                </tr>
              )}
              {paymentVoucherData?.map((item) => (
                <tr key={item.id}>
                  <td>{formatDate(item.date, 'DD/MM/YYYY')}</td>
                  <td
                    onClick={() => {
                      setSearchTerm(item.voucher.voucher_no);
                      setPageState('view');
                    }}
                  >
                    <p className="hyper-link text-decoration-underline cp mb-0">
                      {item.voucher.voucher_no}
                    </p>
                  </td>
                  <td>{item.new_ledger}</td>
                  <td>{item.account_details?.title}</td>
                  <td>{item.mode}</td>
                  <td>{item.mode_account_id?.account_name}</td>
                  <td>{item.currency?.currency_code}</td>
                  <td>{item.amount}</td>
                  <td>
                    <p
                      className={`mb-0 ${
                        item.commission_type == 'Income'
                          ? 'text-success'
                          : item.commission_type == 'Expense'
                            ? 'text-danger'
                            : ''
                      }`}
                    >
                      {item.commission ||
                        item?.special_commission?.total_commission}
                    </p>
                  </td>
                  <td>{item.vat_amount ? item.vat_amount : item.vat_terms}</td>
                  <td>{item.net_total}</td>
                  <td>{item?.lc_net_total}</td>
                  <td>{item.creator?.user_id}</td>
                  <td>{formatDate(item.created_at, 'HH:MM')}</td>
                  <td>
                    {item.attachments?.charAt(0).toUpperCase() +
                      item?.attachments?.slice(1)}
                  </td>
                </tr>

              ))}
            </tbody>
          )}
        </CustomTable>
      </Col>
    </Row>
  );
};

export default withFilters(PaymentVouchersTable);
