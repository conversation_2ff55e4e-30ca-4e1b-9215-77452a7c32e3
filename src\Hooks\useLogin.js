import { useMutation } from '@tanstack/react-query';
import { loginAdmin, loginUser } from '../Services/Auth';
import useUserStore from '../Stores/UserStore';
import useSettingsStore from '../Stores/SettingsStore';
import { showErrorToast } from '../Utils/Utils';
import { getSettings } from '../Services/Settings';

export function useLogin(role = 'business') {
  const {
    setUser,
    setRole,
    setToken,
    setIsSubscribed,
    setIsProfileCompleted,
    setSelectedBranch,
    setBranchName,
  } = useUserStore();

  const { setSettings } = useSettingsStore();

  return useMutation({
    mutationFn: role === 'admin' ? loginAdmin : loginUser,
    onSuccess: async (data) => {
      // Set the user in the store upon successful login
      setUser(data.user);
      setRole(data.role);
      setToken(data.token);
      setIsSubscribed(data.user.is_subscribed);
      setIsProfileCompleted(data.user.complete_profile);
      setSelectedBranch(data.user.selected_branch);
      setBranchName(data.user.branch_name);
      // Set cookie with JavaScript
      document.cookie = `token=${data.token}; path=/; secure; samesite=strict`;
      if (role !== 'admin') {
        try {
          const settingsResponse = await getSettings();
          if (settingsResponse) {
            setSettings(settingsResponse);
          } else {
            showErrorToast(
              settingsResponse.message || 'Failed to fetch settings'
            );
          }
        } catch (error) {
          showErrorToast('Failed to fetch settings');
          console.error('Settings fetch failed:', error);
        }
      }
    },
    onError: (error) => {
      showErrorToast(error);
      // Handle errors here (e.g., display a notification)
      console.error('Login Failed', error);
    },
  });
}
