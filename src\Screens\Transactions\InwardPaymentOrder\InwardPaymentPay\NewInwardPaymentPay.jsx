import { useMutation, useQuery } from '@tanstack/react-query';
import { Form, Formik } from 'formik';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import AccountBalanceCard from '../../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../../Components/AttachmentsView/AttachmentsView';
import CustomButton from '../../../../Components/CustomButton';
import CustomCheckbox from '../../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../../Components/CustomInput';
import CustomModal from '../../../../Components/CustomModal';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { useNationalities } from '../../../../Hooks/countriesAndStates';
import { getClassificationsWithType } from '../../../../Services/General';
import {
  changeInwardPaymentStatus,
  getBeneficiaryListing,
  getCOAAccountsbyMode,
  payInwardPayment,
  viewInwardPayment,
} from '../../../../Services/Transaction/InwardPayment';
import {
  getAccountsbyType,
  getChequeNumberByBank,
} from '../../../../Services/Transaction/JournalVoucher';
import { getVATType } from '../../../../Services/Transaction/ReceiptVoucher';
import {
  inwardPayValidationSchema,
  mainFormValidationSchema,
} from '../../../../Utils/Validations/ValidationSchemas';
import SignatureCanvas from 'react-signature-canvas';
import { showErrorToast } from '../../../../Utils/Utils';
import { showToast } from '../../../../Components/Toast/Toast';

const NewInwardPaymentPay = ({
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
}) => {
  const navigate = useNavigate();
  const formikRef = useRef();
  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedBank, setSelectedBank] = useState(null);
  const [selectedMode, setSelectedMode] = useState(null);
  const [selectedLedgerAccount, setSelectedLedgerAccount] = useState(null);

  const { id } = useParams();

  const { data: nationalities, isLoading: loadingNationalities } =
    useNationalities();

  const sigCanvas = useRef(null);
  const [trimmedDataURL, setTrimmedDataURL] = useState(null);
  function clear() {
    sigCanvas.current.clear();
  }

  function trim() {
    setTrimmedDataURL(sigCanvas.current.toDataURL());
  }

  // Data fetching
  const {
    data: inwardPaymentData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['viewInwardPayment', id],
    queryFn: () => viewInwardPayment(id),
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const handleSubmit = (values) => {
    const formValues = formikRef.current.values;
    let payload = {
      ...formValues,
      ...selectedFiles,
      signature: trimmedDataURL,
    };
    payInwardPaymentMutation.mutate(payload);

    console.log('submit payload:', payload);
  };

  const handleVatOutOfScope = (values) => {
    console.log('handleVatOutOfScope', values);
  };

  const handleCancel = () => {
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  const payTypeMap = {
    cash_deposit: 'Cash Deposit',
    cash_payment: 'Cash Payment',
    pdc: 'PDC',
    cheque_payment: 'Cheque Payment',
    cheque_deposit: 'Cheque Deposit',
  };

  const initialValues = useMemo(
    () => ({
      debite_note_number:
        'DBN' + (inwardPaymentData?.order?.voucher?.voucher_no || ''),
      date: inwardPaymentData?.order?.date || '',
      account: inwardPaymentData?.order?.debit_account_details?.title || '',
      pay_date: inwardPaymentData?.pay_date || '',
      pay_type:
        payTypeMap[inwardPaymentData?.pay_type] ||
        inwardPaymentData?.pay_type ||
        '',
      order_amount: inwardPaymentData?.fc_amount || '',
      ref_no: inwardPaymentData?.ref_no || '',
      balance_amount:
        inwardPaymentData?.paid?.balance_amount ||
        inwardPaymentData?.fc_amount ||
        '',
      beneficiary_id: inwardPaymentData?.beneficiary_id || '',
      ledger_account: '',
      contact_no: inwardPaymentData?.contact_no || '',
      vat_type: inwardPaymentData?.order?.voucher?.voucher_no || '',
      nationality: inwardPaymentData?.beneficiary?.nationalities?.name || '',
      vat_terms_id: '',
      sender_nationality_id: '',
      id_detail: inwardPaymentData?.id_number || '',
      settle_date: inwardPaymentData?.pay_date || '',
      place_of_issue: 'Dubai',
      cheque_id: '',
      sender: inwardPaymentData?.sender || '',
      due_date: inwardPaymentData?.pay_date || '',
      amount: inwardPaymentData?.balance_amount || '',
      origin_id: '',
      commission: '',
      purpose_id: inwardPaymentData?.beneficiary?.purposes?.id || '',
      vat_amount: '',
      net_total: '',
      narration: '',
      signature: '',
      mode: '',
      currency: '',
      account_id: '',
      vat_trn: 0,
      base_currency: inwardPaymentData?.currency?.currency_code || '',
    }),
    [inwardPaymentData]
  );

  // Get Classification Types
  const {
    data: classificationPurposeTypes,
    isLoading: typesLoading,
    isError: typesIsError,
    error: typesError,
  } = useQuery({
    queryKey: ['classificationTypes', 'purpose'],
    queryFn: () => getClassificationsWithType({ type: 'Purpose' }),
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Function to fetch Classification Types and show loading/error if api fails
  const getTypeOptions = () => {
    if (!typesLoading && !typesError) {
      return classificationPurposeTypes?.map((x) => ({
        value: x.id,
        label: x.description,
      }));
    } else {
      if (typesError) {
        console.error('Unable to fetch clasification types', error);
        return [{ label: 'Unable to fetch types', value: null }];
      } else {
        return [{ label: 'Loading...', value: null, isDisabled: true }];
      }
    }
  };

  // Get VAT Type
  const {
    data: vatType,
    isLoading: isLoadingVatType,
    isError: isErrorVatType,
    error: errorVatType,
  } = useQuery({
    queryKey: ['vatType'],
    queryFn: getVATType,
    refetchOnWindowFocus: false,
    retry: 1,
    staleTime: 1000 * 60 * 5,
  });

  const getVATTermsOptions = () => {
    if (isLoadingVatType)
      return [
        {
          label: 'Loading...',
          value: '',
        },
      ];
    if (isErrorVatType) {
      console.error('Unable to fetch VAT Terms', errorVatType);
      return [{ label: 'Unable to fetch VAT Terms', value: null }];
    }
    return vatType?.vats?.map((item) => ({
      label: `${item.title}${
        !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
      }`,
      // value: item.percentage,
      value: item.id,
    }));
  };

  // CHEQUE NUMBERS
  const {
    data: modeCheques,
    isLoading: isLoadingCheques,
    isError: isErrorCheques,
    error: errorCheques,
  } = useQuery({
    queryKey: ['bank_id', selectedBank],
    queryFn: () => getChequeNumberByBank(selectedBank),
    enabled:
      (selectedMode === 'Bank' || selectedMode === 'PDC') && !!selectedBank,
    staleTime: 1000 * 60 * 5,
  });

  // Memoized options
  const chequeOptions = useMemo(
    () =>
      modeCheques?.map((cheque) => ({
        label: cheque.cheque_number,
        value: cheque.id,
      })) || [],
    [modeCheques]
  );

  // Get account options //
  const { data: partyAccounts } = useQuery({
    queryKey: ['accounts', 'party'],
    queryFn: () => getAccountsbyType('party'),
    staleTime: 1000 * 60 * 5,
  });

  const { data: generalAccounts } = useQuery({
    queryKey: ['accounts', 'general'],
    queryFn: () => getAccountsbyType('general'),
    staleTime: 1000 * 60 * 5,
  });

  const { data: walkinAccounts } = useQuery({
    queryKey: ['accounts', 'walkin'],
    queryFn: () => getAccountsbyType('walkin'),
    staleTime: 1000 * 60 * 5,
  });

  //GET BENEFICIARY
  const {
    data: beneficiary,
    isLoading: isLoadingBeneficiary,
    isError: isErrorBeneficiary,
    error: errorBeneficiary,
  } = useQuery({
    queryKey: ['beneficiary'],
    queryFn: () => getBeneficiaryListing(),
    staleTime: 1000 * 60 * 5,
  });

  const getBeneficiaryOptions = () => {
    if (!isLoadingBeneficiary && !isErrorBeneficiary) {
      const options =
        beneficiary?.map((x) => ({
          value: x.id,
          label: x.name,
        })) || [];

      return [...options, { label: 'Add New Beneficiary', value: null }];
    } else {
      if (isErrorBeneficiary) {
        console.error('Unable to fetch beneficiary', errorBeneficiary);
        return [{ label: 'Unable to fetch beneficiary', value: null }];
      } else {
        return [{ label: 'Loading...', value: null, isDisabled: true }];
      }
    }
  };

  const getAccountsByTypeOptions = (accountType) => {
    if (!accountType)
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];

    const accounts =
      {
        party: partyAccounts,
        general: generalAccounts,
        walkin: walkinAccounts,
      }[accountType] || [];

    const options =
      accounts.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];

    const addNewLabel = {
      party: 'Add New PL',
      general: 'Add New GL',
      walkin: 'Add New WIC',
    }[accountType];

    if (addNewLabel) {
      options.push({ label: addNewLabel, value: null });
    }

    return options;
  };

  const {
    data: modeAccounts,
    isLoading: isLoadingModeAccounts,
    isError: isErrorModeAccounts,
    error: errorModeAccounts,
  } = useQuery({
    queryKey: ['modeAccounts', selectedMode],
    queryFn: () => getCOAAccountsbyMode(selectedMode),
    enabled: !!selectedMode,
    staleTime: 1000 * 60 * 5,
  });

  const getModeAccountOptions = () => {
    if (!isLoadingModeAccounts && !isErrorModeAccounts) {
      return (
        modeAccounts?.map((x) => ({
          value: x.id,
          label: x.account_name,
        })) || []
      );
    } else {
      if (isErrorModeAccounts) {
        console.error('Unable to fetch mode accounts', errorModeAccounts);
        return [{ label: 'Unable to fetch mode accounts', value: null }];
      } else {
        return [{ label: 'Loading...', value: null, isDisabled: true }];
      }
    }
  };

  // Mutation: Pay Inward Payment
  const payInwardPaymentMutation = useMutation({
    mutationFn: (formData) => payInwardPayment(id, formData),
    onSuccess: () => {
      showToast('Inward Payment Paid Successfully!', 'success');
      setTimeout(() => {
        navigate(-1);
      }, 300);
    },
    onError: (error) => {
      console.error('Error creating inward payment pay', error);
      showErrorToast(error);
    },
  });

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          enableReinitialize={true}
          // validationSchema={inwardPayValidationSchema}
          initialValues={initialValues}
          onSubmit={handleSubmit}
        >
          {({
            values,
            touched,
            errors,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // Auto-set fixed VAT if vat_type is 'fixed'
            useEffect(() => {
              if (vatType?.vat_type === 'fixed') {
                const vatValue = parseFloat(vatType?.vat_percentage);
                console.log(vatValue, 'vatValue');

                setFieldValue('vat_trn', vatValue);
                setFieldValue('vat_terms_id', null); // Optional: clear variable term
              }
            }, [vatType?.vat_type, vatType?.vat_percentage, values.commission]);
            // Calculates VAT and Net Total correctly
            useEffect(() => {
              const amount = parseFloat(values.amount) || 0;
              const commission = parseFloat(values.commission) || 0;
              const vatPercentage = parseFloat(values.vat_trn);

              const vatAmount = commission * (vatPercentage / 100);
              const netTotal = amount + commission + vatAmount;

              setFieldValue('vat_amount', vatAmount.toFixed(2));
              setFieldValue('net_total', netTotal.toFixed(2));
            }, [values.amount, values.commission, values.vat_trn]);

            return (
              <Form>
                <div className="row">
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                    <div className="row mb-4">
                      {/* First Row */}
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="debite_note_number"
                          label="Debite Note Number"
                          placeholder="DN15"
                          value={values.debite_note_number}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="date"
                          label="Date"
                          type="date"
                          value={values.date}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="account"
                          label="Account"
                          placeholder="Enter Account"
                          value={values.account}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="pay_date"
                          label="Pay Date"
                          type="date"
                          value={values.pay_date}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="pay_type"
                          label="Pay Type"
                          value={values.pay_type}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="order_amount"
                          label="Order Amount"
                          placeholder="EUR 5,000.00"
                          value={values.order_amount}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="ref_no"
                          label="Ref.No."
                          placeholder="003"
                          value={values.ref_no}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="balance_amount"
                          label="Balance Amount"
                          placeholder="EUR 5,000.00"
                          value={values.balance_amount}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <SearchableSelect
                          name="beneficiary_id"
                          label="Beneficiary"
                          options={getBeneficiaryOptions()}
                          value={values.beneficiary_id}
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('beneficiary_id', selected.value);
                            }
                          }}
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="contact_no"
                          label="Contact No"
                          placeholder="123456789"
                          value={values.contact_no}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="nationality"
                          label="Nationality"
                          value={values.nationality}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="id_detail"
                          label="ID Detail"
                          placeholder="Emirates ID, 12345678, dd/mm/yyyy"
                          value={values.id_detail}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="place_of_issue"
                          label="Place Of Issue"
                          placeholder="Dubai"
                          value={values.place_of_issue}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-3"></div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="sender"
                          label="Sender"
                          placeholder="Account ABC"
                          value={values.sender}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-4">
                        <SearchableSelect
                          label={'Nationality'}
                          name="sender_nationality_id"
                          options={
                            loadingNationalities
                              ? [
                                  {
                                    label: 'Loading...',
                                    value: null,
                                    isDisabled: true,
                                  },
                                ]
                              : nationalities
                          }
                          onChange={(v) => {
                            setFieldValue('sender_nationality_id', v.value);
                          }}
                          value={values.sender_nationality_id}
                          placeholder={'Select nationality'}
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <SearchableSelect
                          name="origin_id"
                          label="Origin"
                          options={[
                            { label: 'Select Origin', value: '' },
                            { label: 'Origin A', value: 'origin_a' },
                            { label: 'Origin B', value: 'origin_b' },

                            // Add origin options
                          ]}
                          value={values.origin_id}
                          onChange={(selected) =>
                            setFieldValue('origin_id', selected.value)
                          }
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <SearchableSelect
                          label={'Purpose'}
                          name="purpose_id"
                          options={
                            typesLoading
                              ? [
                                  {
                                    label: 'Loading...',
                                    value: null,
                                    isDisabled: true,
                                  },
                                ]
                              : getTypeOptions()
                          }
                          onChange={(v) => {
                            setFieldValue('purpose_id', v.value);
                          }}
                          value={values.purpose_id}
                          placeholder={'Select Purpose'}
                        />
                      </div>

                      {/* Second Row */}
                      <div className="col-12 col-sm-6 mb-3">
                        <SearchableSelect
                          name="ledger_account"
                          label="Ledger"
                          options={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          value={values.ledger_account}
                          onChange={(selected) => {
                            setFieldValue('ledger_account', selected.value);
                            setFieldValue('account_id', '');
                          }}
                        />
                      </div>

                      {values.ledger_account === 'general' && (
                        <div className="col-12 col-sm-6 mb-3">
                          <SearchableSelect
                            name="mode"
                            label="Mode"
                            options={[
                              { label: 'Cash', value: 'Cash' },
                              { label: 'Bank', value: 'Bank' },
                              { label: 'PDC', value: 'PDC' },
                              { label: 'Online', value: 'Online' },
                            ]}
                            value={values.mode}
                            onChange={(selected) => {
                              setFieldValue('mode', selected.value),
                                setSelectedMode(selected.value),
                                setSelectedBank('');
                            }}
                          />
                        </div>
                      )}

                      <div className="col-12 col-sm-6 mb-3">
                        <SearchableSelect
                          label="Account"
                          name={'account_id'}
                          options={
                            values.ledger_account === 'general'
                              ? getModeAccountOptions()
                              : getAccountsByTypeOptions(values.ledger_account)
                          }
                          placeholder={'Select Account'}
                          value={values.account_id}
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('account_id', selected.value);
                              setSelectedLedgerAccount(selected.value);
                              setSelectedBank(selected.value); //for cheque number
                            }
                          }}
                          onBlur={handleBlur}
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <SearchableSelect
                          name="vat_type"
                          label="VAT Type"
                          options={[
                            { label: 'Charge', value: 'charge' },
                            { label: 'Absorb', value: 'absorb' },
                          ]}
                          value={values.vat_type}
                          onChange={(selected) =>
                            setFieldValue('vat_type', selected.value)
                          }
                        />
                      </div>

                      {vatType?.vat_type === 'variable' && (
                        <div className="col-12 col-sm-6 mb-3">
                          <SearchableSelect
                            name="vat_terms_id"
                            label="VAT Terms"
                            options={getVATTermsOptions()}
                            value={values.vat_terms_id}
                            onChange={(selected) => {
                              let vatValue = 0;

                              if (vatType?.vat_type === 'fixed') {
                                // Use fixed percentage from API
                                vatValue = parseFloat(vatType?.vat_percentage);
                              } else if (vatType?.vat_type === 'variable') {
                                // Use percentage from selected VAT term
                                const selectedVat = vatType?.vats?.find(
                                  (v) => v.id === selected.value
                                );

                                if (selectedVat?.title === 'Out of Scope') {
                                  setShowVatOutOfScopeModal(true);
                                }

                                vatValue =
                                  parseFloat(selectedVat?.percentage) || 0;
                              }
                              setFieldValue('vat_terms_id', selected.value);
                              setFieldValue('vat_trn', vatValue); // For VAT calculation
                            }}
                            isDisabled={vatType?.vat_type === 'fixed'}
                          />
                        </div>
                      )}

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="settle_date"
                          label="Settle Date"
                          type="date"
                          value={values.settle_date}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>
                      {(values.mode === 'Bank' || values.mode === 'PDC') && (
                        <div className="col-12 col-sm-6 mb-3">
                          <SearchableSelect
                            name={'cheque_id'}
                            label="Cheque Number"
                            options={chequeOptions}
                            placeholder={'Select Cheque Number'}
                            value={values.cheque_id}
                            onChange={(selected) => {
                              setFieldValue('cheque_id', selected.value);
                            }}
                            onBlur={handleBlur}
                          />
                        </div>
                      )}

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="due_date"
                          label="Due Date"
                          type="date"
                          value={values.due_date}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-1 col-sm-1 mb-3">
                        <CustomInput
                          name="base_currency"
                          label="Amount"
                          value={values.base_currency}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-5 mb-3">
                        <CustomInput
                          name="amount"
                          label=" "
                          placeholder="Enter Amount"
                          value={values.amount}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="commission"
                          label="Commission"
                          placeholder="Enter Commission"
                          value={values.commission}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>

                      <div className="col-1 col-sm-1 mb-3">
                        <CustomInput
                          name="vat_trn"
                          label="VAT Amount"
                          value={`${parseFloat(values?.vat_trn).toFixed(0)}%`}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-5 mb-3">
                        <CustomInput
                          name="vat_amount"
                          label="."
                          placeholder="0.00"
                          value={values.vat_amount}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          disabled
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="net_total"
                          label="Net Total"
                          placeholder="0.00"
                          value={values.net_total}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>

                      {/* Narration and Signature */}
                      <div className="col-12 mb-3">
                        <CustomInput
                          name="narration"
                          label="Narration"
                          type="textarea"
                          rows={4}
                          placeholder="Enter Narration"
                          value={values.narration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 mb-3">
                        <label>Signature</label>

                        <SignatureCanvas
                          ref={sigCanvas}
                          penColor="green"
                          canvasProps={{
                            height: 200,
                            className: 'sigCanvas',
                          }}
                        />
                        <div className="mt-4">
                          <button
                            type="button" // ✅ prevent submit
                            className="customButton"
                            style={{ width: '20px', marginRight: '15px' }}
                            onClick={clear}
                          >
                            Clear
                          </button>
                          <button
                            type="button" // ✅ prevent submit
                            className="customButton"
                            style={{ width: '20px' }}
                            onClick={trim}
                          >
                            Trim
                          </button>
                        </div>
                        {trimmedDataURL ? (
                          <img alt="signature" src={trimmedDataURL} />
                        ) : null}
                      </div>
                    </div>
                  </div>
                  <div className="col-0  col-xxl-2" />
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                    <div className="row">
                      {/* Right side cards */}
                      <div
                        className="col-12 mb-5"
                        style={{ maxWidth: '350px' }}
                      >
                        <AccountBalanceCard />
                      </div>
                    </div>
                  </div>

                  <div className="d-flex flex-wrap justify-content-start mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => {}}
                      />
                      <CustomCheckbox
                        label="Print"
                        style={{ border: 'none', margin: 0 }}
                      />
                    </div>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <VoucherNavigationBar
        isNavigationShow={false}
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setSelectedFiles}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms_id = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    r
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Submit'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => {
                        setShowVatOutOfScopeModal(false);
                        formikRef.current.values.vat_terms_id = '';
                      }}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default NewInwardPaymentPay;
