import { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useLocation } from 'react-router-dom';
import BackButton from '../../../Components/BackButton';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import useAccountsByType from '../../../Hooks/useAccountsByType';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import NewSpecialCommission from './NewSpecialCommission';

const SpecialCommission = () => {
  const { state } = useLocation();
  usePageTitle('Special Commission');

  // [new, view, edit,  listing]
  // View is for specific Suspense Voucher search and view it's detail
  // Edit is for editing the specific Suspense Voucher's detail
  // Listing is for Suspense Voucher listing table
  const [pageState, setPageState] = useState('new');

  // const [isDisabled, setIsDisabled] = useState(true);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);

  // Get account options using custom hook //
  const { getAccountsByTypeOptions } = useAccountsByType();
  // --- //

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  const renderPageContent = () => {
    const pageComponents = {
      new: (
        <NewSpecialCommission
          state={state}
          getAccountsByTypeOptions={getAccountsByTypeOptions}
          newlyCreatedAccount={newlyCreatedAccount}
          setShowAddLedgerModal={setShowAddLedgerModal}
        />
      ),
    };

    return pageComponents[pageState] || null;
  };

  return (
    <>
      <section className="position-relative">
        <div className="d-flex flex-column flex-wrap mb-4">
          <BackButton />
          <h2 className="screen-title mb-0">Special Commission</h2>
        </div>
        <Row>
          <Col xs={12}>{renderPageContent()}</Col>
        </Row>
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
      >
        {renderAddLedgerForm()}
      </CustomModal>
    </>
  );
};

export default SpecialCommission;
