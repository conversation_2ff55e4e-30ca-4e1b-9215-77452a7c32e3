.combined-select-container {
  /* margin-bottom: 1.5rem; */
  position: relative;
}

.combined-select-input {
  display: flex;
  align-items: center;
  border: 1px solid rgba(100, 100, 100, 0.22);
  border-radius: 5px;
  background-color: var(--input-bg-color, white);
  margin-top: 2px;
  overflow: visible;
  height: 47px;
  padding: 4px 0px 5px 4px;
}

.combined-select-input:hover {
  border-color: var(--primary-color, #39ae94) !important;
}

.combined-select-left,
.combined-select-right {
  flex: 1;
  position: relative;
}

.separator {
  padding: 0 8px;
  color: rgba(100, 100, 100, 0.5);
  font-weight: normal;
  font-size: 16px;
}
.separator-between-selects {
  padding: 0 8px;
  color: rgba(100, 100, 100, 0.5);
  font-weight: normal;
  font-size: 16px;
}


/* Override react-select styles */
.currency-select{
  width: 90%;
}
.currency-select {
  height: 100%;
}

.amount-input {
  width: 100%;
  height: 100%;
  border: none;
  background-color: transparent;
  padding: 0 12px;
  color: var(--input-label-color, #333);
}

.amount-input:focus {
  outline: none;
}

.amount-input:disabled {
  background-color: transparent;
  cursor: not-allowed;
}

.amount-input::placeholder {
  color: #666;
}

.currency-select__control {
  height: 100% !important;
  min-height: unset !important;
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

.currency-select__value-container {
  padding: 2px 8px !important;
  height: 100%;
}

.currency-select__placeholder {
  color: #666 !important;
}

.currency-select__single-value {
  color: var(--input-label-color, #333) !important;
}

.currency-select__indicators {
  height: 100%;
}

.currency-select__menu {
  z-index: 9999 !important;
}

/* SearchableSelect menu list styles for fixed Add New button */
.searchable-select-menu-list {
  display: flex !important;
  flex-direction: column !important;
  padding: 0 !important;
  overflow: visible !important;
  height: 100%;
}

.scrollable-options {
  max-height: 200px;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: thin; /* For Firefox */
  position: relative;
  z-index: 1;
  will-change: scroll-position; /* Optimize for scrolling */
  touch-action: pan-y; /* Allow vertical touch scrolling */
}

.fixed-add-new-option {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Style for the Add New option */
.fixed-add-new-option > div {
  text-align: center !important;
  font-weight: 500;
}

/* Custom scrollbar styling */
.scrollable-options::-webkit-scrollbar {
  width: 8px;
}

.scrollable-options::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  border-radius: 4px;
}

.scrollable-options::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}

.scrollable-options::-webkit-scrollbar-thumb:hover {
  background-color: #aaa;
}

.error-container {
  display: flex;
  justify-content: space-between;
}

.input-error-message {
  font-size: 12px;
  margin-top: 4px;
}
