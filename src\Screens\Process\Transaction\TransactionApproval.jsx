import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import {
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiPaperClip,
} from 'react-icons/hi2';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../Components/StatusChip/StatusChip';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import withModal from '../../../HOC/withModal';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getPendingTransactionApprovalListing } from '../../../Services/Process/Transaction';
import { statusFiltersConfig } from '../../../Utils/Constants/TableFilter';
import { transactionApprovalHeaders } from '../../../Utils/Constants/TableHeaders';
import { showErrorToast } from '../../../Utils/Utils';
import CustomModal from '../../../Components/CustomModal';
import { Form, Formik } from 'formik';
import CustomInput from '../../../Components/CustomInput';
import CustomButton from '../../../Components/CustomButton';

const TransactionApproval = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
  showModal,
}) => {
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const {
    data: { data: pendingTransactionApprovalData = [] } = {},
    isLoading: isLoadingPendingTransactionApproval,
    isError: isErrorPendingTransactionApproval,
    error: pendingTransactionApprovalError,
  } = useFetchTableData(
    'pendingTransactionApprovalListing',
    filters,
    updatePagination,
    getPendingTransactionApprovalListing
  );

  const handleApproveClick = (item) => {
    console.log(item.id);
  };
  const handleRejectClick = (item) => {
    console.log(item.id);
    setShowRejectionModal(true);
  };
  const handleReject = () => {
    setShowRejectionModal(false);
  };
  const handleAttachmentClick = (item) => {
    console.log(item.id);
    setSelectedItem(item);
    setShowAttachmentsModal(true);
  };
  if (pendingTransactionApprovalError) {
    showErrorToast(pendingTransactionApprovalError, 'error');
  }

  return (
    <>
      <div className="d-flex justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">Transaction Approval</h2>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={transactionApprovalHeaders}
            pagination={pagination}
            isLoading={isLoadingPendingTransactionApproval}
            selectOptions={[
              {
                title: 'Transaction Type',
                options: statusFiltersConfig,
              },
            ]}
            dateFilters={[{ title: 'Period', type: 'date' }]}
            rangeFilters={[{ title: 'Transaction No.', type: 'number' }]}
          >
            {(pendingTransactionApprovalData.length ||
              isErrorPendingTransactionApproval) && (
              <tbody>
                {isErrorPendingTransactionApproval && (
                  <tr>
                    <td colSpan={transactionApprovalHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {pendingTransactionApprovalData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item?.trans_type}</td>
                    <td>{item?.trans_no}</td>
                    <td>{item?.trans_date}</td>
                    <td>{item?.party}</td>
                    <td>{item?.secondary_account}</td>
                    <td>{item?.currency}</td>
                    <td>{item?.amount}</td>
                    <td>{item?.user_id}</td>
                    <td>{item?.approved_by}</td>
                    <td>{item?.received_from_paid_to}</td>
                    <td>{item?.comment}</td>
                    <td>
                      <StatusChip status={item?.status} />
                    </td>
                    <td>{item?.attachments}</td>
                    <td>
                      <TableActionDropDown
                        actions={[
                          ...(item?.status?.toLowerCase() === 'pending'
                            ? [
                                {
                                  name: 'Approve',
                                  icon: HiOutlineCheckCircle,
                                  onClick: () => handleApproveClick(item),
                                  className: 'view with-color',
                                },
                                {
                                  name: 'Reject',
                                  icon: HiOutlineXCircle,
                                  onClick: () => handleRejectClick(item),
                                  className: 'delete with-color',
                                },
                              ]
                            : []),
                          ...(item?.attachments?.toLowerCase() === 'yes'
                            ? [
                                {
                                  name: 'Attachment',
                                  icon: HiPaperClip,
                                  className: 'attachments',
                                  onClick: () => handleAttachmentClick(item),
                                },
                              ]
                            : []),
                        ]}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
      {/* Profit & Loss Posting Modal  */}
      <CustomModal
        show={showRejectionModal}
        close={() => setShowRejectionModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle px-5">Rejection Reason</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ rejection_reason: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handleReject}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'rejection_reason'}
                    type={'textarea'}
                    required
                    label={'Comments'}
                    value={values.rejection_reason}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.rejection_reason && errors.rejection_reason}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Submit'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowRejectionModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
      {/* Attachments Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          item={selectedItem}
          // queryToInvalidate={'documentRegisterListing'}
          // deleteService={deleteDocumentRegisterAttachment}
          // uploadService={addDocumentRegisterAttachment}
          closeUploader={setShowAttachmentsModal}
        />
      </CustomModal>
    </>
  );
};

export default withModal(withFilters(TransactionApproval));
