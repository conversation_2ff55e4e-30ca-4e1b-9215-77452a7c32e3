import { Form, Formik } from 'formik';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import ExchangeRatesCard from '../../../Components/ExchangeRatesCard/ExchangeRatesCard';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { MOCK_EXCHANGE_RATES } from '../../../Mocks/MockData';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { internalPaymentVoucherHeaders } from '../../../Utils/Constants/TableHeaders';
import InternalPaymentVoucher from './InternalPaymentVoucher';
import InternalPaymentVoucherRow from './InternalPaymentVoucherRow';
import { getAccountsbyType, getVATType } from '../../../Services/Transaction/PaymentVoucher.js';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createJournalVoucher,
  getChequeNumberByBank,
  getCurrencyRate,
} from '../../../Services/Transaction/JournalVoucher.js';
import { getCostCenterRegisterListing } from '../../../Services/Masters/CostCenterRegister.js';
import { getCurrencyOptions, isNullOrEmpty, showErrorToast } from '../../../Utils/Utils.jsx';
import useSettingsStore from '../../../Stores/SettingsStore.js';
import { showToast } from '../../../Components/Toast/Toast.jsx';
import { createInternalPaymentVoucher, getInternalPaymentVoucherChequeNumberByBank } from '../../../Services/Transaction/InternalPaymentVoucher.js';
import useFormStore from '../../../Stores/FormStore.js';

const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      ledger: '',
      debit_account_id: '',
      narration: '',
      currency_id: '',
      amount: '',
      vat_percentage: '',
      vat_terms: '',
      vat_amount: '',
      total: '',
    };
  });
  return rows;
};
const INITIAL_STATE = generateInitialRows(5);

const NewInternalPaymentVoucher = ({
  isDisabled = false,
  setIsDisabled,
  setShowAddLedgerModal,
  newlyCreatedAccount,
   uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
    modesData,
    accountData,
    currencyOptions,
                                     date,
                                     lastVoucherNumbers,
}) => {
  const formikRef = useRef();
  const [rows, setRows] = useState(INITIAL_STATE);
  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedBank, setSelectedBank] = useState(null);
  const [dueDate, setDueDate] = useState('');
  const [isDueDateEditable, setIsDueDateEditable] = useState(false);
  const [voucherDate, setVoucherDate] = useState(new Date().toISOString().split('T')[0]);
  const [totalNetTotal, setTotalNetTotal] = useState(0);
  const [showSubmitError, setShowSubmitError] = useState(false);
  const { getPrintSettings } = useSettingsStore();
  const [showError, setShowError] = useState(false);
  const [addedAttachments, setAddedAttachments] = useState([]);
  const queryClient = useQueryClient();
    const [isChequeFieldEnabled, setIsChequeFieldEnabled] = useState(false);
  // const handleSubmit = () => {
  //   const formValues = formikRef.current.values;
  //   console.log('formValues', formValues);
  //   console.log('selectedFiles', selectedFiles);
  //   let payload = {
  //     ...formValues,
  //     ...selectedFiles,
  //   };
  //
  //   if (payload.mode === 'online') {
  //     setShowPaymentModal(true);
  //   } else {
  //     console.log('submit payload:', payload);
  //   }
  // };

  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'internal-payment-voucher'; // Unique identifier for this form


  const handleSubmit = async () => {

    //console.log('asdsdasadads',formikRef);

    if (!formikRef.current) return;

    // Check for any rows with error=true
    const hasErrors = Object.values(rows).some((row) => row.error === true);
    if (hasErrors) {
      showErrorToast({ message: hasErrors });
      return;
    }


    // Validate the form
    const errors = await formikRef.current.validateForm();
    console.log('errors',errors)
    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show errors
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return; // Do not submit if there are errors
    }

    const formValues = formikRef.current.values;


    let payload = {
      ...rows,
    };

  //  console.log('payload',payload);

    // Remove rows that have empty values
    // All inputs must be filled except narration
    let transactions = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([key, v]) => {
          // Skip checking narration field
          if (key === 'narration') return true;
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

   // console.log('transactions',transactions)

    if (isNullOrEmpty(transactions)) {
      setShowSubmitError(true);
      return;
    }

    transactions = Object.values(transactions).map(({ id, ...rest }) => rest);

    const transformedTransactions = transactions?.reduce((acc, t, index) => {
      Object.entries(t).forEach(([key, value]) => {
        acc[`vats[${index}][${key}]`] = value;
      });
      return acc;
    }, {});

   // console.log('transformedTransactions',transformedTransactions)


    payload = {
      ...formValues,
      date,
      amount : parseFloat(totalNetTotal.toFixed(2)),
      mode :  formValues.mode.charAt(0).toUpperCase() + formValues.mode.slice(1),
      due_date : dueDate,
      ...transformedTransactions,
      ...addedAttachments,
    };

    console.log('payload final',payload);

    createInternalPaymentVoucherMutation.mutate(payload);
  };

  const handleResetRows = () => {
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // Clear saved form values when resetting
    clearFormValues(formId);
    clearFormValues('special-commission');
  };



  const createInternalPaymentVoucherMutation = useMutation({
    mutationFn: createInternalPaymentVoucher,
    onSuccess: (data) => {
      showToast('Internal Payment Voucher Created!', 'success');
      if (getPrintSettings('internal_payment_voucher')) {
        window.open(data.detail?.pdf_url, '_blank');
      }
      queryClient.invalidateQueries(['internalPaymentVoucherListing']);
      handleResetRows();

    },
    onError: (error) => {
      console.error('Error creating Journal Voucher', error);

        showErrorToast(error);
    },
  });

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);
  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    newRows[id] = {
      id,
      ledger: '',
      debit_account_id: '',
      narration: '',
      currency_id: '',
      amount: '',
      vat_terms: '',
      vat_percentage: '',
      vat_amount: '',
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };
  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };
  const handleCancel = () => {
    setIsDisabled(true);
    setRows(INITIAL_STATE);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  const handleVatOutOfScope = (values) => {
    console.log('handleVatOutOfScope', values);
  };


  const getAccountsByTypeOptions = (accountType) => {

    console.log(accountType);

    if (!accountType) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
    accountData[accountType] || {};


    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Accounts', errorMessage);
      return [{ label: 'Unable to fetch Accounts', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];
    switch (accountType) {
      case 'party':
        options.push({
          label: `Add New PL`,
          value: null,
        });
        break;
      case 'general':
        options.push({
          label: `Add New GL`,
          value: null,
        });
        break;
      case 'walkin':
        options.push({
          label: `Add New WIC`,
          value: null,
        });
        break;
      default:
        break;
    }
    return options;
  };

  let getcostCenterData = [];

  const {
    data: costCenterData,
    isLoading: isLoadingCostCenterData,
    isError: isErrorCostCenterData,
    error: errorCostCenterData,
  } = useQuery({
    queryKey: ['per_page', 50],
    queryFn: () => getCostCenterRegisterListing(),
    staleTime: 1000 * 60 * 5,
  });

  // Defensive check depending on API structure
  if (Array.isArray(costCenterData)) {
    getcostCenterData = costCenterData.map((cost) => ({
      label: cost.code,
      value: cost.id,
    }));
  } else if (Array.isArray(costCenterData?.data)) {
    getcostCenterData = costCenterData.data.map((cost) => ({
      label: cost.code,
      value: cost.id,
    }));
  }

  console.log(getcostCenterData);

  useEffect(() => {
    costCenterData;
  }, [costCenterData]);



  const getAccountsByTypeMode = (mode) => {
    if (!mode) {
      return [{ label: 'Select Mode', value: null, isDisabled: true }];
    }

    const { data, loading, error, errorMessage } =
    modesData[mode] || {};

 //   console.log(modesData);

    if (mode === 'bank') {
      setDueDate(voucherDate);
      setIsDueDateEditable(true);
      setIsChequeFieldEnabled(true);
      
    } else if (mode === 'pdc') {
      const tomorrow = new Date(voucherDate);
      tomorrow.setDate(tomorrow.getDate() + 1);
      setDueDate(tomorrow.toISOString().split('T')[0]);
      setIsDueDateEditable(true);

      setIsChequeFieldEnabled(true);

    } else {
      setDueDate('');
      setIsDueDateEditable(false);
      setIsChequeFieldEnabled(false);

    }

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch Mode', errorMessage);
      return [{ label: 'Unable to fetch Mode', value: null }];
    }
    return data?.map((x) => ({
      value: x?.id,
      label: x?.account_name,
    })) || [];
  };




  const {
    data: modeCheques,
    isLoading: isLoadingCheques,
    isError: isErrorCheques,
    error: errorCheques,
  } = useQuery({
    queryKey: ['bank_id', selectedBank],
    queryFn: () => getInternalPaymentVoucherChequeNumberByBank(selectedBank),
    staleTime: 1000 * 60 * 5,
  });

  console.log('selectedBank',selectedBank)

  console.log('modeCheques',modeCheques);

  const chequeOptions =
    modeCheques?.map((cheque) => ({
      label: cheque.cheque_number, // adjust this based on your API response
      value: cheque.id,
    })) || [];

  useEffect(() => {
    modeCheques;
  }, [modeCheques]);


  // Query
  // Get VAT Type //
  const {
    data: vatType,
    isLoading: isLoadingVatType,
    isError: isErrorVatType,
    error: errorVatType,
  } = useQuery({
    queryKey: ['vatType'],
    queryFn: getVATType,
    refetchOnWindowFocus: false,
    retry: 1,
    staleTime: 1000 * 60 * 5,
  });

  const vatData = {
    vatType,
    isLoadingVatType,
    isErrorVatType,
    errorVatType,
  };

  console.log('vat data', vatData);
  console.log('rows =', rows);

  useEffect(() => {
    const total = Object.values(rows).reduce((sum, row) => {
      const net = parseFloat(row.total);
      return sum + (isNaN(net) ? 0 : net);
    }, 0);

    setTotalNetTotal(total);
  }, [rows]);


  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            ledger: '',
            account_id: '',
            cost_center_id: '',
            mode: '',
            mode_account_id : '',
            cheque_number: '',
            due_date: '',
            currency_id: '',
            amount: '',
            narration: '',
          }}
          validate={(values) => {
            const errors = {};

            // Required fields for special commission
            if (!values.ledger) errors.ledger = 'Ledger is required';
            if (!values.account_id) errors.account_id = 'Account is required';
            // if (!values.amount) errors.amount = 'Amount is required';
            if (!values.currency_id)
              errors.currency_id = 'Currency is required';
            // if (!values.commission_type)
            //   errors.commission_type = 'Commission Type is required';

            return errors;
          }}
        >
          {({
            values,
            touched,
            errors,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-xxl-9">
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-45">
                      <div className="combined-select-container">
                        <label className="mainLabel">Ledger</label>
                        <div className={`combined-select-input ${isDisabled ? 'disabled-combined-select': ''}`}>
                          <div className="combined-select-left">
                            <SearchableSelect
                              name={'ledger'}
                              options={[
                                { label: 'PL', value: 'party' },
                                { label: 'GL', value: 'general' },
                                { label: 'WIC', value: 'walkin' },
                              ]}
                              className={"ledger-select__control"}
                              isDisabled={isDisabled}
                              placeholder={'Ledger'}
                              value={values.ledger}
                              onChange={(selected) => {
                                setFieldValue('ledger', selected.value);
                              }}
                              onBlur={handleBlur}
                            />
                          </div>
                          <div className="separator-between-selects">|</div>
                          <div className="combined-select-right">
                            <SearchableSelect
                              name={'account_id'}
                              options={getAccountsByTypeOptions(values.ledger)}

                              isDisabled={isDisabled}
                              placeholder={'Select Account'}
                              value={values.account_id}
                              className={"account-select__control"}
                              onChange={(selected) => {
                                if (
                                  selected.label?.toLowerCase()?.startsWith('add new')
                                ) {
                                  setShowAddLedgerModal(
                                    selected.label?.toLowerCase()
                                  );
                                } else {
                                  setFieldValue('account_id', selected.value);
                                }
                              }}
                              onBlur={handleBlur}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'cost_center_id'}
                        label={'Cost Center'}
                        options={getcostCenterData}
                        isDisabled={isDisabled}
                        placeholder={'Select Cost Center'}
                        value={values.cost_center_id}
                        onChange={(selected) => {
                          setFieldValue('cost_center_id', selected.value);
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    {/* MergeSelect */}
                    <div className="col-12 col-sm-6 mb-45">
                      <div className="combined-select-container">
                        <label className="mainLabel">Mode</label>
                        <div className={`combined-select-input ${isDisabled ? 'disabled-combined-select': ''}`}>
                          <div className="combined-select-left">
                            <SearchableSelect
                              name={'mode'}
                              className={'mode-select__control'}
                              options={[
                                {
                                  label: 'Cash',
                                  value: 'cash',
                                },
                                {
                                  label: 'Bank',
                                  value: 'bank',
                                },
                                {
                                  label: 'PDC',
                                  value: 'pdc',
                                },
                                {
                                  label: 'Online',
                                  value: 'online',
                                },
                              ]}
                              isDisabled={isDisabled}
                              placeholder={'Mode'}
                              value={values.mode}
                              onChange={(selected) => {
                                setFieldValue('mode', selected.value);
                              }}
                              onBlur={handleBlur}
                            />
                          </div>
                          <div className="separator-between-selects">|</div>
                          <div className="combined-select-right">
                            <SearchableSelect
                              name={'mode_account_id'}
                              className={'account-select__control'}
                              options={getAccountsByTypeMode(values.mode)}
                              isDisabled={isDisabled}
                              placeholder={'Select Account'}
                              value={values.mode_account_id}
                              onChange={(selected) => {
                                setFieldValue('mode_account_id', selected.value);
                                setSelectedBank(selected.value);
                                console.log(selectedBank);
                              }}
                              onBlur={handleBlur}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'cheque_number'}
                        label={'Cheque Number'}
                        options={chequeOptions}
                        isDisabled={!isChequeFieldEnabled}
                        placeholder={'Select Cheque Number'}
                        value={values.cheque_number}
                        onChange={(selected) => {
                          setFieldValue('cheque_number', selected.value);
                        }}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'due_date'}
                        label={'Due Date'}
                        type={'date'}
                        disabled={!isDueDateEditable}
                        value={dueDate}
                        min={
                          values.mode === 'pdc'
                            ? new Date(
                              new Date(voucherDate).setDate(
                                new Date(voucherDate).getDate() + 1,
                              ),
                            )
                              .toISOString()
                              .split('T')[0]
                            : undefined
                        }
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.due_date && errors.due_date}
                      />
                    </div>

                    {/* MergeSelect */}
                    <div className="col-12 col-sm-6 mb-45">
                      <div className="combined-select-container">
                        <label className="mainLabel">Currency</label>
                        <div className={`combined-select-input ${isDisabled ? 'disabled-combined-select': ''}`}>
                          <div className="combined-select-left">
                            <SearchableSelect
                              name={'currency_id'}

                              options={currencyOptions}
                              className={"account-select__control"}
                              isDisabled={isDisabled}
                              placeholder={'Currency'}
                              value={values.currency_id}
                              onChange={(selected) => {
                                setFieldValue('currency_id', selected.value);
                              }}
                              onBlur={handleBlur}
                            />
                          </div>
                          <div className="separator-between-selects"></div>
                          <div className="combined-select-right">
                            <CustomInput
                              name={'amount'}
                              inputClass={"ledger-select__control mt-1"}
                              type={'number'}
                              disabled={true}
                              placeholder={'Enter Amount'}
                              value={totalNetTotal.toFixed(2)}
                              // onChange={handleChange}
                              onBlur={handleBlur}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-12 mb-3">
                      <CustomInput
                        name={'narration'}
                        label={'Narration'}
                        type={'textarea'}
                        rows={4}
                        placeholder={'Enter Narration'}
                        disabled={isDisabled}
                        value={values.narration}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.narration && errors.narration}
                      />
                    </div>
                  </div>
                </div>
                {!isDisabled && (
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                    <div className="row">
                      {/* Right side cards */}
                      <div
                        className="col-12 mb-5"
                        style={{ maxWidth: '350px' }}
                      >
                        <AccountBalanceCard />
                        <ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} />
                      </div>
                    </div>
                  </div>
                )}

                <CustomTable
                  displayCard={false}
                  headers={internalPaymentVoucherHeaders}
                  isPaginated={false}
                  className={'inputTable'}
                  hideSearch
                  hideItemsPerPage
                >
                  <tbody>
                    {Object.values(rows).map((row, index) => (
                      <InternalPaymentVoucherRow
                        key={row.id}
                        row={row}
                        index={index}
                        isDisabled={isDisabled}
                        handleDeleteRow={handleDeleteRow}
                        updateField={updateField}
                        accountData={accountData}
                        setShowAddLedgerModal={setShowAddLedgerModal}
                        currencyOptions = {currencyOptions}
                        vatData = {vatData}
                        currency = {values.currency_id}
                        rows = {rows}
                      />
                    ))}
                  </tbody>
                </CustomTable>

                <div className="d-flex flex-wrap justify-content-start mb-5">
                  <div className="d-inline-block mt-3">
                    <CustomCheckbox
                      label="Account Balance"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                      onChange={() => { }}
                    />
                    <CustomCheckbox
                      label="Print"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
       loading={createInternalPaymentVoucherMutation.isPending}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherHeading="Last IPV Number"
        lastVoucherNumbers={lastVoucherNumbers}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
                uploadOnly
                getUploadedFiles={setAddedAttachments}
                closeUploader={() => setUploadAttachmentsModal(false)}
              />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    r
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Submit'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => {
                        setShowVatOutOfScopeModal(false);
                        formikRef.current.values.vat_terms = '';
                      }}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default NewInternalPaymentVoucher;
