import axiosInstance from '../../Config/axiosConfig';
import { balanceWriteOffData } from '../../Mocks/MockData';

// GET Receivables
export const getbalanceWriteOffListing = async (params) => {
  try {
    // const { data } = await axiosInstance.get('/user-api/beneficiary-register', {
    //   params,
    // });
    return balanceWriteOffData.detail;
    return data.detail; // Assume this returns the listing object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
