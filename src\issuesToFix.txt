Page Specific

General
- Add RightIcon for Date type input
- focus on input when modal opens
- Modal button disable/loading in delete pages
- <PERSON><PERSON> in User Maintenance Page on checkboxes

Enhancements
- Loader in Subscription Logs
- Loader in Change Subscription
- MultiLevelDropdown animation
- Fix Add more VAT button in Branch Management
- Add a better 404 page
- Create combined SearchableSelect. Search for 'MergeSelect' in codebase.
- Remittance ROE fix default add row (action is missing)
- outOfScope is not being saved in formStore (New/Edit ReceiptVoucher)
- Optimistically update statuses every where
- import whole module screens from one file for freelance work - App.jsx