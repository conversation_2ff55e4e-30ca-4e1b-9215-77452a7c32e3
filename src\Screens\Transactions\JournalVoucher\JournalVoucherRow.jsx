import { useEffect, useState } from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import Skeleton from 'react-loading-skeleton';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import useCurrencyRate from '../../../Hooks/useCurrencyRate';

const JournalVoucherRow = ({
  row,
  length = 3,
  index,
  isDisabled,
  updateField,
  currencyOptions,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect,
  getAccountsByTypeOptions,
  newlyCreatedAccount,
  setShowAddLedgerModal,
  handleDeleteRow,
  date,
}) => {
  const [showRowRateError, setShowRowRateError] = useState(false);

  const { data: currencyRate, isLoading: isLoadingCurrencyRate } =
    useCurrencyRate(row.currency_id, date);

  useEffect(() => {
    if (currencyRate?.rate) {
      if (row.fc_amount) {
        updateField(
          row.id,
          'lc_amount',
          parseFloat(row.fc_amount) * parseFloat(currencyRate.rate)
        );
      }
      updateField(row.id, 'rate', currencyRate.rate);
    } else if (currencyRate) {
      setCurrencyToSelect(row.currency_id);
      setShowMissingCurrencyRateModal(true);
      updateField(row.id, 'lc_amount', 0);
      updateField(row.id, 'rate', 0);
      updateField(row.id, 'currency_id', null);
    }
  }, [currencyRate?.rate]);

  useEffect(() => {
    if (row.rate && row.fc_amount) {
      updateField(
        row.id,
        'lc_amount',
        parseFloat(row.fc_amount) * parseFloat(row.rate)
      );
    }
  }, [row.rate]);

  return (
    <tr>
      <td>{index + 1}</td>
      <td>
        <SearchableSelect
          isDisabled={isDisabled}
          options={[
            { label: 'PL', value: 'party' },
            { label: 'GL', value: 'general' },
            { label: 'WIC', value: 'walkin' },
          ]}
          placeholder="Ledger"
          value={row.ledger}
          onChange={(selected) => {
            updateField(row.id, 'ledger', selected.value);
          }}
          borderRadius={10}
        />
      </td>
      <td>
        <SearchableSelect
          isDisabled={isDisabled}
          options={getAccountsByTypeOptions(row.ledger)}
          placeholder="Account"
          value={row.account_id}
          onChange={(selected) => {
            if (selected.label?.toLowerCase()?.startsWith('add new')) {
              setShowAddLedgerModal(selected.label?.toLowerCase());
            } else {
              updateField(row.id, 'account_id', selected.value);
            }
          }}
          borderRadius={10}
          minWidth={240}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.narration}
          placeholder="Enter Narration"
          disabled={isDisabled}
          onChange={(e) => updateField(row.id, 'narration', e.target.value)}
          borderRadius={10}
          style={{ minWidth: 300 }}
        />
      </td>
      <td>
        <SearchableSelect
          isDisabled={isDisabled}
          options={currencyOptions}
          placeholder=""
          value={row.currency_id}
          onChange={(selected) => {
            updateField(row.id, 'currency_id', selected.value);
          }}
          borderRadius={10}
          minWidth={100}
        />
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.fc_amount}
          placeholder="Enter FC Amount"
          disabled={isDisabled}
          onChange={(e) => {
            if (row.rate) {
              if (e.target.value === '') {
                updateField(row.id, 'lc_amount', 0);
              } else {
                updateField(
                  row.id,
                  'lc_amount',
                  parseFloat(e.target.value) * parseFloat(row.rate)
                );
              }
            }
            updateField(row.id, 'fc_amount', e.target.value);
          }}
          borderRadius={10}
        />
      </td>
      <td>
        {isLoadingCurrencyRate ? (
          <Skeleton duration={1} width={'100%'} baseColor="#ddd" height={16} />
        ) : (
          <CustomInput
            type={'text'}
            value={row.rate}
            placeholder="Rate"
            inputClass={showRowRateError ? 'text-danger' : ''}
            disabled={isDisabled}
            error={
              showRowRateError && currencyRate?.min_range
                ? `Range: ${currencyRate?.min_range} - ${currencyRate?.max_range}`
                : ''
            }
            onChange={(e) => {
              const newRate = parseFloat(e.target.value);
              const isError =
                newRate < currencyRate?.min_range ||
                newRate > currencyRate?.max_range;
              setShowRowRateError(isError);
              updateField(row.id, 'error', isError);
              updateField(row.id, 'rate', e.target.value);
            }}
            borderRadius={10}
          />
        )}
      </td>
      <td>
        <CustomInput
          type={'text'}
          value={row.lc_amount}
          placeholder="LC Amount"
          disabled={true}
          onChange={(e) => updateField(row.id, 'lc_amount', e.target.value)}
          borderRadius={10}
        />
      </td>
      <td>
        <SearchableSelect
          isDisabled={isDisabled}
          options={[
            {
              label: 'Debit',
              value: 'Debit',
            },
            {
              label: 'Credit',
              value: 'Credit',
            },
          ]}
          value={row.sign}
          onChange={(selected) => updateField(row.id, 'sign', selected.value)}
          borderRadius={10}
          minWidth={113}
        />
      </td>
      <td>
        <TableActionDropDown
          actions={[
            {
              name: 'Delete',
              icon: HiOutlineTrash,
              onClick: () => handleDeleteRow(row.id),
              className: 'delete',
              disabled: isDisabled ? isDisabled : length <= 2 ? true : false,
            },
          ]}
        />
      </td>
    </tr>
  );
};

export default JournalVoucherRow;
