import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiOutlineEye } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../Components/StatusChip/StatusChip';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getPDCProcessReceivables } from '../../../Services/Process/PDCProcesses';
import { statusFiltersConfig } from '../../../Utils/Constants/TableFilter';
import { pdcProcessHeaders } from '../../../Utils/Constants/TableHeaders';
import { showErrorToast } from '../../../Utils/Utils';

const ReceivableTable = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  const navigate = useNavigate();
  const {
    data: { data: receivablesData = [] } = {},
    isLoading: isLoadingReceivables,
    isError: isErrorReceivables,
    error: receivablesError,
  } = useFetchTableData(
    'PDCProcessesReceivablesListing',
    filters,
    updatePagination,
    getPDCProcessReceivables
  );

  if (receivablesError) {
    showErrorToast(receivablesError, 'error');
  }
  return (
    <Row>
      <Col xs={12}>
        <CustomTable
          filters={filters}
          setFilters={setFilters}
          headers={pdcProcessHeaders}
          pagination={pagination}
          isLoading={isLoadingReceivables}
          selectOptions={[
            {
              title: 'Bank',
              options: statusFiltersConfig,
            },
            {
              title: 'Ledger',
              options: statusFiltersConfig,
            },
            {
              title: 'Account',
              options: statusFiltersConfig,
            },
            {
              title: 'Cheque No.',
              options: statusFiltersConfig,
            },
          ]}
          additionalFilters={[
            { title: 'Posting Date', type: 'date' },
            { title: 'Due Date', type: 'date' },
          ]}
        >
          {(receivablesData?.length || isErrorReceivables) && (
            <tbody>
              {isErrorReceivables && (
                <tr>
                  <td colSpan={pdcProcessHeaders?.length}>
                    <p className="text-danger mb-0">
                      Unable to fetch data at this time
                    </p>
                  </td>
                </tr>
              )}
              {receivablesData?.map((item, index) => (
                <tr key={item.id}>
                  <td>{item.cheque_no}</td>
                  <td>{item.due_date}</td>
                  <td>{item.posting_date}</td>
                  <td>{item.fcy}</td>
                  <td>{item.fc_amount}</td>
                  <td>{item.drawn_on}</td>
                  <td>{item.title_of_account}</td>
                  <td>{item.narration}</td>
                  <td>
                    <StatusChip status={item.status} />
                  </td>
                  <td>
                    <TableActionDropDown
                      actions={[
                        {
                          name: 'View',
                          icon: HiOutlineEye,
                          onClick: () =>
                            navigate(
                              `${
                                item.id
                              }/receivable/${item.status.toLowerCase()}`,
                              {
                                state: { cheque: item },
                              }
                            ),
                          className: 'view',
                        },
                      ]}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          )}
        </CustomTable>
      </Col>
    </Row>
  );
};

export default withFilters(ReceivableTable);
