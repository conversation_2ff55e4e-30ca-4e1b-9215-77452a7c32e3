import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import withModal from '../../../HOC/withModal';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getRateReValuationListing } from '../../../Services/Process/ProfitAndLossPosting';
import { statusFiltersConfig } from '../../../Utils/Constants/TableFilter';
import { rateRevaluationHeaders } from '../../../Utils/Constants/TableHeaders';
import { showErrorToast } from '../../../Utils/Utils';

const RateReValuation = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
  showModal,
}) => {
  const {
    data: { data: rateReValuationData = [] } = {},
    isLoading: isLoadingRateReValuation,
    isError: isErrorRateReValuation,
    error: rateReValuationError,
  } = useFetchTableData(
    'rateReValuationListing',
    filters,
    updatePagination,
    getRateReValuationListing
  );

  const handlePostClick = () => {
    console.log('post clicked');
    showModal(
      'Rate Valuation',
      'Rate Valuation has been posted using JV# 17600',
      null,
      'success'
    );
  };

  if (rateReValuationError) {
    showErrorToast(rateReValuationError, 'error');
  }

  return (
    <>
      <div className="d-flex justify-content-between flex-wrap gap-3 mb-4">
        <h2 className="screen-title flex-shrink-0 mb-0">Rate Re-Valuation</h2>
        <div className="d-flex flex-column gap-3 ms-auto">
          <div className="d-flex flex-wrap justify-content-end gap-3">
            <CustomButton text={'Print'} onClick={() => alert('Print...')} />
            <CustomButton text={'Post'} onClick={handlePostClick} />
            <CustomButton
              text={'Generate'}
              onClick={() => alert('Generate...')}
            />
          </div>
          <div className="d-flex justify-content-end">
            <CustomButton
              text={'Recalculate Closing Rates'}
              variant={'secondaryButton'}
              onClick={() => alert('Recalculate Closing Rates...')}
            />
          </div>
        </div>
      </div>

      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={rateRevaluationHeaders}
            pagination={pagination}
            isLoading={isLoadingRateReValuation}
            selectOptions={[
              {
                title: 'Account',
                options: statusFiltersConfig,
              },
            ]}
            additionalFilters={[{ title: 'Date Range', type: 'date' }]}
          >
            {(rateReValuationData.length || isErrorRateReValuation) && (
              <tbody>
                {isErrorRateReValuation && (
                  <tr>
                    <td colSpan={rateRevaluationHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {rateReValuationData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item?.group}</td>
                    <td>{item?.currency}</td>
                    <td>{item?.fc_balance}</td>
                    <td>{item?.valuation_rate}</td>
                    <td>{item?.value_in_dhs}</td>
                    <td>{item?.gain_loss}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </>
  );
};

export default withModal(withFilters(RateReValuation));
