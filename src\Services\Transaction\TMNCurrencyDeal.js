import axiosInstance from '../../Config/axiosConfig';
import { buildFormData } from '../../Utils/Utils';

// /tmn-currency-deal?date=2025-07-01&search=8&type=buy
// GET TMN CURRENCY DEAL LISTING AND DETAILS
export const getTMNCurrencyDealListing = async (params) => {
  try {
    const { data } = await axiosInstance.get(`/user-api/tmn-currency-deal`, {
      params,
    });
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// CREATE TMN CURRENCY DEAL
export const createTMNCurrencyDeal = async (formData) => {
  try {
    const payload = new FormData();
    buildFormData(payload, formData);
    const { data } = await axiosInstance.post(
      `/user-api/tmn-currency-deal`,
      payload
    );
    const { message, status, detail } = data;
    return { message, status, detail };
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

// DELETE TMN CURRENCY DEAL
export const deleteTMNCurrencyDeal = async (id, type) => {
  try {
    const { data } = await axiosInstance.delete(
      `/user-api/tmn-currency-deal/${id}/${type}`
    );
    const { message, status, detail } = data;
    return { message, status, detail };
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
// GET Purposes
export const getPurposes = async () => {
  try {
    const { data } = await axiosInstance.get(
      `/user-api/tmn-currency-deal/purpose`
    );
    return data.detail; // Assume this returns success obj
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
