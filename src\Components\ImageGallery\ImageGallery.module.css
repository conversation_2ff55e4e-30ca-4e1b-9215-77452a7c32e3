.thumbnail {
  position: relative;
  width: 100px;
  height: auto;
  cursor: pointer;
  transition: transform 0.3s;
}
.thumbnail img {
  z-index: 0;
  display: block;
}
.thumbnail:hover {
  transform: scale(1.05);
}

.thumbnail::after {
  z-index: 9;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(146, 31, 31, 0.877);
  opacity: 0.4;
  transition: opacity 0.3s;
}
.noScale:hover {
  transform: none;
}

.thumbnail:hover::after {
  opacity: 1;
}

.modal {
  position: fixed;
  z-index: 9;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modalContent {
  position: relative;
  max-width: 90dvw;
  max-height: 90dvh;
  display: flex;
  align-items: center;
}

.close {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #aaa;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  border: 1px solid rgb(45, 66, 75);
}

.close:hover,
.close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.modalImage {
  width: 100%;
  max-width: 100%;
  max-height: 90dvh;
  display: block;
  margin: 0 auto;
}

.previous,
.next {
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  padding: 10px;
  font-size: 20px;
  cursor: pointer;
}

.previous {
  position: absolute;
  left: 10px;
}

.next {
  position: absolute;
  right: 10px;
}

.previous:hover,
.next:hover {
  background-color: rgba(0, 0, 0, 0.8);
}
@media screen and (max-width: 575px) {
  .close {
    top: 8px;
    right: 8px;
    font-size: 18px;
    width: 26px;
    height: 26px;
  }
  .previous,
  .next {
    padding: 6px;
    font-size: 16px;
  }
  .previous {
    left: 0px;
  }

  .next {
    right: 0px;
  }
}
