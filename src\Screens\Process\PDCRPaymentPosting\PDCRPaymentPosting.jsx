import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../Components/StatusChip/StatusChip';
import withFilters from '../../../HOC/withFilters ';
import { useFetchTableData } from '../../../Hooks/useTable';
import { getPDCRPaymentPosting } from '../../../Services/Process/PDCRPaymentPosting';
import { statusFiltersConfig } from '../../../Utils/Constants/TableFilter';
import { pdcrPaymentPostingHeaders } from '../../../Utils/Constants/TableHeaders';
import { formatDate, showErrorToast } from '../../../Utils/Utils';

const PDCRPaymentPosting = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  const {
    data: { data: pdcrPaymentPostingData = [] } = {},
    isLoading: isLoadingPdcrPaymentPosting,
    isError: isErrorPdcrPaymentPosting,
    error: pdcrPaymentPostingError,
  } = useFetchTableData(
    'pdcrPaymentPostingListing',
    filters,
    updatePagination,
    getPDCRPaymentPosting
  );

  if (pdcrPaymentPostingError) {
    showErrorToast(pdcrPaymentPostingError, 'error');
  }
  return (
    <>
      <div className="d-flex justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">PDCR Payment Posting</h2>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={pdcrPaymentPostingHeaders}
            pagination={pagination}
            isLoading={isLoadingPdcrPaymentPosting}
            selectOptions={[
              {
                title: 'Cheque Status',
                options: statusFiltersConfig,
              },
              {
                title: 'Received From',
                options: statusFiltersConfig,
              },
              {
                title: 'Issued To',
                options: statusFiltersConfig,
              },
            ]}
            additionalFilters={[
              { title: 'Posting Date', type: 'date' },
              { title: 'Due Date', type: 'date' },
            ]}
          >
            {(pdcrPaymentPostingData.length || isErrorPdcrPaymentPosting) && (
              <tbody>
                {isErrorPdcrPaymentPosting && (
                  <tr>
                    <td colSpan={pdcrPaymentPostingHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {pdcrPaymentPostingData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item?.cheque_no}</td>
                    <td>{formatDate(item?.dated)}</td>
                    <td>{item?.fcy}</td>
                    <td>{item?.fc_amount}</td>
                    <td>{item?.received_from}</td>
                    <td>{item?.issued_to}</td>
                    <td>
                      <StatusChip status={item.status} />
                    </td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </>
  );
};

export default withFilters(PDCRPaymentPosting);
