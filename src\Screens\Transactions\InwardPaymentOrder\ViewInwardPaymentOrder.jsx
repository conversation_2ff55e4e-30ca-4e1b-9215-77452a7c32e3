import React, { useState } from 'react';
import { HiOutlineTrash } from 'react-icons/hi2';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA,
  MOCK_SUMMARY_DATA,
  supportLogsData
} from '../../../Mocks/MockData';
import { inwardPaymentOrderNewHeaders, SUMMARY_TABLE_HEADERS } from '../../../Utils/Constants/TableHeaders';

const ViewInwardPaymentOrder = ({
  setPageState,
  attachmentsModal,
  setAttachmentsModal,
}) => {
  const [tableData, setTableData] = useState(MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA?.tableData);
  const [deleteModal, setDeleteModal] = useState(false);

  return (
    <>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7 mb-4">
            <div className="row">
              <div className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">Debit Account</p>
                <p className="detail-text wrapText mb-0">
                  {`${MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.debitAccount.party} ${MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.debitAccount.label}`}
                </p>
              </div>
              <div className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">Office</p>
                <p className="detail-text wrapText mb-0">
                  {MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.office}
                </p>
              </div>
              <div className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">VAT Type</p>
                <p className="detail-text wrapText mb-0">
                  {MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.vatType}
                </p>
              </div>
              <div className="col-12 col-sm-6 mb-4">
                <p className="detail-title detail-label-color mb-1">VAT Terms</p>
                <p className="detail-text wrapText mb-0">
                  {MOCK_INWARD_PAYMENT_ORDER_VIEW_DATA.vatTerms}
                </p>
              </div>
            </div>
          </div>
        </div>
        <CustomTable
          displayCard={false}
          headers={inwardPaymentOrderNewHeaders}
          isPaginated={false}
          hideSearch
          hideItemsPerPage
        >
          <tbody>
            {tableData?.map((row, index) => (
              <tr key={index}>
                <td>{index + 1}</td>
                <td>{row.refNo}</td>
                <td>{row.payType}</td>
                <td>{row.beneficiary}</td>
                <td>{row.sender}</td>
                <td>{row.idNumber}</td>
                <td>{row.contactNo}</td>
                <td>{row.currency}</td>
                <td>{row.fcAmount}</td>
                <td>{row.commission}</td>
                <td>{row.payDate}</td>
                <td>{row.bankName}</td>
                <td>{row.bankAccount}</td>
                <td>{row.narration}</td>
                <td>
                  <TableActionDropDown
                    actions={[
                      {
                        name: 'Delete',
                        icon: HiOutlineTrash,
                        // onClick: () => handleDeleteRow(row.id),
                        className: 'delete',
                      },
                    ]}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </CustomTable>
        <div className="mt-4">
          <CustomTable
            displayCard={false}
            headers={SUMMARY_TABLE_HEADERS}
            data={MOCK_SUMMARY_DATA}
            isPaginated={false}
            hideSearch
            hideItemsPerPage
          >
            <tbody>
              {MOCK_SUMMARY_DATA.map((row, index) => (
                <tr key={index}>
                  <td>{row.currency}</td>
                  <td>{row.total}</td>
                  <td>{row.commission}</td>
                  <td>{row.vatAmount}</td>
                  <td>{row.netTotal}</td>
                </tr>
              ))}
            </tbody>
          </CustomTable>
        </div>
      </div>
      <VoucherNavigationBar
        actionButtons={[
          {
            text: 'Edit',
            onClick: () => {
              console.log('edit');
              setPageState('edit');
            },
          },
          {
            text: 'Delete',
            onClick: () => {
              setDeleteModal(true);
            },
            variant: 'secondaryButton',
          },
          {
            text: 'Print',
            onClick: () => {
              console.log('print');
            },
            variant: 'secondaryButton',
          },
        ]}
        onAttachmentClick={() => setAttachmentsModal(true)}
        lastVoucherHeading="Last DBN Number"
        lastVoucherNumber={'05'}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={attachmentsModal}
        close={() => setAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          item={supportLogsData[0]}
          closeUploader={() => setAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Delete Suspense Voucher Modal */}
      <CustomModal
        show={deleteModal}
        close={() => {
          setDeleteModal(false); // Close the modal on cancel
        }}
        action={() => {
          console.log('delete');
          setDeleteModal(false);
        }}
        title="Delete"
        description="Are you sure you want to delete this record?"
        disableClick={false} // Disable modal actions while loading
      />
    </>
  );
};

export default ViewInwardPaymentOrder;
