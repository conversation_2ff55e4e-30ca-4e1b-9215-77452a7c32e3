import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import {
  FaChevronLeft,
  FaChevronRight,
  FaMagnifyingGlass,
  FaPaperclip,
} from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import BackButton from '../../../Components/BackButton';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { newTTRConfirmationHeaders } from '../../../Utils/Constants/TableHeaders';

const generateInitialRows = (count) => {
  const rows = {};
  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      doc_type: '12',
      doc_no: '111',
      narration: 'Lorem ipsum',
      tmn_amount: '100000',
    };
  });
  return rows;
};

const INITIAL_STATE = generateInitialRows(4);

const TTRConfirmationRow = ({ row }) => {
  return (
    <tr>
      <td>{row.doc_type}</td>
      <td>{row.doc_no}</td>
      <td>{row.narration}</td>
      <td>{row.tmn_amount}</td>
    </tr>
  );
};

const ViewTTRRegisterConfirmation = () => {
  usePageTitle('View TTR Confirmation');

  const navigate = useNavigate();
  const [isDisabled, setIsDisabled] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [debitAccount, setDebitAccount] = useState('Party Abc');
  const [creditAccount, setCreditAccount] = useState('Party Abc');
  // const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [rows, setRows] = useState(INITIAL_STATE);
  const [unConfirmed, setUnConfirmed] = useState(0);
  const [totalConfirmed, setTotalConfirmed] = useState(0);
  const [tmnBalance, setTmnBalance] = useState(0);
  const [deleteModal, setDeleteModal] = useState(false);

  return (
    <>
      <section className="position-relative">
        <div className="d-flex gap-3 justify-content-between flex-wrap mb-4">
          <div className="d-flex flex-column gap-2">
            <BackButton />
            <h2 className="screen-title mb-0">View TTR Confirmation</h2>
          </div>
          <div className="d-flex gap-2">
            {isDisabled ? (
              <CustomButton text={'New'} onClick={() => setIsDisabled(false)} />
            ) : null}
          </div>
        </div>
        <Row>
          <Col xs={12}>
            <CustomInput
              style={{ width: '180px' }}
              type="text"
              placeholder="Search"
              error={false}
              showBorders={false}
              borderRadius={10}
              name="search"
              rightIcon={FaMagnifyingGlass}
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
              }}
              onButtonClick={() => {
                console.log('search for:', searchTerm);
                navigate(
                  `/transactions/ttr-register/confirmation/${searchTerm}/view`
                );
              }}
            />
            <div className="d-flex align-items-start justify-content-between flex-wrap-reverse">
              <div className="d-flex gap-3 align-items-end">
                <span className="d-flex gap-1">
                  <p className="mb-0">Debit Account: </p>
                  <p className="fw-medium mb-0">{debitAccount}</p>
                </span>
                <span className="d-flex gap-1">
                  <p className="mb-0">Credit Account: </p>
                  <p className="fw-medium mb-0">{creditAccount}</p>
                </span>
              </div>
              <div>
                <span className="d-flex flex-column gap-1">
                  <p className="fw-medium mb-0">Date</p>
                  {date}
                </span>
              </div>
            </div>

            <CustomTable
              headers={newTTRConfirmationHeaders}
              isPaginated={false}
              className={'inputTable noActions'}
              hideSearch
              hideItemsPerPage
            >
              <tbody>
                {Object.values(rows).map((row) => (
                  <TTRConfirmationRow key={row.id} row={row} />
                ))}
              </tbody>
            </CustomTable>
            <div className="d-flex justify-content-end gap-3 mt-45 mb-5">
              <div className="d-flex flex-column gap-2 mt-1">
                <CustomInput
                  name="unConfirmed"
                  label={'Un-Confirmed'}
                  labelClass={'fw-medium'}
                  type="number"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={unConfirmed}
                  readOnly
                />
                <CustomInput
                  name="totalConfirmed"
                  label={'Total Confirmed'}
                  labelClass={'fw-medium'}
                  type="number"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={totalConfirmed}
                  readOnly
                />
                <CustomInput
                  name="tmnBalance"
                  label={'TMN Balance'}
                  labelClass={'fw-medium'}
                  type="number"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={tmnBalance}
                  readOnly
                />
              </div>
            </div>
          </Col>
        </Row>
        <div
          style={{ bottom: 20, minHeight: 77 }}
          className="d-card w-100 position-sticky mb-3 p-3 d-flex justify-content-between align-items-center"
        >
          {/* Button */}
          <div className="d-flex flex-column justify-content-center gap-4 ">
            {!isDisabled ? (
              <div className="d-flex gap-3 flex-wrap">
                <CustomButton
                  text={'Edit'}
                  onClick={() => {
                    navigate(`/transactions/ttr-register/confirmation/2/edit`);
                  }}
                />
                <CustomButton
                  text={'Delete'}
                  onClick={() => {
                    setDeleteModal(true);
                  }}
                />
                <CustomButton
                  variant={'secondaryButton'}
                  text={'Print'}
                  onClick={() => {
                    console.log('print');
                  }}
                />
              </div>
            ) : null}
          </div>
          {/* navigation */}
          <div className="d-flex gap-2 voucher-navigation-wrapper position-absolute start-50 translate-middle-x">
            <FaChevronLeft size={24} />
            <FaChevronRight size={24} />
            <FaPaperclip
              size={24}
              onClick={() => setUploadAttachmentsModal(true)}
            />
          </div>
          {/* Print */}
          <div className="d-flex align-items-center gap-3 ms-auto">
            <p style={{ fontWeight: 500 }} className=" m-0">
              Last FSN Number: <span className="fw-medium">05</span>
            </p>
            <CustomCheckbox
              style={{ border: 'none', margin: 0 }}
              label={'Print'}
            />
          </div>
        </div>
      </section>

      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachment}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
      <CustomModal
        show={deleteModal}
        close={() => {
          setDeleteModal(false);
        }}
        // disableClick={deleteMutation.isPending}
        action={() => {
          console.log('delete');
          navigate(`/transactions/ttr-register/confirmation/new`);
        }}
        title="Delete?"
        description="Are you sure you want to delete this TTR Confirmation?"
      />
    </>
  );
};

export default ViewTTRRegisterConfirmation;
