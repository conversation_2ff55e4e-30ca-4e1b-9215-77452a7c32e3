import { Form, Formik } from 'formik';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../../Components/CustomButton';
import CustomInput from '../../../../Components/CustomInput';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';

const GenerateJournalReport = () => {
  const navigate = useNavigate();
  const handleSubmit = (values) => {
    console.log('Form Values:', values);
    navigate('generated');
  };

  return (
    <section>
      <div className="d-flex gap-3 justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">Journal Report</h2>
      </div>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <Formik
              initialValues={{
                transaction_type: '',
                transaction_no_from: '',
                transaction_no_to: '',
                ledger: '',
                account: '',
                fcy: '',
                fcy_amount_from: '',
                fcy_amount_to: '',
                transaction_date_from: '',
                transaction_date_to: '',
                entry_date_from: '',
                entry_date_to: '',
                user_id: '',
                attachment: '',
                mark_type: '',
              }}
              onSubmit={handleSubmit}
            >
              {({ values, handleChange, handleBlur, setFieldValue }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        label="Transaction Type"
                        name="transaction_type"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.transaction_type}
                        onChange={(v) =>
                          setFieldValue('transaction_type', v.value)
                        }
                        placeholder="Select Transaction Type"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="Transaction No. From"
                        name="transaction_no_from"
                        type="text"
                        placeholder="From"
                        value={values.transaction_no_from}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="Transaction No. To"
                        name="transaction_no_to"
                        type="text"
                        placeholder="To"
                        value={values.transaction_no_to}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        label="Ledger"
                        name="ledger"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.ledger}
                        onChange={(v) => setFieldValue('ledger', v.value)}
                        placeholder="Select Ledger"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        label="Account"
                        name="account"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.account}
                        onChange={(v) => setFieldValue('account', v.value)}
                        placeholder="Select Account"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        label="FCy"
                        name="fcy"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.fcy}
                        onChange={(v) => setFieldValue('fcy', v.value)}
                        placeholder="Select FCy"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="FCy Amount From"
                        name="fcy_amount_from"
                        type="text"
                        placeholder="From"
                        value={values.fcy_amount_from}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="FCy Amount To"
                        name="fcy_amount_to"
                        type="text"
                        placeholder="To"
                        value={values.fcy_amount_to}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>

                    <div className="col-12 col-sm-6">
                      <div className="row flex-wrap">
                        <label htmlFor="transactionDateFrom">
                          Transaction Date Range
                        </label>
                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="transaction_date_from"
                            type="date"
                            value={values.transaction_date_from}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="transaction_date_to"
                            type="date"
                            value={values.transaction_date_to}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="col-12 col-sm-6">
                      <div className="row flex-wrap">
                        <label htmlFor="entry_date_from">Entry Date Range</label>
                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="entry_date_from"
                            type="date"
                            value={values.entry_date_from}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>

                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="entry_date_to"
                            type="date"
                            value={values.entry_date_to}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        label="User ID"
                        name="user_id"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.user_id}
                        onChange={(v) => setFieldValue('user_id', v.value)}
                        placeholder="Select User ID"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        label="Attachment"
                        name="attachment"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.attachment}
                        onChange={(v) => setFieldValue('attachment', v.value)}
                        placeholder="Select Attachment"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        label="Mark Type"
                        name="mark_type"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.mark_type}
                        onChange={(v) => setFieldValue('mark_type', v.value)}
                        placeholder="Select Mark Type"
                      />
                    </div>
                  </div>

                  <div className="d-flex mb-4">
                    <CustomButton type="submit" text="Generate" />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GenerateJournalReport;
