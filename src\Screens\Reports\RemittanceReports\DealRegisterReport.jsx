import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import {
  dealRegisterReportData
} from '../../../Mocks/MockData';
import {
  dealRegisterReportHeaders
} from '../../../Utils/Constants/TableHeaders';

const DealRegisterReport = ({ filters, setFilters, pagination }) => {
  const tableData = dealRegisterReportData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">Deal Register Report</h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={dealRegisterReportHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'Ledger', options: [{ value: 'All', label: 'All' }] },
              { title: 'FCy', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'Showing',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'Sort By',
                options: [{ value: 'All', label: 'All' }],
              },
            ]}
            dateFilters={[{ title: 'Date Range' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={dealRegisterReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.account}</td>
                    <td>{item.buy_fcy}</td>
                    <td>{item.buy_fc_amount}</td>
                    <td>{item.sell_fcy}</td>
                    <td>{item.sell_fc_amount}</td>
                    <td>{item.rate}</td>
                    <td>{item.tran_no}</td>
                    <td>{item.value_date}</td>
                    <td>{item.user_id}</td>
                    <td>{item.date}</td>
                    <td>{item.time}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(DealRegisterReport);
