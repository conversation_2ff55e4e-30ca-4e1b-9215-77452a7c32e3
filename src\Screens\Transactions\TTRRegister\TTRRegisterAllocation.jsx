import React, { useState } from 'react';
import { FaCircleXmark } from 'react-icons/fa6';
import {
  HiOutlinePencilSquare,
  HiOutlineTrash,
  HiPrinter,
} from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import { MOCK_TTR_REGISTER_ALLOCATION_DATA } from '../../../Mocks/MockData';
import { ttrRegisterAllocationHeaders } from '../../../Utils/Constants/TableHeaders';
import CustomModal from '../../../Components/CustomModal';
import CustomInput from '../../../Components/CustomInput';
import { isNumber } from 'lodash';
import CustomButton from '../../../Components/CustomButton';

const TTRRegisterAllocation = ({ filters, setFilters, pagination }) => {
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [inEditMode, setInEditMode] = useState(false);
  const [tableData, setTableData] = useState(MOCK_TTR_REGISTER_ALLOCATION_DATA);
  const isLoading = false;
  const isError = false;

  return (
    <>
      <CustomTable
        filters={filters}
        setFilters={setFilters}
        headers={ttrRegisterAllocationHeaders}
        pagination={pagination}
        isLoading={isLoading}
        selectOptions={[
          {
            title: 'Party Account',
            options: [
              {
                label: 'Select Party Account',
                value: '',
              },
            ],
          },
        ]}
        renderAtEnd={
          inEditMode && (
            <div className="d-flex justify-content-end mb-4">
              <CustomInput
                type="text"
                label={'Total Allocated TMN'}
                borderRadius={10}
                value={tableData.reduce((sum, item) => {
                  return sum + parseInt(item.allocated.replace(/,/g, ''), 10);
                }, 0)}
                onChange={(e) => {}}
              />
            </div>
          )
        }
      >
        {(tableData.length || isError) && (
          <tbody>
            {isError && (
              <tr>
                <td colSpan={ttrRegisterAllocationHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {tableData?.map((item) => (
              <tr key={item.id}>
                <td>{item.date}</td>
                <td>{item.debit_party}</td>
                <td>{item.credit_party}</td>
                <td>{item.bank_name}</td>
                <td>{item.bank_account}</td>
                <td>{item.remarks}</td>
                <td>
                  {inEditMode ? (
                    <CustomInput
                      name="allocated"
                      type="number"
                      borderRadius={10}
                      style={{ maxWidth: '150px' }}
                      value={
                        parseInt(item.allocated.replace(/,/g, ''), 10) || 0
                      }
                      onChange={(e) => {
                        setTableData(
                          tableData.map((row) =>
                            row.id === item.id
                              ? { ...row, allocated: e.target.value || 0 }
                              : row
                          )
                        );
                      }}
                    />
                  ) : (
                    item.allocated
                  )}
                </td>
                <td>{item.confirmed}</td>
                <td>{item.unconfirmed}</td>
                <td>
                  <TableActionDropDown
                    actions={[
                      {
                        name: 'Edit',
                        icon: HiOutlinePencilSquare,
                        onClick: () => {
                          setInEditMode(true);
                        },
                        className: 'edit',
                      },
                      {
                        name: 'Print',
                        icon: HiPrinter,
                        onClick: () => {
                          console.log(item.id, 'print');
                        },
                        className: 'attachments',
                      },
                      {
                        name: 'Delete',
                        icon: HiOutlineTrash,
                        onClick: () => {
                          setShowDeleteModal(true);
                        },
                        className: 'delete',
                      },
                      {
                        name: 'Cancel Unallocated',
                        icon: FaCircleXmark,
                        onClick: () => {
                          console.log(item.id, 'cancel unallocated');
                        },
                        className: 'delete',
                      },
                    ]}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        )}
      </CustomTable>
      {inEditMode && (
        <div className="d-flex justify-content-start gap-2">
          <CustomButton
            text="Save"
            onClick={() => {
              console.log(tableData);
              setInEditMode(false);
            }}
          />
          <CustomButton
            text="Cancel"
            variant="secondaryButton"
            onClick={() => {
              setInEditMode(false);
            }}
          />
        </div>
      )}
      <CustomModal
        show={showDeleteModal}
        close={() => {
          setShowDeleteModal(false);
        }}
        title="Delete Allocation"
        description="Are you sure you want to delete this allocation?"
        action={() => setShowDeleteModal(false)}
      />
    </>
  );
};

export default withFilters(TTRRegisterAllocation);
