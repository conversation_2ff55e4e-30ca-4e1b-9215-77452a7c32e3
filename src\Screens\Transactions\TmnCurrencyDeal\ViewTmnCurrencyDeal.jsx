import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomModal from '../../../Components/CustomModal';
import { showToast } from '../../../Components/Toast/Toast';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  deleteTMNCurrencyDeal,
  getTMNCurrencyDealListing,
} from '../../../Services/Transaction/TMNCurrencyDeal';
import {
  formatDate,
  isNullOrEmpty,
  showErrorToast,
} from '../../../Utils/Utils';

const ViewTmnCurrencyDeal = ({
  searchTerm,
  setDate,
  setWriteTerm,
  setSearchTerm,
  setPageState,
  lastVoucherNumbers,
  searchType, // 'buy' or 'sell'
}) => {
  const queryClient = useQueryClient();
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const {
    data: { data: [tmnCurrencyDealData] = [] } = {},
    isLoading,
    isFetching,
    isError,
    error,
  } = useQuery({
    queryKey: ['tmnCurrencyDeal', searchTerm, searchType],
    queryFn: () =>
      getTMNCurrencyDealListing({ search: searchTerm, type: searchType }),
  });

  const tmnCurrencyDeal = tmnCurrencyDealData?.tmn_currency_deal;
  useEffect(() => {
    if (tmnCurrencyDealData?.voucher?.voucher_no) {
      setDate(tmnCurrencyDealData.voucher.date);
      setWriteTerm(tmnCurrencyDealData.voucher.voucher_no);
    }
  }, [tmnCurrencyDealData?.voucher?.voucher_no]);

  // Delete Mutation
  const deleteTmnCurrencyDealMutation = useMutation({
    mutationFn: (id) => deleteTMNCurrencyDeal(id, tmnCurrencyDeal?.type),
    onSuccess: () => {
      showToast('TMN Currency Deal deleted successfully!', 'success');
      queryClient.invalidateQueries(['tmnCurrencyDeal', searchTerm]);
      setShowDeleteModal(false);
      setPageState('list');
      setWriteTerm('');
      setSearchTerm('');
      // setDate(new Date().toISOString().split('T')[0]);
    },
    onError: (error) => {
      setShowDeleteModal(false);
      showErrorToast(error);
    },
  });

  if (isError) {
    showErrorToast(error);
    return (
      <div className="d-card">
        <p className="text-danger mb-0">Error fetching TMN Currency Deal</p>
      </div>
    );
  }

  if (isLoading || isFetching) {
    return (
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <div className="row mb-3">
              {Array.from({ length: 14 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!tmnCurrencyDeal) {
    return (
      <>
        <div className="d-card">
          <p className="text-danger mb-0">
            No TMN Currency Deal found for ID {searchTerm}
          </p>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7 mb-4">
            <div className="row">
              {[
                {
                  label: 'Type',
                  value: tmnCurrencyDeal?.type,
                },
                {
                  label: 'Mode',
                  value: tmnCurrencyDeal?.mode,
                },
                {
                  label: 'Account',
                  value: tmnCurrencyDeal?.account_details?.title,
                },
                {
                  label: 'Beneficiary',
                  value: tmnCurrencyDeal?.beneficiary?.name,
                },
                {
                  label: 'Bank',
                  value: tmnCurrencyDeal?.bank_name,
                },
                {
                  label: 'Bank',
                  value: tmnCurrencyDeal?.bank_account_no,
                },
                {
                  label: 'City',
                  value: tmnCurrencyDeal?.city,
                },
                {
                  label: 'Purpose',
                  value: tmnCurrencyDeal?.purpose?.description,
                },
                {
                  label: `${searchType === 'buy' ? 'Buy' : 'Sell'} FCy`,
                  value: `${tmnCurrencyDeal?.fcy?.currency_code} ${tmnCurrencyDeal?.fc_amount}`,
                },
                {
                  label: 'Rate Type',
                  value: `${tmnCurrencyDeal?.rate_type} ${tmnCurrencyDeal?.rate}`,
                },
                {
                  label: 'Ag FCy',
                  value: `${tmnCurrencyDeal?.against_f_cy?.currency_code} ${tmnCurrencyDeal?.ag_amount}`,
                },
                ...(searchType === 'buy'
                  ? [
                      {
                        label: 'Commission',
                        value: `${tmnCurrencyDeal?.fcy?.currency_code} ${tmnCurrencyDeal?.commission_amount}`,
                      },
                      {
                        label: 'VAT Terms',
                        value: `${tmnCurrencyDeal?.vats?.title} ${tmnCurrencyDeal?.vats?.percentage}`,
                      },
                      {
                        label: 'VAT',
                        value: `${tmnCurrencyDeal?.fcy?.currency_code} ${tmnCurrencyDeal?.vat_amount}`,
                      },
                    ]
                  : []),
                {
                  label: 'Total',
                  value: tmnCurrencyDeal?.total_amount,
                },
                {
                  label: 'User ID',
                  value: tmnCurrencyDeal?.creator?.user_id,
                },
                {
                  label: 'Created At',
                  value: formatDate(tmnCurrencyDeal?.created_at, 'HH:MM:SS'),
                },
              ].map((x, i) => {
                if (isNullOrEmpty(x.value)) return null;
                return (
                  <div key={i} className="col-12 col-sm-6 mb-4">
                    <p className="detail-title detail-label-color mb-1">
                      {x.label}
                    </p>
                    <p className="detail-text wrapText mb-0">{x.value}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      <VoucherNavigationBar
        searchTerm={searchTerm}
        actionButtons={[
          { text: 'Edit', onClick: () => setPageState('edit') },
          {
            text: 'Delete',
            onClick: () => setShowDeleteModal(true),
            variant: 'secondaryButton',
          },
          ...(tmnCurrencyDeal?.pdf_url
            ? [
                {
                  text: 'Print',
                  onClick: () => {
                    if (receiptVoucherData?.pdf_url) {
                      window.open(receiptVoucherData?.pdf_url, '_blank');
                    }
                  },
                  variant: 'secondaryButton',
                },
              ]
            : []),
        ]}
        loading={isLoading || isFetching}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Attachments Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          viewOnly
          items={tmnCurrencyDealData}
          closeUploader={() => setShowAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Delete Modal */}
      <CustomModal
        show={showDeleteModal}
        close={() => {
          setShowDeleteModal(false); // Close the modal on cancel
        }}
        action={() => {
          if (tmnCurrencyDeal) {
            deleteTmnCurrencyDealMutation.mutate(tmnCurrencyDeal.id);
          }
        }}
        title="Delete"
        description={`Are you sure you want to delete TMN Currency Deal ${tmnCurrencyDealData?.voucher_no}?`}
        disableClick={deleteTmnCurrencyDealMutation.isPending}
      />
    </>
  );
};

export default ViewTmnCurrencyDeal;
