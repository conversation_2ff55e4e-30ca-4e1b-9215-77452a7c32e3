import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ErrorMessage, Form, Formik } from 'formik';
import { useEffect, useRef, useState } from 'react';
import { HiOutlinePencilSquare, HiOutlineTrash } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import AddAllocationDetailsForm from '../../../Components/AddAllocationDetailsForm/AddAllocationDetailsForm.jsx';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs.jsx';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable.jsx';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown.jsx';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { getBanks, getCities, getDocTypes } from '../../../Services/General';
import { getCurrencyRatesPair } from '../../../Services/General.js';
import {
  getBenefeciariesByAccount,
  getVATType,
} from '../../../Services/Transaction/ReceiptVoucher';
import {
  createTMNCurrencyDeal,
  getPurposes,
} from '../../../Services/Transaction/TMNCurrencyDeal.js';
import useFormStore from '../../../Stores/FormStore';
import useSettingsStore from '../../../Stores/SettingsStore';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils.jsx';
import { showToast } from '../../../Components/Toast/Toast.jsx';
import { ledgerOptions } from '../../../Utils/Constants/SelectOptions.js';

const NewTmnCurrencyDeal = ({
  state,
  date,
  isDisabled = false,
  setIsDisabled,
  setPageState,
  setSearchTerm,
  getAccountsByTypeOptions,
  newlyCreatedAccount,
  newlyCreatedBeneficiary,
  setShowAddLedgerModal,
  currencyOptions,
  lastVoucherNumbers,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect,
}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const formikRef = useRef();

  // Access the form store
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'new_tmn_currency_deal'; // Unique identifier for this form

  // For getting print checkbox state from BE
  const { getPrintSettings, updatePrintSetting } = useSettingsStore();

  // State variables
  const [selectedLedgerAccount, setSelectedLedgerAccount] = useState(null);
  const [showAddAllocationModal, setShowAddAllocationModal] = useState(false);
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachments, setAddedAttachments] = useState(null);
  const [allocations, setAllocations] = useState([]);
  const [editAllocationRow, setEditAllocationRow] = useState(null);
  const [shouldShowAllocationTable, setShouldShowAllocationTable] =
    useState(false);
  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [outOfScope, setOutOfScope] = useState('');
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [hasShownMissingRateModal, setHasShownMissingRateModal] =
    useState(false);
  const [tMNCurrencyObj, setTMNCurrencyObj] = useState({});

  console.log('state', state);
  // Load saved form if returning from special Commission and FormStore has values
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);
    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);
      console.log('savedValues', savedValues);
      formikRef.current.setValues(savedValues);
      setSelectedLedgerAccount(savedValues?.account_id);
      setAllocations(savedValues?.allocations || []);
      setIsDisabled(false);

      // if (hasFormValues('special-commission')) {
      //   setAddedSpecialCommissionValues(getFormValues('special-commission'));
      // }
      // Clear lastVisitedPage so it doesn't persist beyond one use
      clearLastVisitedPage(formId);
      clearFormValues(formId);
    }
  }, []);

  // Calculate allocated amount from allocations
  useEffect(() => {
    if (formikRef.current) {
      const totalAllocated = allocations.reduce((sum, allocation) => {
        return sum + (parseFloat(allocation.amount) || 0);
      }, 0);

      formikRef.current.setFieldValue('allocated', totalAllocated.toFixed(2));

      // Also update balance amount (telex_transfer_amount - allocated)
      const telexTransferAmount =
        parseFloat(formikRef.current.values.telex_transfer_amount) || 0;
      const balanceAmount = telexTransferAmount - totalAllocated;
      formikRef.current.setFieldValue(
        'balance_amount',
        balanceAmount.toFixed(2)
      );
    }
  }, [allocations]);

  const createTMNCurrencyDealMutation = useMutation({
    mutationFn: createTMNCurrencyDeal,
    onSuccess: (data) => {
      showToast('TMN Currency Deal Created!', 'success');
      if (getPrintSettings('tmn_currency_deal')) {
        window.open(data.detail?.pdf_url, '_blank');
      }
      queryClient.invalidateQueries(['tmnCurrencyDealListing']);
      handleResetRows();
    },
    onError: (error) => {
      console.error('Error creating TMN Currency Deal', error);
      if (
        error.message.toLowerCase() ==
        'tmn currency deal limit reached for this branch.'
      ) {
        showModal(
          'Cannot Create',
          'You have reached the maximum number of TBN/TSN. To create new transactions you need to increase the transaction count form the Transaction Number Register.',
          null,
          'error'
        );
      } else {
        showErrorToast(error);
      }
    },
  });

  // Get VAT Type //
  const {
    data: vatType,
    isLoading: isLoadingVatType,
    isError: isErrorVatType,
    error: errorVatType,
  } = useQuery({
    queryKey: ['vatType'],
    queryFn: getVATType,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const vatData = {
    vatType,
    isLoadingVatType,
    isErrorVatType,
    errorVatType,
  };

  // Get Purposes dropdown options
  const {
    data: purposes,
    isLoading: isLoadingPurposes,
    isError: isErrorPurposes,
    error: errorPurposes,
  } = useQuery({
    queryKey: ['tmn-currency-deal-purposes'],
    queryFn: getPurposes,
  });
  const getPurposeOptions = () => {
    if (isLoadingPurposes) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (isErrorPurposes) {
      console.error('Unable to fetch purposes', errorPurposes);
      return [{ label: 'Unable to fetch purposes', value: null }];
    }

    return purposes?.map((x) => ({
      value: x?.id,
      label: x?.description,
    }));
  };

  // Get Benefeciaries from selected Ledger+Account
  const {
    data: beneficiaryAccounts,
    isLoading: isLoadingBeneficiary,
    isError: isErrorBeneficiary,
    error: errorBeneficiary,
  } = useQuery({
    queryKey: ['beneficiaries', selectedLedgerAccount],
    queryFn: () => getBenefeciariesByAccount(selectedLedgerAccount),
    enabled: !!selectedLedgerAccount,
  });

  // Make options array from the benfeciary queries call
  const getBeneficiaryOptions = (account_id) => {
    if (!account_id) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const data = beneficiaryAccounts;
    const loading = isLoadingBeneficiary;
    const error = isErrorBeneficiary;
    const errorMessage = errorBeneficiary;

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch beneficiaries', errorMessage);
      return [{ label: 'Unable to fetch beneficiaries', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
        bank_account_number: x?.bank_account_number || '',
        bank_name: x?.bank_name || '',
        city: x?.city || '',
        purpose: x?.purpose?.id || '',
      })) || [];

    options.push({
      label: `Add New Beneficiary`,
      value: null,
    });

    return options;
  };

  // Banks
  const {
    data: banks,
    isLoading: isLoadingBanks,
    isError: isErrorBanks,
    error: errorBanks,
  } = useQuery({
    queryKey: ['banks'],
    queryFn: getBanks,
  });
  const getBankOptions = () => {
    if (isLoadingBanks) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorBanks) {
      console.error('Unable to fetch banks', errorBanks);
      return [{ label: 'Unable to fetch banks', value: null }];
    }
    return banks?.map((x) => ({
      value: x?.id,
      label: x?.account_name,
    }));
  };

  // Document Types
  const {
    data: docTypes,
    isLoading: isLoadingDocTypes,
    isError: isErrorDocTypes,
    error: errorDocTypes,
  } = useQuery({
    queryKey: ['doc-types'],
    queryFn: getDocTypes,
  });
  const getDocTypeOptions = () => {
    if (isLoadingDocTypes) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorDocTypes) {
      console.error('Unable to fetch document types', errorDocTypes);
      return [{ label: 'Unable to fetch document types', value: null }];
    }
    return docTypes?.map((x) => ({
      value: x?.id,
      label: x?.description,
    }));
  };

  // Cities
  const {
    data: cities,
    isLoading: isLoadingCities,
    isError: isErrorCities,
    error: errorCities,
  } = useQuery({
    queryKey: ['cities'],
    queryFn: getCities,
  });
  const getCityOptions = () => {
    if (isLoadingCities) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorCities) {
      console.error('Unable to fetch cities', errorCities);
      return [{ label: 'Unable to fetch cities', value: null }];
    }
    return cities?.map((x) => ({
      value: x?.id,
      label: x?.description,
    }));
  };

  const getVATTermsOptions = () => {
    if (vatData.isLoadingVatType)
      return [
        {
          label: 'Loading...',
          value: '',
        },
      ];
    if (vatData.isErrorVatType) {
      console.error('Unable to fetch VAT Terms', vatData.errorMessage);
      return [{ label: 'Unable to fetch VAT Terms', value: null }];
    }
    return vatData?.vatType?.vats?.map((item) => ({
      id: item.id,
      label: `${item.title}${
        !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
      }`,
      // Change this value to item.id from item.percentage in other places as well
      value: item.id,
      percentage: item.percentage,
    }));
  };

  // Fetch dual currency rate for the selected Currency
  const { data: currencyRatesPair, isLoading: isLoadingCurrencyRatesPair } =
    useQuery({
      queryKey: [
        'dual-currency-rate',
        selectedCurrency, // Currency 1
        tMNCurrencyObj.value, // Currency 2
        date,
        formikRef.current?.values?.type, // Deal type (buy/sell)
      ],
      queryFn: () =>
        getCurrencyRatesPair(
          selectedCurrency,
          tMNCurrencyObj.value,
          date,
          formikRef.current?.values?.type
        ),
      enabled: !!selectedCurrency && !!tMNCurrencyObj?.value,
    });

  // To update Rate field and show missing rate modal if rate not present
  useEffect(() => {
    if (
      selectedCurrency &&
      !isNullOrEmpty(currencyRatesPair) &&
      !currencyRatesPair?.direct_rate &&
      !hasShownMissingRateModal
    ) {
      formikRef.current.setFieldValue('fc_currency_id', '');
      formikRef.current.setFieldValue('total_currency_id', '');
      formikRef.current.setFieldValue('vat_currency_id', '');
      formikRef.current.setFieldValue('commission_currency_id', '');
      formikRef.current.setFieldValue('rate', '');
      setCurrencyToSelect(selectedCurrency);
      setShowMissingCurrencyRateModal(true);
      setHasShownMissingRateModal(true);
    } else if (currencyRatesPair?.direct_rate) {
      formikRef.current.setFieldValue('rate', currencyRatesPair?.direct_rate);
    }
  }, [
    selectedCurrency,
    currencyRatesPair?.direct_rate,
    hasShownMissingRateModal,
  ]);

  useEffect(() => {
    if (currencyOptions.length === 1 && !currencyOptions[0].value) {
    } else {
      // If TMN not present then aler user to add currency first
      if (!currencyOptions.find((x) => x.label === 'TMN')) {
        // alert('Please add TMN currency first');
        // navigate('/master/currency-register');
      } else {
        setTMNCurrencyObj(currencyOptions.find((x) => x.label === 'TMN') || {});
      }
    }
  }, [currencyOptions]);

  const handleVatOutOfScope = (values) => {
    setOutOfScope(values.out_of_scope);
    setShowVatOutOfScopeModal(false);
  };

  // Handle form reset
  const handleResetRows = () => {
    setIsDisabled(true);
    setAllocations([]);
    setAddedAttachments(null);
    setOutOfScope('');
    setSelectedCurrency(null);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    clearFormValues(formId);
    clearFormValues('special-commission');
    // setAddedSpecialCommissionValues(null);
  };

  // Handle Allocation Submit
  const handleAddAllocation = (allocationValues) => {
    setShowAddAllocationModal(false);
    console.log('Allocation Values:', allocationValues);
    if (editAllocationRow) {
      setAllocations(
        allocations.map((x) =>
          x.id === editAllocationRow.id ? allocationValues : x
        )
      );
    } else {
      setAllocations([...allocations, allocationValues]);
    }
    setEditAllocationRow(null);
  };

  const handleNavigateToSpecialCommissionPage = () => {
    // Check if required fields are filled
    const requiredFields = [
      'account_ledger',
      'account_id',
      'ag_amount',
      'fc_currency_id',
      // 'commission_type',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    if (missingFields.length > 0) {
      // Set touched for all required fields to show errors
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      console.log('missingFields', missingFields);
      return;
    }

    // If all required fields are filled, proceed
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, {
        ...formikRef.current.values,
        allocations: allocations,
      });
      setLastVisitedPage(formId, 'special-commission');
    }

    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'tmn-currency-deal',
        values: {
          date: date,
          ledger:
            ledgerOptions.find(
              (x) => x.value === formikRef.current.values.account_ledger
            )?.label || '',
          account:
            getAccountsByTypeOptions(
              formikRef.current.values.account_ledger
            ).find((x) => x.value === formikRef.current.values.account_id)
              ?.label || '',
          amount: formikRef.current.values.ag_amount,
          currency:
            currencyOptions.find(
              (x) => x.value === formikRef.current.values.fc_currency_id
            )?.label || '',
          isEdit: false,
        },
      },
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!formikRef.current) return;

    // Validate the form
    const errors = await formikRef.current.validateForm();
    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show errors
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return; // Do not submit if there are errors
    }

    const formValues = formikRef.current.values;
    let payload = {
      date,
      ...formValues,
      ...addedAttachments,
      ...(outOfScope && {
        out_of_scope_reason: outOfScope,
      }),
    };

    // If allocations are present and mode is regular, then transform allocations and add to payload
    if (
      allocations.length > 0 &&
      formikRef.current?.values?.mode === 'regular'
    ) {
      const transformAllocations = (values, index = 0) => {
        return Object.entries(values).reduce((acc, [key, value]) => {
          if (key === 'id') return acc;
          acc[`allocations[${index}][${key}]`] = value;
          return acc;
        }, {});
      };

      payload = {
        ...payload,
        // iterate over allocations and call transformAllocations for each allocation
        allocations: allocations.map((allocation, index) =>
          transformAllocations(allocation, index)
        ),
      };
    }
    createTMNCurrencyDealMutation.mutate(payload);
  };

  useEffect(() => {
    if (formikRef.current?.values) {
      formikRef.current.values.ag_currency_id =
        currencyOptions.find((x) => x.label === 'TMN')?.value || '';
    }
  }, [currencyOptions]);

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            type: 'buy',
            mode: '',
            account_ledger: '',
            account_id: '',
            beneficiary_id: '',
            bank_name: '',
            bank_account_no: '',
            city: '',
            purpose_id: '',
            fc_currency_id: '',
            fc_amount: '',
            rate_type: 'X',
            rate: '',
            ag_currency_id: '',
            ag_amount: '',
            commission_currency_id: '',
            commission_amount: '',
            vat_terms_id: '',
            vat_terms: '',
            vat_currency_id: '',
            vat_amount: '',
            total_currency_id: '',
            total_amount: '',

            telex_transfer_amount: '',
            allocated: '',
            balance_amount: '',

            // allocations[0][ledger]
            // allocations[0][account_id]
            // allocations[0][telex_transfer_amount]
            // allocations[0][amount]
            // allocations[0][balance_amount]
            // allocations[0][document_type_id]
            // allocations[0][number]
            // allocations[0][bank_id]
            // allocations[0][code]
            // allocations[0][city_id]
            // allocations[0][description]
          }}
          validate={(values) => {
            const errors = {};

            // Required fields validation
            if (!values.account_ledger)
              errors.account_ledger = 'Ledger is required';
            if (!values.account_id) errors.account_id = 'Account is required';
            if (!values.type) errors.type = 'Deal Type is required';
            if (!values.mode) errors.mode = 'Mode is required';
            if (!values.fc_currency_id)
              errors.fc_currency_id = 'FC Currency is required';
            if (!values.fc_amount) errors.fc_amount = 'FC Amount is required';
            if (!values.ag_currency_id)
              errors.ag_currency_id = 'AG Currency is required';
            if (!values.ag_amount) errors.ag_amount = 'AG Amount is required';
            // if type is buy then commission amount is required
            if (values.type === 'buy' && !values.commission_amount)
              errors.commission_amount = 'Commission Amount is required';
            return errors;
          }}
        >
          {({
            values,
            touched,
            errors,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // Calculates VAT and Net Total correctly
            useEffect(() => {
              const fcAmount = parseFloat(values.fc_amount) || 0;
              const commission = parseFloat(values.commission_amount) || 0;
              const vatPercentage = parseFloat(values.vat_terms) || 0;

              const vatAmount = commission * (vatPercentage / 100);
              const totalAmount = fcAmount + commission + vatAmount;

              setFieldValue('vat_amount', vatAmount.toFixed(2));
              setFieldValue('total_amount', totalAmount.toFixed(2));
            }, [values.fc_amount, values.commission_amount, values.vat_terms]);

            useEffect(() => {
              const fcAmount = parseFloat(values.fc_amount) || 0;
              const rate = parseFloat(values.rate) || 0;

              if (rate === 0) return;

              const agAmount =
                fcAmount * parseFloat(currencyRatesPair?.direct_rate);

              setFieldValue('ag_amount', agAmount.toFixed(2));
              setFieldValue('telex_transfer_amount', agAmount.toFixed(2));
              setFieldValue('balance_amount', agAmount.toFixed(2));
            }, [values.fc_amount, values.rate]);

            useEffect(() => {
              setShouldShowAllocationTable(
                allocations.length > 0 &&
                  formikRef.current?.values?.mode === 'regular'
              );
            }, [allocations, formikRef.current?.values?.mode]);

            return (
              <Form>
                <div className="row">
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                    <div className="row">
                      {/* Deal Type */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'type'}
                          label={'Type'}
                          options={[
                            { label: 'Buy', value: 'buy' },
                            { label: 'Sell', value: 'sell' },
                          ]}
                          isDisabled={isDisabled}
                          placeholder={'Select Type'}
                          value={values.type}
                          onChange={(selected) => {
                            setFieldValue('type', selected.value);
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="type"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>

                      {/* Mode */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'mode'}
                          label={'Mode'}
                          options={[
                            { label: 'On A/C', value: 'on_ac' },
                            { label: 'Regular', value: 'regular' },
                          ]}
                          isDisabled={isDisabled}
                          placeholder={'Select Mode'}
                          value={values.mode}
                          onChange={(selected) => {
                            setFieldValue('mode', selected.value);
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="mode"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>

                      {/* Combined Ledger and Account Select */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Ledger"
                          type1="select"
                          type2="select"
                          name1="account_ledger"
                          name2="account_id"
                          value1={values.account_ledger}
                          value2={values.account_id || newlyCreatedAccount?.id}
                          options1={ledgerOptions}
                          options2={getAccountsByTypeOptions(
                            values.account_ledger
                          )}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Select Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('account_ledger', selected.value);
                              setFieldValue('beneficiary_id', '');
                              setFieldValue('bank_name', '');
                              setFieldValue('bank_account_no', '');
                              setFieldValue('city', '');
                              setFieldValue('purpose_id', '');
                            }
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('account_id', selected.value);
                              setSelectedLedgerAccount(selected.value);
                              setFieldValue('beneficiary_id', '');
                              setFieldValue('bank_name', '');
                              setFieldValue('bank_account_no', '');
                              setFieldValue('city', '');
                              setFieldValue('purpose_id', '');
                              //   setSpecialCommissionValues((prev) => ({
                              //     ...prev,
                              //     account_id: selected,
                              //   }));
                            }
                          }}
                        />
                        <ErrorMessage
                          name="account_id"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                      {/* Beneficiary */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'beneficiary_id'}
                          label={'Beneficiary'}
                          options={getBeneficiaryOptions(selectedLedgerAccount)}
                          isDisabled={isDisabled}
                          placeholder={'Select Beneficiary'}
                          value={
                            values.beneficiary_id || newlyCreatedBeneficiary?.id
                          }
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('beneficiary_id', selected.value);
                              setFieldValue('bank_name', selected.bank_name);
                              setFieldValue(
                                'bank_account_no',
                                selected.bank_account_number
                              );
                              setFieldValue('city', selected.city);
                              setFieldValue('purpose_id', selected.purpose);
                            }
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      {/* Bank Name */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'bank_name'}
                          label={'Bank Name'}
                          disabled={true}
                          value={values.bank_name}
                        />
                      </div>
                      {/* Bank Account No */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'bank_account_no'}
                          label={'Bank Account'}
                          disabled={true}
                          value={values.bank_account_no}
                        />
                      </div>
                      {/* City */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'city'}
                          label={'City'}
                          disabled={true}
                          value={values.city}
                        />
                      </div>

                      {/* Purpose */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'purpose_id'}
                          label={'Purpose'}
                          options={getPurposeOptions()}
                          isDisabled={isDisabled}
                          placeholder={'Select Purpose'}
                          value={values.purpose_id}
                          onChange={(selected) => {
                            setFieldValue('purpose_id', selected.value);
                          }}
                          onBlur={handleBlur}
                        />
                      </div>

                      {/* Combined FC Currency and Amount */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label={`${values.type == 'buy' ? 'Buy' : 'Sell'} FCy`}
                          type1="select"
                          type2="input"
                          name1="fc_currency_id"
                          name2="fc_amount"
                          value1={values.fc_currency_id}
                          value2={values.fc_amount}
                          options1={currencyOptions}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Currency"
                          placeholder2="Amount"
                          inputType2="number"
                          className1="fc-currency"
                          className2="fc-amount"
                          onChange1={(selected) => {
                            setSelectedCurrency(selected.value);
                            setHasShownMissingRateModal(false);
                            setFieldValue('fc_currency_id', selected.value);
                            setFieldValue('total_currency_id', selected.value);
                            setFieldValue('vat_currency_id', selected.value);
                            setFieldValue(
                              'commission_currency_id',
                              selected.value
                            );
                          }}
                          onChange2={handleChange}
                          additionalProps={{
                            isLoadingCurrencyRate: isLoadingCurrencyRatesPair,
                          }}
                        />
                      </div>

                      {/* Combined Rate Type and Rate */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Rate Type"
                          type1="select"
                          type2="input"
                          name1="rate_type"
                          name2="rate"
                          value1={values.rate_type}
                          value2={values.rate}
                          options1={[
                            { label: 'X', value: 'X' },
                            { label: '/', value: '/' },
                          ]}
                          isSecondInputDisabled={true}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          //   placeholder1="Rate Type"
                          placeholder2="Enter Rate"
                          inputType2="number"
                          className1="rate-type"
                          className2="rate"
                          onChange1={(selected) => {
                            if (
                              selected.value === '/' &&
                              !currencyRatesPair?.reverse_rate
                            ) {
                              setShowMissingCurrencyRateModal(true);
                            } else {
                              setFieldValue('rate_type', selected.value);
                              if (selected.value === '/') {
                                setFieldValue(
                                  'rate',
                                  currencyRatesPair?.reverse_rate
                                );
                              } else {
                                setFieldValue(
                                  'rate',
                                  currencyRatesPair?.direct_rate
                                );
                              }
                            }
                          }}
                          onChange2={handleChange}
                          error={touched.rate && errors.rate}
                        />
                      </div>

                      {/* Combined AG Currency and Amount */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="AG FCy"
                          type1="select"
                          type2="input"
                          name1="ag_currency_id"
                          name2="ag_amount"
                          value1={tMNCurrencyObj?.value}
                          value2={values.ag_amount}
                          isDisabled={true}
                          inputType2="number"
                          min2={0}
                          options1={currencyOptions}
                          placeholder1="Currency"
                          placeholder2="Amount"
                          className1="ag-currency"
                          className2="ag-amount"
                          handleBlur={handleBlur}
                          onChange2={handleChange}
                        />
                      </div>

                      {/* VAT and Commission */}
                      {values.type == 'buy' && (
                        <>
                          {/* Combined Commission Currency and Amount */}
                          <div className="col-12 col-sm-6 mb-45">
                            <CombinedInputs
                              label="Commission"
                              type1="select"
                              type2="input"
                              name1="commission_currency_id"
                              name2="commission_amount"
                              value1={values.commission_currency_id}
                              value2={values.commission_amount}
                              options1={currencyOptions}
                              isDisabled={isDisabled}
                              isfirstInputDisabled={true}
                              handleBlur={handleBlur}
                              placeholder1="Currency"
                              placeholder2="Amount"
                              inputType2="number"
                              min2={0}
                              className1="commission-currency"
                              className2="commission-amount"
                              onChange1={(selected) => {
                                setFieldValue(
                                  'commission_currency_id',
                                  selected.value
                                );
                              }}
                              onChange2={(e) => {
                                handleChange(e);
                              }}
                            />
                            <ErrorMessage
                              name="commission_amount"
                              component="div"
                              className="input-error-message text-danger"
                            />
                          </div>

                          {vatData?.vatType?.vat_type === 'variable' && (
                            <div className="col-12 col-sm-6 mb-45">
                              <SearchableSelect
                                name={'vat_terms'}
                                label={'VAT %'}
                                options={getVATTermsOptions()}
                                isDisabled={isDisabled}
                                placeholder={'Select VAT %'}
                                value={values.vat_terms_id}
                                onChange={(selected) => {
                                  if (
                                    selected.label.startsWith('Out of Scope')
                                  ) {
                                    setShowVatOutOfScopeModal(true);
                                  }
                                  setFieldValue('vat_terms_id', selected.value);
                                  setFieldValue(
                                    'vat_terms',
                                    parseFloat(selected.percentage)
                                  );
                                }}
                                onBlur={handleBlur}
                              />
                            </div>
                          )}
                          {vatData?.vatType?.vat_type === 'fixed' && (
                            <div className="col-12 col-sm-6 mb-3">
                              <CustomInput
                                name={'vat_percentage'}
                                label={'VAT %'}
                                type={'number'}
                                disabled={true}
                                placeholder={'Enter VAT Percentage'}
                                value={
                                  vatData.vatType?.vat_percentage
                                    ? vatData.vatType?.vat_percentage
                                    : !isNaN(values.vat_terms)
                                    ? values.vat_terms
                                    : ''
                                }
                                onChange={handleChange}
                                onBlur={handleBlur}
                              />
                            </div>
                          )}
                          {/* Combined VAT Currency and Amount */}
                          <div className="col-12 col-sm-6 mb-45">
                            <CombinedInputs
                              label="VAT Amount"
                              type1="select"
                              type2="input"
                              name1="vat_currency_id"
                              name2="vat_amount"
                              value1={values.vat_currency_id}
                              value2={values.vat_amount}
                              options1={currencyOptions}
                              isDisabled={true}
                              handleBlur={handleBlur}
                              placeholder1="Currency"
                              placeholder2="Amount"
                              inputType2="number"
                              min2={0}
                              className1="vat-currency"
                              className2="vat-amount"
                            />
                          </div>
                        </>
                      )}

                      {/* Combined Total Currency and Amount */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Total"
                          type1="select"
                          type2="input"
                          name1="total_currency_id"
                          name2="total_amount"
                          value1={values.fc_currency_id}
                          value2={values.total_amount}
                          options1={currencyOptions}
                          isDisabled={true}
                          handleBlur={handleBlur}
                          placeholder1="Currency"
                          placeholder2="Amount"
                          inputType2="number"
                          min2={0}
                          className1="total-currency"
                          className2="total-amount"
                          onChange2={(e) => {
                            handleChange(e);
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-5">
                    <div className="row">
                      {/* Telex Transfer Amount */}
                      <div className="col-12 col-sm-6 col-xxl-8 offset-xxl-4 mb-3">
                        <CustomInput
                          name={'telex_transfer_amount'}
                          label={'Telex Transfer Amount'}
                          disabled={true}
                          value={values.telex_transfer_amount}
                        />
                      </div>
                      {/* Allocated */}
                      <div className="col-12 col-sm-6 col-xxl-8 offset-xxl-4 mb-3">
                        <CustomInput
                          name={'allocated'}
                          label={'Allocated'}
                          disabled={true}
                          value={values.allocated}
                        />
                      </div>
                      {/* Balance Amount */}
                      <div className="col-12 col-sm-6 col-xxl-8 offset-xxl-4 mb-3">
                        <CustomInput
                          name={'balance_amount'}
                          label={'Balance Amount'}
                          disabled={true}
                          value={values.balance_amount}
                        />
                      </div>
                    </div>
                  </div>

                  {/* TODO: Account Balance Cards */}
                  {!isDisabled && (
                    <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                      <div className="row">
                        {/* Right side cards */}
                        <div
                          className="col-12 mb-5"
                          style={{ maxWidth: '350px' }}
                        >
                          {/* Add any additional cards here if needed */}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Print and Account Balance Checkboxes */}
                  <div className="d-flex flex-wrap justify-content-start mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => {}}
                        readOnly={isDisabled}
                      />
                      <CustomCheckbox
                        label="Print"
                        checked={getPrintSettings('tmn_currency_deal')}
                        onChange={(e) => {
                          updatePrintSetting(
                            'tmn_currency_deal',
                            e.target.checked
                          );
                        }}
                        style={{ border: 'none', margin: 0 }}
                        readOnly={isDisabled}
                      />
                    </div>
                  </div>
                </div>

                {/* Add Allocation */}
                <div className="d-flex mb-4">
                  <CustomButton
                    disabled={
                      isDisabled ||
                      formikRef?.current?.values?.mode != 'regular' ||
                      formikRef.current?.values?.ag_amount === ''
                    }
                    variant="primary"
                    type="button"
                    text="Add Allocation"
                    onClick={() => setShowAddAllocationModal(true)}
                  />
                </div>

                {/* Special Commission */}
                <div className="d-flex">
                  <CustomButton
                    type={'button'}
                    onClick={handleNavigateToSpecialCommissionPage}
                    text={`${'Add'} Special Commission`}
                    // disabled={!!values.commission || isDisabled}
                  />
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>

      {/* { Allocation table */}
      {shouldShowAllocationTable && (
        <div className="mt-45">
          <CustomTable
            hasFilters={false}
            setFilters={false}
            headers={[
              'Sell No',
              'Account Name',
              'Amount',
              'Doc Type',
              'Number',
              'Bank',
              'Code',
              'City',
              'Description',
              'Action',
            ]}
            isLoading={false}
            sortKey={false}
            sortOrder={false}
            handleSort={false}
            isPaginated={false}
          >
            <tbody>
              {allocations?.map((row) => (
                <tr key={row.id}>
                  <td>{row.id}</td>
                  <td>{row.account_name}</td>
                  <td>{row.amount}</td>
                  <td>{row.document_type}</td>
                  <td>{row.number}</td>
                  <td>{row.bank_name}</td>
                  <td>{row.code}</td>
                  <td>{row.city}</td>
                  <td>{row.description}</td>
                  <td>
                    <TableActionDropDown
                      actions={[
                        {
                          name: 'Edit',
                          icon: HiOutlinePencilSquare,
                          onClick: () => {
                            setShowAddAllocationModal(true);
                            setEditAllocationRow(
                              allocations.find((x) => x.id === row.id)
                            );
                          },
                          className: 'edit',
                        },
                        {
                          name: 'Delete',
                          icon: HiOutlineTrash,
                          onClick: () => {
                            setAllocations(
                              allocations.filter((x) => x.id !== row.id)
                            );
                          },
                          className: 'delete',
                        },
                      ]}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </CustomTable>
        </div>
      )}

      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleResetRows,
            variant: 'secondaryButton',
          },
        ]}
        loading={createTMNCurrencyDealMutation.isPending}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />

      {/* Upload Attachments Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setAddedAttachments}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Allocation Modal */}
      <CustomModal
        show={!!showAddAllocationModal}
        close={() => setShowAddAllocationModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        <AddAllocationDetailsForm
          inPopup
          allocationData={editAllocationRow}
          onSuccess={handleAddAllocation}
          bankOptions={getBankOptions()}
          balanceAmount={formikRef.current?.values?.balance_amount}
          telexTransferAmount={formikRef.current?.values?.ag_amount}
          docTypeOptions={getDocTypeOptions()}
          cityOptions={getCityOptions()}
          getAccountsByTypeOptions={getAccountsByTypeOptions}
          onCancel={() => {
            setEditAllocationRow(null);
            setShowAddAllocationModal('');
          }}
        />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: '' }}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  <CustomButton type="submit" text={'Submit'} />
                  <CustomButton
                    variant={'secondaryButton'}
                    text={'Cancel'}
                    type={'button'}
                    onClick={() => {
                      setShowVatOutOfScopeModal(false);
                      formikRef.current.values.vat_terms = '';
                    }}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default NewTmnCurrencyDeal;
