import * as Yup from 'yup';

export const signUpValidationSchema = Yup.object().shape({
  business_name: Yup.string()
    .required('Business Name is required')
    .max(50, 'Business Name must be at most 50 characters'),
  user_name: Yup.string()
    .required('Full Name is required')
    .matches(
      /^[a-zA-Z]+(?: [a-zA-Z]+)*$/,
      'Full Name must contain only letters'
    )
    .max(39, 'Full Name must be at most 39 characters'),
  user_id: Yup.string()
    .required('User ID is required')
    .max(30, 'User ID must be at most 30 characters'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must be atleast 8 characters long'),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords do not match')
    .required('Please re-enter your new password'),
});
export const changePasswordValidation = Yup.object().shape({
  current_password: Yup.string().required('Current Password is required'),
  password: Yup.string()
    .required('New Password is required')
    .min(8, 'Password must be atleast 8 characters long'),
  password_confirmation: Yup.string()
    .required('Confirm Password is required')
    .oneOf([Yup.ref('password'), null], 'Passwords do not match.')
    .label('Confirm Password'),
});
export const loginValidationSchema = Yup.object().shape({
  user_id: Yup.string().required('User ID is required'),
  password: Yup.string()
    // .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),
});
export const adminLoginValidationSchema = Yup.object().shape({
  email: Yup.string().required('Email is required'),
  password: Yup.string()
    // .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),
});
export const forgotEmail = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email address is required'),
});
export const forgotCode = Yup.object().shape({
  verificationCode: Yup.string()
    .required('Verification code is required')
    .matches(/^\d{4}$/, 'Verification code must be 4 digits'),
});
export const forgotPassword = Yup.object().shape({
  password: Yup.string()
    // .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),
  password_confirmation: Yup.string()
    .required('Confirm Password is required')
    .oneOf([Yup.ref('password'), null], 'Passwords do not match')
    .label('Confirm Password'),
});
export const supportFormSchema = Yup.object().shape({
  name: Yup.string().required('Full Name is required'),
  email: Yup.string().required('Email is required'),
  support_type: Yup.string().required('Support Type is required'),
  message: Yup.string().required('Message is required'),
  contact_no: Yup.string()
    .required('Contact number is required')
    .matches(/^\+?[1-9]\d{9,14}$/, 'Enter a valid contact number'),
});

export const addWalkInCustomerValidationSchema = Yup.object().shape({
  customer_name: Yup.string().required('Customer name is required'),
  company: Yup.string().required('Company name is required'),
  city: Yup.string().required('City is required'),
  mobile_number: Yup.string()
    .required('Mobile number is required')
    .matches(/^\+?[1-9]\d{9,14}$/, 'Enter a valid mobile number'),
  telephone_number: Yup.string()
    .required('Telephone number is required')
    .matches(/^\+?[1-9]\d{9,14}$/, 'Enter a valid telephone number'),
  email: Yup.string().nullable().email('Invalid email format'),
  id_type: Yup.string().required('ID type is required'),
  id_number: Yup.string().required('ID number is required'),
  // issue_date: Yup.date()
  //   .required('Issue date is required')
  //   .typeError('Invalid date format'),
  // expiry_date: Yup.date()
  //   .required('Expiry date is required')
  //   .typeError('Invalid date format')
  //   .min(Yup.ref('issue_date'), 'Expiry date must be after issue date'),
  vat_trn: Yup.string()
    .matches(
      /^[a-zA-Z0-9\s]+$/, // Only allows letters, numbers, and spaces
      'Field cannot contain special characters'
    )
    .nullable(),
  nationality: Yup.string().required('Nationality is required'),
  status: Yup.string().required('Status is required'),
});
export const addCurrencyRegisterValidationSchema = Yup.object().shape({
  currency_code: Yup.string().required('Currency Code is required'),
  currency_name: Yup.string().required('Currency Name is required'),
  rate_type: Yup.string().required('Rate Type is required'),
  currency_type: Yup.string().required('Currency Type is required'),
  rate_variation: Yup.number().required('Rate Variation is required'),
  allow_online_rate: Yup.string(),
  allow_auto_pairing: Yup.string(),
  allow_second_preference: Yup.string(),
  special_rate_currency: Yup.string(),
  restrict_pair: Yup.string(),
});
export const editCurrencyRegisterValidationSchema = Yup.object().shape({
  currency_code: Yup.string().required('Currency Code is required'),
  currency_name: Yup.string().required('Currency Name is required'),
  rate_type: Yup.string().required('Rate Type is required'),
  currency_type: Yup.string().required('Currency Type is required'),
  rate_variation: Yup.number().required('Rate Variation is required'),
  // group: Yup.string().required('Group is required'),
  allow_online_rate: Yup.string(),
  allow_auto_pairing: Yup.string(),
  allow_second_preference: Yup.string(),
  special_rate_currency: Yup.string(),
  restrict_pair: Yup.string(),
});
export const newWareHouseSchema = Yup.object({
  name: Yup.string().required('Warehouse name is required'),
});
export const addClassificationSchema = Yup.object({
  classification: Yup.string().required('Classification required'),
  description: Yup.string().required('Description is required'),
});
export const addClassificationTypeSchema = Yup.object({
  type: Yup.string().required('Classification type is required'),
});
export const addPartyLedgerClassificationSchema = Yup.object({
  classification: Yup.string().required('Classification is required'),
});
export const addBeneficiaryRegisterValidationSchema = Yup.object().shape({
  account: Yup.string().required('Account is required'),
  type: Yup.string().required('Type is required'),
  name: Yup.string()
    .required('Name is required')
    .min(3, 'Name must be at least 3 characters')
    .max(50, 'Name cannot exceed 50 characters'),
  company: Yup.string()
    .nullable()
    .min(2, 'Company name must be at least 2 characters'),
  contact_no: Yup.string()
    .nullable()
    .min(8, 'Contact number must be at least 8 digits')
    .max(15, 'Contact number cannot exceed 15 digits'),
  bank_account_number: Yup.string()
    .nullable()
    .matches(/^[0-9]+$/, 'Bank account number must be numeric'),
  swift_bic_code: Yup.string()
    .nullable()
    .matches(/^[A-Za-z0-9]{8,11}$/, 'Invalid SWIFT/BIC code'),
  routing_number: Yup.string()
    .nullable()
    .matches(/^[0-9]+$/, 'Routing number must be numeric'),
  iban: Yup.string()
    .nullable()
    .matches(/^[A-Za-z0-9]{15,34}$/, 'Invalid IBAN format'),
  corresponding_bank_account_number: Yup.string()
    .nullable()
    .matches(/^[0-9]+$/, 'Corresponding bank account number must be numeric'),
  corresponding_swift_bic_code: Yup.string()
    .nullable()
    .matches(/^[A-Za-z0-9]{8,11}$/, 'Invalid corresponding SWIFT/BIC code'),
  corresponding_routing_number: Yup.string()
    .nullable()
    .matches(/^[0-9]+$/, 'Corresponding routing number must be numeric'),
  corresponding_iban: Yup.string()
    .nullable()
    .matches(/^[A-Za-z0-9]{15,34}$/, 'Invalid corresponding IBAN format'),
  purpose: Yup.string().nullable(),
  ifsc_code: Yup.string()
    .nullable()
    .matches(/^[A-Za-z0-9]{11}$/, 'Invalid IFSC code'),
});
export const addCBClassificationSchema = Yup.object({
  group: Yup.string().required('Group required'),
  title: Yup.string().required('Title is required'),
});
export const addCommissionValidationSchema = Yup.object().shape({
  account_type: Yup.string().required('Account Type name is required'),
  account: Yup.string().required('account is required'),
  commission_type: Yup.string().required('Commission type is required'),
  receipt_percentage: Yup.string().required('Receipt percentage is required'),
  payment_percentage: Yup.string().required('Payment percentage is required'),
  tmn_buy_remittance_percentage: Yup.string().required(
    'TMN buy remittance percentage is required'
  ),
  tmn_sell_remittance_percentage: Yup.string().required(
    'TMN sell remittance percentage is required'
  ),
  currency_transfer_request_percentage: Yup.string().required(
    'Currency transfer request percentage is required'
  ),
  outward_remittance_percentage: Yup.string().required(
    'Outward remittance percentage is required'
  ),
  currency_buy_sell_percentage: Yup.string().required(
    'Currency buy/sell percentage is required'
  ),
  inward_remittance_percentage: Yup.string().required(
    'Inward remittance percentage is required'
  ),
});
export const addCostCenterRegisterValidationSchema = Yup.object({
  type: Yup.string().required('Title is required'),
  group: Yup.string().required('Group required'),
});

export const partyLedgerAccountValidationSchema = Yup.object().shape({
  account_title: Yup.string()
    .matches(/^[a-zA-Z\s]+$/, 'Account Title must contain only letters') // Letters and spaces
    .max(30, 'Account Title cannot exceed 30 characters')
    .required('Account Title is required'),
  classification: Yup.string().required('Classification is required'),
  debit_posting_account: Yup.string().required(
    'Debit Posting Account is required'
  ),
  credit_posting_account: Yup.string().required(
    'Credit Posting Account is required'
  ),
});

// export const userMaintenanceValidationSchema = Yup.object().shape({
//   user_name: Yup.string().required('Company Name is required'),
//   user_id: Yup.string()
//     .required('Telephone Number is required')
//     .matches(/^\+?[1-9]\d{1,14}$/, 'Enter a valid telephone number'),
//   email: Yup.string()
//     .nullable()
//     .matches(/^\+?[1-9]\d{1,14}$/, 'Enter a valid fax number'), // Optional but validated
//   password: Yup.string().nullable().email('Enter a valid email'), // Optional but must be valid if entered
//   password_confirmation: Yup.string()
//     .nullable()
//     .matches(/^\+?[1-9]\d{1,14}$/, 'Enter a valid mobile number'), // Optional but validated
// });

export const partyLedgerContactValidationSchema = Yup.object().shape({
  company_name: Yup.string().required('Company Name is required'),
  telephone_number: Yup.string()
    .required('Telephone Number is required')
    .matches(/^\+?[1-9]\d{1,14}$/, 'Enter a valid telephone number'),
  fax: Yup.string()
    .nullable()
    .matches(/^\+?[1-9]\d{1,14}$/, 'Enter a valid fax number'), // Optional but validated
  email: Yup.string().nullable().email('Enter a valid email'), // Optional but must be valid if entered
  mobile_number: Yup.string()
    .nullable()
    .matches(/^\+?[1-9]\d{1,14}$/, 'Enter a valid mobile number'), // Optional but validated
});
export const partyLedgerIdValidationSchema = Yup.object().shape({
  id_type: Yup.string().required('ID Type is required'),
  id_number: Yup.string().required('ID Number is required'),
  issue_date: Yup.date()
    .required('Issue date is required')
    .typeError('Invalid date format'),
  valid_upto: Yup.date()
    .required('Expiry date is required')
    .typeError('Invalid date format')
    .min(Yup.ref('issue_date'), 'Expiry date must be after issue date'),
});
export const partyLedgerVatValidationSchema = Yup.object().shape({
  vat_trn: Yup.string()
    .matches(
      /^[a-zA-Z0-9.\s]+$/,
      'VAT TRN can only contain letters, numbers, decimal points and spaces'
    )
    .nullable('VAT TRN is required'),
  vat_country: Yup.string().nullable('VAT country is required'),
  vat_state: Yup.string().nullable('VAT state is required'),
});

export const addTellerRegisterValidationSchema = Yup.object().shape({
  till_assigned_to_user: Yup.string().required(
    'Till Assigned To User is required'
  ),
  cash_account: Yup.string().required('Cash Account is required'),
});
export const addCountryValidationSchema = Yup.object({
  country: Yup.string().required('Country is required'),
});
export const addOfficeLocationValidationSchema = Yup.object({
  office_location: Yup.string().required('Office Location is required'),
});
export const addGroupMasterValidationSchema = Yup.object({
  group_type: Yup.string().required('Group Type is required'),
  description: Yup.string().required('Description is required'),
});
export const addSalesmanValidationSchema = Yup.object({
  name: Yup.string()
    .required('Name is required')
    .max(30, 'Name must be at most 30 characters long'),
});
export const addDocumentRegisterValidationSchema = Yup.object({
  group_name: Yup.string().required('Group is required'),
  type: Yup.string().required('Type is required'),
  description: Yup.string().required('Description is required'),
  number: Yup.string().required('Number is required'),
  issue_date: Yup.date()
    .required('Issue date is required')
    .typeError('Invalid date format'),
  due_date: Yup.date()
    .required('Due date is required')
    .typeError('Invalid date format')
    .min(Yup.ref('issue_date'), 'Due date must be after issue date'),
});
export const addAttachmentValidationSchema = Yup.object({
  files: Yup.mixed()
    .required('A document is required.') // Ensure something is provided
    .test(
      'is-valid-documents',
      'At least one valid file must be provided.',
      function (value) {
        const validDocumentTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'image/jpeg',
          'image/jpg',
          'image/webp',
          'image/gif',
          'image/png',
        ];

        // Ensure at least one valid file or URL is present
        const hasValidFiles =
          value &&
          value.some((file) => {
            if (typeof file === 'string') {
              return true; // If it's a string, assume it's a valid URL
            }
            if (file instanceof File) {
              return validDocumentTypes.includes(file.type); // Validate document file type
            }
            return false;
          });

        return hasValidFiles;
      }
    ),
});
export const addCOAValidationSchema = Yup.object({
  account_name: Yup.string().required('Account Name is required'),
  account_type: Yup.string().required('Account Type is required'),
  // parent_account_id: Yup.string().required('Parent Account is required'),
  // parent_account_id: Yup.string().required('Parent Account is required'),
  description: Yup.string().required('Description is required'),
});
export const addSupportTypeSchema = Yup.object({
  name: Yup.string().required('Support Type Name is required'),
});

export const editProfileSchema = Yup.object({
  business_name: Yup.string().required('Business name is required'),
  user_name: Yup.string().required('User name is required'),
  phone: Yup.string().required('Phone Number is required'),
});

export const editAdminProfileSchema = Yup.object({
  first_name: Yup.string().required('First name is required'),
  last_name: Yup.string().required('Last name is required'),
  country_code: Yup.string().required('Country Code is required'),
  phone: Yup.string().required('Phone Number is required'),
});

export const changePasswordSchema = Yup.object({
  current_password: Yup.string().required('Current Password is required'),
  password: Yup.string().required('New Password is required'),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords do not match')
    .required('Please re-enter your new password'),
});

export const addPackageValidationSchema = Yup.object().shape({
  title: Yup.string()
    .required('Subscription Name is required')
    .min(3, 'Subscription Name must be at least 3 characters long')
    .max(50, 'Subscription Name must be at most 50 characters long'),
  no_of_users: Yup.number()
    .required('Number of Users is required')
    .typeError('Number of Users must be a valid number')
    .positive('Number of Users must be positive')
    .integer('Number of Users must be an integer'),
  branches: Yup.number()
    .required('Branch is required')
    .typeError('Branch must be a valid number')
    .min(1, 'Branch must be at least 1') // Assuming a minimum value of 1 for branches
    .max(100, 'Branch must be at most 100'), // Assuming a maximum value of 100 for branches
  price_monthly: Yup.number()
    .required('Monthly Price is required')
    .typeError('Monthly Price must be a valid number')
    .positive('Monthly Price must be positive'),
  price_yearly: Yup.number()
    .required('Yearly Price is required')
    .typeError('Yearly Price must be a valid number')
    .positive('Yearly Price must be positive'),
});
export const passwordResetValidationSchema = Yup.object({
  user_id: Yup.string().required('User ID is required'),
  current_password: Yup.string().required('Current Password is required'),
  password: Yup.string().required('New Password is required'),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords do not match')
    .required('Please re-enter new password'),
});

export const branchManagementValidationSchema = Yup.object().shape({
  name: Yup.string()
    .required('Branch Name is required')
    .max(50, 'Branch Name must be at most 50 characters'),
  address: Yup.string()
    .required('Address is required')
    .max(100, 'Address must be at most 100 characters'),
  city: Yup.string().required('City is required'),
  phone: Yup.string()
    .required('Contact No is required')
    .matches(/^\+?[1-9]\d{9,14}$/, 'Enter a valid Contact No number'),
  supervisor: Yup.string().required('Supervisor is required'),
});
export const branchVatRateValidationSchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
  percentage: Yup.number()
    .required('Percentage is required')
    .typeError('Percentage must be a valid number'),
});

// Branch Management Validation Schemas
export const branchSystemDatesValidationSchema = Yup.object().shape({
  opening_date: Yup.date()
    .required('Opening Date is required')
    .typeError('Invalid date format'),
  closed_upto_date: Yup.date()
    .required('Closed Upto Date is required')
    .typeError('Invalid date format')
    .min(
      Yup.ref('opening_date'),
      'Closed Upto date must be after Opening date'
    ),
  accept_data_upto_date: Yup.date()
    .required('Accept Data Upto Date is required')
    .typeError('Invalid date format')
    .min(
      Yup.ref('closed_upto_date'),
      'Accept Data Upto date must be after Closed Upto date'
    ),
});

export const branchDashboardValidationSchema = Yup.object().shape({
  startup_alert_period: Yup.number()
    .required('Startup Alert Period is required')
    .positive('Must be a positive number')
    .integer('Must be a whole number'),
  currency_rate_trend: Yup.number()
    .required('Currency Rate Trend is required')
    .positive('Must be a positive number')
    .integer('Must be a whole number'),
  dashboard_comparison_period: Yup.number()
    .required('Dashboard Comparison is required')
    .positive('Must be a positive number')
    .integer('Must be a whole number'),
  // currency_pairs: Yup.array().min(
  //   1,
  //   'At least one currency pair must be selected'
  // ),
});

export const branchCentralBankValidationSchema = Yup.object().shape({
  inward_payment_order_limit: Yup.number()
    .required('Inwards Payment Order is required')
    .positive('Must be a positive number'),
  outward_remittance_limit: Yup.number()
    .required('Outwards Remittance is required')
    .positive('Must be a positive number'),
  counter_transaction_limit: Yup.number()
    .required('Counter Transaction is required')
    .positive('Must be a positive number'),
  cash_limit: Yup.number()
    .required('Cash Limit is required')
    .positive('Must be a positive number'),
  cash_bank_pay_limit: Yup.number()
    .required('Cash/Bank Pay Limit is required')
    .positive('Must be a positive number'),
  monthly_transaction_limit: Yup.number()
    .required('Monthly Transaction is required')
    .positive('Must be a positive number'),
  counter_commission_limit: Yup.number()
    .required('Counter Commission is required')
    .positive('Must be a positive number'),
});

export const branchVatParametersValidationSchema = Yup.object().shape({
  vat_trn: Yup.string()
    .required('VAT TRN is required')
    .matches(
      /^[a-zA-Z0-9.\s]+$/,
      'VAT TRN can only contain letters, numbers, decimal points and spaces'
    ),
  vat_country: Yup.string().required('Country is required'),
  default_city: Yup.string().required('Default City is required'),
  cities: Yup.string().required('Cities is required'),
  vat_type: Yup.string().required('VAT Type is required'),
  vat_percentage: Yup.number().when('vat_type', {
    is: (val) => val !== 'variable',
    then: (schema) =>
      schema
        .required('VAT Percentage is required')
        .min(0, 'VAT Percentage cannot be negative')
        .max(100, 'VAT Percentage cannot exceed 100'),
  }),
});

export const branchMiscParametersValidationSchema = Yup.object().shape({
  debit_posting_account: Yup.string().required(
    'Debit Posting Account is required'
  ),
  credit_posting_account: Yup.string().required(
    'Credit Posting Account is required'
  ),
});

export const deleteChequeBookValidationSchema = Yup.object().shape({
  bank: Yup.string().required('Bank Account is required'),
  reference_no: Yup.string().required('Reference No. is required'),
});

export const addChequeBookValidationSchema = Yup.object().shape({
  bank: Yup.string().required('Bank Account is required'),
  starting_no: Yup.string().required('Starting No. is required'),
  count: Yup.string().required('Count is required'),
});

export const customSubscriptionRequestValidationSchema = Yup.object().shape({
  user_name: Yup.string().required('Business Name is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  no_of_users: Yup.string().required('Expected No. of Users is required'),
  branches: Yup.string().required('Expected No. of Branches is required'),
});

export const paymentValidationSchema = Yup.object().shape({
  cardholderName: Yup.string()
    .trim()
    .matches(/^[a-zA-Z\s]+$/, 'Cardholder Name must contain only letters')
    .required('Card holder name is required'),
  number: Yup.string()
    .trim()
    .matches(/^\d{16}$/, 'Card Number must be exactly 16 digits')
    .required('Card Number is required'),
  cvv: Yup.string()
    .trim()
    .matches(/^\d{3,4}$/, 'CVV must be 3 or 4 digits')
    .required('CVV is required'),
  exp_month: Yup.string()
    // .matches(/^(0[1-9]|1[0-2])$/, 'Expiry Month must be between 01 and 12')
    .required('Validity is required'),
});

export const foreignCurrencyDealValidationSchema = Yup.object().shape({
  date: Yup.date(),
  debitLedger: Yup.string().required('Debit ledger is required'),
  debitAccount: Yup.string().required('Debit account is required'),
  creditLedger: Yup.string().required('Credit ledger is required'),
  creditAccount: Yup.string().required('Credit account is required'),
  buyFCyDr: Yup.string().required('Buy FCy (Dr) is required'),
  buyFCyDrAmount: Yup.string().required('Buy FCy (Dr) amount is required'),
  buyFCyCr: Yup.string().required('Buy FCy (Cr) is required'),
  buyFCyCrAmount: Yup.string().required('Buy FCy (Cr) amount is required'),
  rateType: Yup.string().required('Rate type is required'),
  sellFCCr: Yup.string().required('Sell FC (Cr) is required'),
  sellFCDr: Yup.string().required('Sell FC (Dr) is required'),
  commissionType: Yup.string().required('Commission type is required'),
  commission: Yup.string().required('Commission is required'),
  narration: Yup.string().required('Narration is required'),
  comment: Yup.string().required('Comment is required'),
});

export const bankTransactionSchema = Yup.object().shape({
  transactionType: Yup.string().required('Transaction type is required'),
  bank: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: () => Yup.string().required('Bank is required'),
  }),
  ledger: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: () => Yup.string().required('Ledger is required'),
  }),
  fromAccount: Yup.string().required('From account is required'),
  toAccount: Yup.string().required('To account is required'),
  chequeNumber: Yup.string().required('Cheque number is required'),
  amount: Yup.number()
    .required('Amount is required')
    .positive('Amount must be positive'),
  currency: Yup.string().required('Currency is required'),
  commissionType: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: () => Yup.string().required('Commission type is required'),
  }),
  commissionPercentage: Yup.number().when('transactionType', {
    is: 'inward_tt',
    then: () => Yup.number().required('Commission percentage is required'),
  }),
  commissionAmount: Yup.number().when('transactionType', {
    is: 'inward_tt',
    then: () => Yup.number().required('Commission amount is required'),
  }),
  country: Yup.string().when('transactionType', {
    is: 'inward_tt',
    then: () => Yup.string().required('Country is required'),
  }),
  narration: Yup.string().required('Narration is required'),
});

export const currencyTransferValidationSchema = Yup.object().shape({
  debitLedger: Yup.string().required('Debit Ledger is required'),
  debitAccount: Yup.string().required('Debit Account is required'),
  creditLedger: Yup.string().required('Credit Ledger is required'),
  creditAccount: Yup.string().required('Credit Account is required'),
  accountTitle: Yup.string().required('Account Title is required'),
});

export const mainFormValidationSchema = Yup.object().shape({
  debite_note_number: Yup.string().required('Debit Note Number is required'),
  date: Yup.date().required('Date is required'),
  account: Yup.string().required('Account is required'),
  ledger: Yup.string().required('Ledger is required'),
  mode: Yup.string().required('Mode is required'),
  account_select: Yup.string().required('Account selection is required'),
  pay_type: Yup.string().required('Pay Type is required'),
  order_amount: Yup.number()
    .typeError('Amount must be a number')
    .required('Order Amount is required')
    .positive('Amount must be positive'),
  ref_no: Yup.string().required('Reference Number is required'),
  balance_amount: Yup.number()
    .typeError('Amount must be a number')
    .required('Balance Amount is required'),
  beneficiary: Yup.string().required('Beneficiary is required'),
  contact_no: Yup.string()
    .matches(/^\d+$/, 'Contact number must contain only digits')
    .min(8, 'Contact number must be at least 8 digits')
    .required('Contact number is required'),
  vat_type: Yup.string().required('VAT Type is required'),
  vat_terms: Yup.string().required('VAT Terms is required'),
  id_detail: Yup.string().required('ID Detail is required'),
  settle_date: Yup.date().required('Settle Date is required'),
  place_of_issue: Yup.string().required('Place of Issue is required'),
  sender: Yup.string().required('Sender is required'),
  due_date: Yup.date().required('Due Date is required'),
  nationality: Yup.string().required('Nationality is required'),
  commission: Yup.number()
    .typeError('Commission must be a number')
    .min(0, 'Commission cannot be negative'),
  vat_amount: Yup.number()
    .typeError('VAT Amount must be a number')
    .min(0, 'VAT Amount cannot be negative')
    .required('VAT Amount is required'),
  net_total: Yup.number()
    .typeError('Net Total must be a number')
    .required('Net Total is required'),
  narration: Yup.string().required('Narration is required'),
});

export const inwardPayValidationSchema = Yup.object().shape({
  beneficiary_id: Yup.string().required('Beneficiary is required'),
  ledger_account: Yup.string().required('Ledger is required'),
  account_id: Yup.string().required('Account selection is required'),
  vat_type: Yup.string().required('VAT Type is required'),
  vat_terms_id: Yup.string().required('VAT Terms is required'),
  settle_date: Yup.date().required('Settle Date is required'),
  due_date: Yup.date().required('Due Date is required'),
  amount: Yup.number()
    .typeError('Amount must be a number')
    .required('Amount is required')
    .positive('Amount must be positive'),
  commission: Yup.number()
    .typeError('Commission must be a number')
    .min(0, 'Commission cannot be negative'),
  net_total: Yup.number()
    .typeError('Net Total must be a number')
    .required('Net Total is required'),
  narration: Yup.string().required('Narration is required'),
  origin_id: Yup.string().required('Origin is required'),
  purpose_id: Yup.string().required('Purpose is required'),
  mode: Yup.string().when('ledger_account', {
    is: 'general',
    then: Yup.string().required('Mode is required'),
    otherwise: Yup.string(),
  }),
  cheque_id: Yup.string().when('mode', {
    is: (mode) => mode === 'Bank' || mode === 'PDC',
    then: Yup.string().required('Cheque Number is required'),
    otherwise: Yup.string(),
  }),
  sender_nationality_id: Yup.string().required(
    'Sender Nationality is required'
  ),
});

export const vatOutOfScopeValidationSchema = Yup.object().shape({
  out_of_scope: Yup.string()
    .required('Reason is required')
    .min(10, 'Reason must be at least 10 characters')
    .max(500, 'Reason cannot exceed 500 characters'),
});
export const specialCommissionValidationSchema = Yup.object().shape({
  commission: Yup.number()
    .required('Commission percentage is required')
    .min(0, 'Commission percentage must be greater than or equal to 0')
    .max(100, 'Commission percentage must be less than or equal to 100'),
  total_commission: Yup.number()
    .required('Commission amount is required')
    .min(0, 'Commission amount must be greater than 0'),
});
export const accountToAccountvalidationSchema = Yup.object().shape({
  debitLedger: Yup.string().required('Debit ledger is required'),
  debitAccount: Yup.string().required('Debit account is required'),
  creditLedger: Yup.string().required('Credit ledger is required'),
  creditAccount: Yup.string().required('Credit account is required'),
  chequeNumber: Yup.string().when('showChequeNumber', {
    is: true,
    then: (schema) => schema.required('Cheque number is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  currency: Yup.string().required('Currency is required'),
  fcAmount: Yup.number()
    .positive('FC Amount must be positive')
    .required('FC Amount is required'),
  comment: Yup.string().trim(),
});