import { useMutation, useQuery } from '@tanstack/react-query';
import { ErrorMessage, FastField, Form, Formik } from 'formik';
import React, { useEffect, useState } from 'react';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import { showToast } from '../../../Components/Toast/Toast';
import { usePageTitle } from '../../../Hooks/usePageTitle';
// import { createUser } from '../../../Services/Administration/Users';
import { Col } from 'react-bootstrap';
import PhoneInput, { parsePhoneNumber } from 'react-phone-number-input';
import { useNavigate } from 'react-router-dom';
import { PulseLoader } from 'react-spinners';
import Accordion from '../../../Components/Accordion/Accordion';
import ChartOfAccountDropdown from '../../../Components/ChartOfAccountDropdown/ChartOfAccountDropdown';
import CheckboxAccordion from '../../../Components/CheckboxAccordion/CheckboxAccordion';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import {
  addUser,
  getAccessRights,
  getAccessRightsGeneral,
  getAccountPermissions,
} from '../../../Services/Administration/UserMaintenance';
import { setUserId } from '../../../Services/Auth';
import useThemeStore from '../../../Stores/ThemeStore';
import { themeDictionary } from '../../../Utils/Constants/ColorConstants';
import {
  getUsersOptions,
  isNullOrEmpty,
  showErrorToast,
} from '../../../Utils/Utils';
import './UserMaintenance.css';

const NewUserMaintenance = () => {
  usePageTitle('User Maintenance - Create');
  const navigate = useNavigate();
  const { theme } = useThemeStore();
  const [copyFrom, setCopyFrom] = useState({
    access_rights_copy_from: '',
    account_permissions_copy_from: '',
  });

  const [otherAccessUserId, setOtherAccessUserId] = useState('');
  const [otherPermissionUserId, setOtherPermissionUserId] = useState('');

  const getUserAccessRights = async () => {
    if (copyFrom.access_rights_copy_from) {
      setOtherAccessUserId(copyFrom.access_rights_copy_from);
    }
  };

  // Get Other User Access Rights
  const {
    data: otherUserAccessRights,
    isLoading: isLoadingUserAccessRights,
    isError: IsErrorUserAccessRights,
    error: ErrorUserAccessRights,
  } = useQuery({
    queryKey: ['UserAccessRights', otherAccessUserId], // Ensure the query key uniquely identifies the query
    queryFn: () => getAccessRights(otherAccessUserId),
    enabled: !!otherAccessUserId,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Get Other User Account Permission
  const {
    data: otherUserAccountPermissions,
    isLoading: isLoadingUserAccountPermissions,
    isError: IsErrorUserAccountPermissions,
    error: ErrorUserAccountPermissions,
  } = useQuery({
    queryKey: ['UserAccountPermissions', otherPermissionUserId],
    queryFn: () => getAccountPermissions(otherPermissionUserId),
    enabled: !!otherPermissionUserId,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  const getUserAccountPermissions = async () => {
    if (copyFrom.account_permissions_copy_from) {
      setOtherPermissionUserId(copyFrom.account_permissions_copy_from);
    }
  };

  function removeFalseValues(obj) {
    // Iterate through each key in the object
    for (const key in obj) {
      // If the value is an object, recursively call the function on it
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        removeFalseValues(obj[key]);
      }

      // If the value is false, delete the key
      if (obj[key] === false || isNullOrEmpty(obj[key])) {
        delete obj[key];
      }
    }
    return obj;
  }

  const handleSubmit = (values) => {
    // Combine all permission states into a single object
    const combinedAccessRights = {
      master: masterPermissions,
      transaction: transactionPermissions,
      process: processPermissions,
      reports: reportsPermissions,
      administration: administrationPermissions,
    };
    const transformedMasterAccessRights =
      removeFalseValues(combinedAccessRights);

    //chart of accounts permissions transformed
    const transformedPermissions = accountFinalPermissions?.map((detail) => ({
      [detail]: true,
    }));
    const payload = {
      ...values,
      access_rights: transformedMasterAccessRights,
      accounts_permission: transformedPermissions,
    };
    createUserMutation.mutate(payload);
  };

  const createUserMutation = useMutation({
    mutationFn: addUser,
    onSuccess: () => {
      showToast('User Created Successfully', 'success');
      setTimeout(() => {
        navigate(-1);
      }, 300);
    },
    onError: (error) => {
      console.error('Failed to create user', error);
      showErrorToast(error.message, 'error');
    },
  });

  const timeSlots = [
    { day: 'Monday' },
    { day: 'Tuesday' },
    { day: 'Wednesday' },
    { day: 'Thursday' },
    { day: 'Friday' },
    { day: 'Saturday' },
    { day: 'Sunday' },
  ];
  const [userIdSuggestions, setUserIdSuggestions] = useState([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [suggestionError, setSuggestionError] = useState('');

  const handleUserIDChange = async (e) => {
    const userName = e;

    if (userName.length > 2) {
      // Fetch suggestions if input is valid
      try {
        setLoadingSuggestions(true);
        const response = await setUserId(userName);
        setUserIdSuggestions(response || []); // Assuming API returns `suggestions`
        setSuggestionError('');
      } catch (error) {
        setSuggestionError(error?.message);
        console.error('Error fetching user ID suggestions:', error);
      } finally {
        setLoadingSuggestions(false);
      }
    } else {
      setUserIdSuggestions([]);
    }
  };

  const [accountFinalPermissions, setAccountFinalPermissions] = useState([]);

  const [masterPermissions, setMasterPermissions] = useState({});
  const [transactionPermissions, setTransactionPermissions] = useState({});
  const [processPermissions, setProcessPermissions] = useState({});
  const [reportsPermissions, setReportPermissions] = useState({});
  const [administrationPermissions, setAdministrationPermissions] = useState(
    {}
  );

  useEffect(() => {
    if (!otherUserAccessRights) return;
    setMasterPermissions(otherUserAccessRights?.master || {});
    setTransactionPermissions(otherUserAccessRights?.transactions || {});
    setProcessPermissions(otherUserAccessRights?.process || {});
    setReportPermissions(otherUserAccessRights?.reports || {});
    setAdministrationPermissions(otherUserAccessRights?.administration || {});
  }, [otherUserAccessRights]);

  // Effect to fetch general permissions (used when no `otherAccessUserId` is selected)
  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        const data = await getAccessRightsGeneral();
        if (data) {
          setMasterPermissions(data?.master || {});
          setTransactionPermissions(data?.transactions || {});
          setProcessPermissions(data?.process || {});
          setReportPermissions(data?.reports || {});
          setAdministrationPermissions(data?.administration || {});
        }
      } catch (err) {
        console.error('Error fetching permissions:', err);
      }
    };

    fetchPermissions(); // Fetch on component mount
  }, []);

  // Reusable AccessRights List Component
  const PermissionsList = ({ key = '', module, setPermissions }) => {
    return (
      <div key={key} className="row justify-content-center">
        <div className="col-12 col-xl-12">
          <div className="row justify-content-center">
            {Object.entries(module).map(([feature, actions], index) => (
              <div key={feature} className="col-12 col-xl-5">
                <CheckboxAccordion
                  title={formatTitle(feature)}
                  color={themeDictionary[theme][0]}
                  module={Object.entries(actions).map(([key, value]) => ({
                    name: key,
                    value,
                  }))}
                  onPermissionsChange={(newPermissions) => {
                    setPermissions((prevPermissions) => ({
                      ...prevPermissions,
                      [feature]: newPermissions,
                    }));
                  }}
                />
                {index % 2 === 0 && <div className="col-xl-1"></div>}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Utility function to format section titles
  const formatTitle = (text) =>
    text.replace(/_/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());

  return (
    <>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title mb-0">User Maintenance</h2>
      </div>
      <div className="d-card py-45 mb-45">
        <div className="row">
          <Formik
            initialValues={{
              user_name: '',
              user_id: '',
              email: '',
              phone: '',
              country_code: '',
              phone_number: '',
              password: '',
              password_confirmation: '',
              apply_time_restriction: 0,
              time_slots: timeSlots.map((slot, index) => ({
                day: slot.day,
                from: '09:00',
                to: '17:00',
              })),
            }}
            // validationSchema={userMaintenanceValidationSchema}
            onSubmit={handleSubmit}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              setFieldValue,
            }) => (
              <Form>
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                  {/* User Details */}
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'user_name'}
                        type={'text'}
                        required
                        label={'User Name'}
                        placeholder={'Enter User Name'}
                        value={values.user_name}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.user_name && errors.user_name}
                      />
                    </div>
                    <Col md={6} xs={12}>
                      <CustomInput
                        label="User ID"
                        required
                        id="user_id"
                        name={'user_id'}
                        type="text"
                        placeholder="Enter User ID"
                        value={values.user_id}
                        onChange={(e) => {
                          handleUserIDChange(e.target.value); // Call your custom function
                          handleChange(e);
                        }}
                        onBlur={handleBlur}
                        error={
                          (touched.user_id && errors.user_id) || suggestionError
                        }
                        // disabled
                      />
                    </Col>
                    <Col md={6} xs={12}></Col>
                    <Col md={6} xs={12}>
                      {/* Suggestion Dropdown */}
                      {userIdSuggestions?.length > 0 && (
                        <div className="suggestions-dropdown">
                          <p
                            style={{ marginTop: '-1rem' }}
                            className="input-error-message text-danger"
                          >
                            This user ID is taken
                          </p>
                          {loadingSuggestions ? (
                            <p>Suggestions: Loading...</p>
                          ) : (
                            <div className="suggestion-item-wrapper">
                              <div style={{ color: '#333' }} className="me-2">
                                Suggestions:{' '}
                              </div>
                              {userIdSuggestions.map((suggestion, index) => (
                                <p
                                  key={index}
                                  className="suggestion-item mb-0"
                                  onClick={() =>
                                    setFieldValue('user_id', suggestion)
                                  }
                                >
                                  {suggestion},
                                </p>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </Col>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'email'}
                        type={'email'}
                        required
                        label={'Email'}
                        placeholder={'Enter Email'}
                        value={values.email}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.email && errors.email}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-4 inputWrapper">
                      <label className="mainLabel">
                        Contact No
                        <span className="text-danger">*</span>
                      </label>
                      <FastField name="phone_number">
                        {({ field }) => (
                          // Handling the phone number and country code in the field's onChange rather than handleSubmit Function
                          <PhoneInput
                            {...field}
                            international
                            withCountryCallingCode
                            placeholder="Enter Contact Number"
                            className="mainInput"
                            defaultCountry="US"
                            onChange={(value) => {
                              let parsedMobileNumber;
                              if (value?.length > 3) {
                                parsedMobileNumber = parsePhoneNumber(
                                  value,
                                  'US'
                                );
                              }
                              setFieldValue(
                                'phone',
                                parsedMobileNumber?.nationalNumber
                              );
                              setFieldValue(
                                'country_code',
                                parsedMobileNumber
                                  ? `+${parsedMobileNumber.countryCallingCode}`
                                  : ''
                              );
                              setFieldValue('phone_number', value);
                            }}
                            onBlur={() =>
                              handleBlur({ target: { name: 'phone' } })
                            }
                          />
                        )}
                      </FastField>
                      <ErrorMessage
                        name="phone"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'password'}
                        type={'password'}
                        required
                        label={'Password'}
                        placeholder={'Enter password'}
                        value={values.password}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.password && errors.password}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'password_confirmation'}
                        type={'password'}
                        required
                        label={'Re-Confirm'}
                        placeholder={'Confirm password'}
                        value={values.password_confirmation}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.password_confirmation &&
                          errors.password_confirmation
                        }
                      />
                    </div>
                  </div>
                  {/* Time Restriction */}
                  <div className="row mb-4">
                    <div className="col-12 mb-4">
                      <div className="d-flex align-items-center">
                        <label className="toggle-switch">
                          <input
                            type="checkbox"
                            name="apply_time_restriction"
                            id="apply_time_restriction"
                            checked={values.apply_time_restriction}
                            onChange={(e) =>
                              setFieldValue(
                                'apply_time_restriction',
                                e.target.checked ? 1 : 0
                              )
                            }
                          />
                          <span className="toggle-slider"></span>
                        </label>
                        <label
                          htmlFor="apply_time_restriction"
                          className="ms-3 cp user-select-none"
                        >
                          Apply Time Restrictions
                        </label>
                      </div>
                    </div>

                    {/* Time Restriction Table */}
                    {values.apply_time_restriction === 1 && (
                      <div className="col-12 mb-4">
                        <div className="table-responsive">
                          <table className="table">
                            <thead>
                              <tr>
                                <th>Day</th>
                                <th>From Time</th>
                                <th>To Time</th>
                              </tr>
                            </thead>
                            <tbody className="time-slots-table-body">
                              {timeSlots.map((slot, index) => (
                                <tr key={slot.day}>
                                  <td width={'47%'}>{slot.day}</td>
                                  <td width={'23%'}>
                                    <CustomInput
                                      type="time"
                                      name={`time_slots.${index}.from`}
                                      style={{
                                        marginBottom: '0px',
                                        minWidth: '150px',
                                        width: '150px',
                                      }}
                                      value={
                                        values.time_slots[index]?.from || ''
                                      }
                                      onChange={handleChange}
                                      onBlur={handleBlur}
                                      error={
                                        touched.time_slots?.[index]?.from &&
                                        errors.time_slots?.[index]?.from
                                      }
                                    />
                                  </td>
                                  <td width={'23%'}>
                                    <CustomInput
                                      type="time"
                                      name={`time_slots.${index}.to`}
                                      style={{
                                        marginBottom: '0px',
                                        minWidth: '150px',
                                        width: '150px',
                                      }}
                                      value={values.time_slots[index]?.to || ''}
                                      onChange={handleChange}
                                      onBlur={handleBlur}
                                      error={
                                        touched.time_slots?.[index]?.to &&
                                        errors.time_slots?.[index]?.to
                                      }
                                    />
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Access Rights */}
                  <div className="row mb-4">
                    <h3 className="screen-title-body mb-3">Access Rights</h3>
                    {/* Copy From */}
                    <div className="col-12 col-sm-6 mb-45 ">
                      <SearchableSelect
                        name="access_rights_copy_from"
                        label="Copy From"
                        placeholder="Select User"
                        options={getUsersOptions()}
                        value={copyFrom.access_rights_copy_from}
                        onChange={(v) =>
                          setCopyFrom({
                            ...copyFrom,
                            access_rights_copy_from: v.value,
                          })
                        }
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45 d-flex align-items-end">
                      <CustomButton
                        type="button"
                        text={'Copy'}
                        onClick={getUserAccessRights}
                        disabled={isLoadingUserAccessRights}
                        loading={isLoadingUserAccessRights}
                      />
                    </div>
                  </div>
                </div>
                {/* Access Rights Dropdowns*/}
                <div className="col-12 mb-45">
                  <Accordion title="Masters">
                    <PermissionsList
                      key="master"
                      module={masterPermissions}
                      setPermissions={setMasterPermissions}
                    />
                  </Accordion>

                  <Accordion title="Transactions">
                    <PermissionsList
                      key="transaction"
                      module={transactionPermissions}
                      setPermissions={setTransactionPermissions}
                    />
                  </Accordion>

                  <Accordion title="Process">
                    <PermissionsList
                      key="process"
                      module={processPermissions}
                      setPermissions={setProcessPermissions}
                    />
                  </Accordion>

                  <Accordion title="Reports">
                    <PermissionsList
                      key="reports"
                      module={reportsPermissions}
                      setPermissions={setReportPermissions}
                    />
                  </Accordion>

                  <Accordion title="Administration">
                    <PermissionsList
                      key="administration"
                      module={administrationPermissions}
                      setPermissions={setAdministrationPermissions}
                    />
                  </Accordion>

                  <Accordion title="Account Permissions">
                    {/* Copy From */}
                    <div className="row mb-4">
                      <div className="col-12 col-xl-6 mb-45 ">
                        <SearchableSelect
                          name="account_permissions_copy_from"
                          label="Copy From"
                          placeholder="Select User"
                          options={getUsersOptions()}
                          value={copyFrom.account_permissions_copy_from}
                          onChange={(v) =>
                            setCopyFrom({
                              ...copyFrom,
                              account_permissions_copy_from: v.value,
                            })
                          }
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45 d-flex align-items-end">
                        <CustomButton
                          type="button"
                          text={'Copy'}
                          onClick={getUserAccountPermissions}
                          disabled={isLoadingUserAccountPermissions}
                          loading={isLoadingUserAccountPermissions}
                        />
                      </div>
                      <div className="checkbox-wrapper">
                        <label className="checkbox-container">
                          <input
                            checked={values.copy_all}
                            onChange={(v) =>
                              setFieldValue('copy_all', !values.copy_all)
                            }
                            type="checkbox"
                            name="copy_all"
                          />
                          <span className="custom-checkbox"></span>
                          Allow Full Access
                        </label>
                      </div>
                    </div>
                    <div className="row mb-4">
                      <div className="col-12 col-xl-6 mb-45 ">
                        <ChartOfAccountDropdown
                          otherUserAccountPermissions={
                            otherUserAccountPermissions
                          } // Pass permissions to the dropdown
                          onSelectionChange={(selectedItems) => {
                            setAccountFinalPermissions(selectedItems);
                          }}
                        />
                      </div>
                    </div>
                  </Accordion>
                </div>
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                  {/* Form Button */}
                  <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                    <div className="d-flex gap-3 justify-content-center mb-3">
                      {createUserMutation.isLoading ? (
                        <PulseLoader size={11} className="modalLoader" />
                      ) : (
                        <>
                          <CustomButton type="submit" text={'Submit'} />
                          <CustomButton
                            variant={'secondaryButton'}
                            text={'Cancel'}
                            type={'button'}
                            onClick={() => navigate(-1)}
                          />
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default NewUserMaintenance;
