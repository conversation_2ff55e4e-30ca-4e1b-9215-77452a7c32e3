import { Form, Formik } from 'formik';
import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa6';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';

const OutwardRemittanceAllocation = ({
  outwardRemittanceData,
  isDisabled = false,
  setShowAddLedgerModal,
  setShowMissingCurrencyRateModal,
  setShowOutOfScopeModal,
  setCurrencyToSelect,
  setPageState,
}) => {
  const handleSubmit = (values) => {
    console.log(values);
    setPageState('new');
  };

  return (
    <Formik
      initialValues={{
        reference_number: outwardRemittanceData.reference_number,
        selling_no: outwardRemittanceData.selling_no,
        beneficiary: outwardRemittanceData.beneficiary,
        remitter: outwardRemittanceData.remitter,
        ledger: outwardRemittanceData.ledger,
        account: outwardRemittanceData.account,
        against: outwardRemittanceData.against,
        rate: outwardRemittanceData.rate,
        currency_charges: outwardRemittanceData.currency_charges,
        vat_term: outwardRemittanceData.vat_term,
        vat_amount: outwardRemittanceData.vat_amount,
        net_total: outwardRemittanceData.net_total,
        base_rate: outwardRemittanceData.base_rate,
        lcy_amount: outwardRemittanceData.lcy_amount,
        signature: outwardRemittanceData.signature,
      }}
      onSubmit={handleSubmit}
    >
      {({ values, handleChange, handleBlur, setFieldValue }) => (
        <Form>
          <div className="row">
            <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
              <div className="row mb-4">
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'selling_no'}
                    type={'text'}
                    label={'Selling No'}
                    placeholder={'Enter Selling No'}
                    value={values.selling_no}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'reference_number'}
                    type={'text'}
                    disabled={true}
                    label={'Reference Number'}
                    placeholder={'Enter Reference Number'}
                    value={values.reference_number}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'beneficiary'}
                    label={'Beneficiary'}
                    options={[
                      {
                        label: 'Beneficiary 1',
                        value: 'b1',
                      },
                      {
                        label: 'Beneficiary 2',
                        value: 'b2',
                      },
                      {
                        label: 'Add New Beneficiary',
                        value: null,
                      },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Beneficiary'}
                    value={values.beneficiary}
                    onChange={(selected) => {
                      if (
                        selected.label?.toLowerCase()?.startsWith('add new')
                      ) {
                        setShowAddLedgerModal(selected.label?.toLowerCase());
                      } else {
                        setFieldValue('beneficiary', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'remitter'}
                    type={'text'}
                    disabled={true}
                    label={'Remitter'}
                    placeholder={'Enter Remitter'}
                    value={values.remitter}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'tt_amount_usd'}
                    type={'text'}
                    label={'TT Amount USD'}
                    placeholder={'Enter TT Amount USD'}
                    value={values.tt_amount_usd}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'date'}
                    type={'date'}
                    disabled={true}
                    label={'Date'}
                    placeholder={'Enter Date'}
                    value={values.date}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'ledger'}
                    label={'Ledger'}
                    options={[]}
                    isDisabled={isDisabled}
                    placeholder={'Select Ledger'}
                    value={values.ledger}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'account'}
                    label={'Account'}
                    options={[
                      {
                        label: 'Add New PL',
                        value: null,
                      },
                      {
                        label: 'Add New WIC',
                        value: null,
                      },
                      {
                        label: 'Add New GL',
                        value: null,
                      },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Account'}
                    value={values.account}
                    onChange={(selected) => {
                      if (
                        selected.label?.toLowerCase()?.startsWith('add new')
                      ) {
                        setShowAddLedgerModal(selected.label?.toLowerCase());
                      } else {
                        setFieldValue('account', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <SearchableSelect
                    name={'office_location'}
                    label={'Office Location'}
                    options={[]} // Add options as needed
                    placeholder={'Select Office Location'}
                    value={values.office_location}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'usd_commission'}
                    type={'text'}
                    label={'USD Commission'}
                    placeholder={'Enter USD Commission'}
                    value={values.usd_commission}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'buy'}
                    label={'Buy'}
                    options={[
                      {
                        label: 'USD',
                        value: 'usd',
                      },
                      {
                        label: 'EUR',
                        value: 'eur',
                      },
                      {
                        label: 'GBP',
                        value: 'gbp',
                      },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Currency'}
                    value={values.buy}
                    onChange={(selected) => {
                      setFieldValue('buy', selected.value);
                      if (selected.value === 'usd') {
                        setShowMissingCurrencyRateModal(true);
                        setCurrencyToSelect('usd');
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'against'}
                    label={'Against'}
                    options={[
                      {
                        label: 'USD',
                        value: 'usd',
                      },
                      {
                        label: 'EUR',
                        value: 'eur',
                      },
                      {
                        label: 'GBP',
                        value: 'gbp',
                      },
                    ]}
                    isDisabled={isDisabled}
                    placeholder={'Select Currency'}
                    value={values.buy}
                    onChange={(selected) => {
                      setFieldValue('against', selected.value);
                      if (selected.value === 'usd') {
                        setShowMissingCurrencyRateModal(true);
                        setCurrencyToSelect('usd');
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'buy_currency'}
                    type={'text'}
                    label={'Buy Currency'}
                    placeholder={'Enter Buy Currency'}
                    value={values.buy_currency}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-45">
                  <SearchableSelect
                    name={'rate'}
                    label={'Rate'}
                    isDisabled={isDisabled}
                    placeholder={'x | 00000000'}
                    value={values.rate}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'against'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Against'}
                    placeholder={'Currency | 0.00'}
                    value={values.against}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'currency_charges'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Charges'}
                    placeholder={'Currency A | 0.00'}
                    value={values.currency_charges}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <SearchableSelect
                    name={'vat_term'}
                    type={'text'}
                    options={[
                      {
                        label: 'VAT 1',
                        value: 'vat1',
                      },
                      {
                        label: 'Out of Scope',
                        value: 'out_of_scope',
                      },
                    ]}
                    isDisabled={isDisabled}
                    label={'VAT Term'}
                    placeholder={'Select VAT Term'}
                    value={values.vat_term}
                    onChange={(selected) => {
                      if (selected.value === 'out_of_scope') {
                        setShowOutOfScopeModal(true);
                      } else {
                        setFieldValue('vat_term', selected.value);
                      }
                    }}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'vat_amount'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'VAT Amount'}
                    placeholder={'Currency A | 0.00'}
                    value={values.vat_amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'net_total'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Net Total'}
                    placeholder={'Currency A | 0.00'}
                    value={values.net_total}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'base_rate'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'Base Rate'}
                    placeholder={'0.00'}
                    value={values.base_rate}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 col-sm-6 mb-3">
                  <CustomInput
                    name={'lcy_amount'}
                    type={'text'}
                    disabled={isDisabled}
                    label={'LCy Amount'}
                    placeholder={'Currency A | 0.00'}
                    value={values.lcy_amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
                <div className="col-12 mb-3">
                  <CustomInput
                    name={'signature'}
                    label={'Signature'}
                    isDisabled={isDisabled}
                    value={values.signature}
                    rows={6}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </div>
              </div>
            </div>
            <div className="row">
              <div className="col-12 col-lg-6 d-flex justify-content-between mt-3 mb-5">
                <div>
                  <div className="d-inline-block mt-3">
                    <CustomCheckbox
                      label="Print"
                      disabled={isDisabled}
                      style={{ border: 'none', margin: 0 }}
                    />
                  </div>
                </div>
                <div className="d-flex gap-2 voucher-navigation-wrapper">
                  <FaChevronLeft size={24} />
                  <FaChevronRight size={24} />
                </div>
              </div>
            </div>
            {!isDisabled && (
              <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                <CustomButton
                  type={'button'}
                  text={'Save'}
                  onClick={() => {
                    handleSubmit();
                  }}
                />
                <CustomButton
                  variant={'secondaryButton'}
                  text={'Cancel'}
                  type={'button'}
                  onClick={() => {
                    setPageState('new');
                  }}
                />
              </div>
            )}
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default OutwardRemittanceAllocation;
