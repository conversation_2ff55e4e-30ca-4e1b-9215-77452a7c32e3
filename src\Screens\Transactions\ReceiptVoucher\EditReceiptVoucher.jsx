import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ErrorMessage, Form, Formik } from 'formik';
import React, { useEffect, useRef, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import { useNavigate } from 'react-router-dom';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs.jsx';
import CustomButton from '../../../Components/CustomButton';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { showToast } from '../../../Components/Toast/Toast';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import withModal from '../../../HOC/withModal';
import useCurrencyRate from '../../../Hooks/useCurrencyRate.js';
import {
  addReceiptVoucherAttachment,
  deleteReceiptVoucherAttachment,
  getBenefeciariesByAccount,
  getReceiptVoucherListing,
  updateReceiptVoucher,
} from '../../../Services/Transaction/ReceiptVoucher';
import useFormStore from '../../../Stores/FormStore';
import useSettingsStore from '../../../Stores/SettingsStore';
import { isNullOrEmpty, showErrorToast } from '../../../Utils/Utils';

const EditReceiptVoucher = ({
  date,
  state,
  showModal,
  getAccountsByTypeOptions,
  getCOAAccountsByModeOptions,
  currencyOptions,
  vatData,
  isDisabled = false,
  setIsDisabled,
  searchTerm,
  setPageState,
  setSearchTerm,
  setShowAddLedgerModal,
  setCurrencyToSelect,
  setShowMissingCurrencyRateModal,
  newlyCreatedAccount,
  newlyCreatedBeneficiary,
  lastVoucherNumbers,
  updatePrintSetting,
}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const formikRef = useRef();

  // Access the form store
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    clearFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
  } = useFormStore();
  const formId = 'edit-receipt-voucher'; // Unique identifier for this form

  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  const [showVatOutOfScopeModal, setShowVatOutOfScopeModal] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [selectedLedgerAccount, setSelectedLedgerAccount] = useState(null);
  const [hasShownModal, setHasShownModal] = useState(false);
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);
  const [outOfScope, setOutOfScope] = useState('');
  const [specialCommissionValues, setSpecialCommissionValues] = useState({});
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);

  const {
    data: { data: [receiptVoucherData] = [] } = {},
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['receiptVoucher', searchTerm],
    queryFn: () => getReceiptVoucherListing({ search: searchTerm }),
  });

  // Fetch currency rate for the selected Currency
  const { data: currencyRate, isLoading: isLoadingCurrencyRate } =
    useCurrencyRate(selectedCurrency, date);

  // Load saved form if present
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);

    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);
      let specialCommissionData = { ...savedValues };

      specialCommissionData.ledger = [
        { label: 'PL', value: 'party' },
        { label: 'GL', value: 'general' },
        { label: 'WIC', value: 'walkin' },
      ].find((x) => savedValues.ledger == x.value);
      specialCommissionData.account_id = getAccountsByTypeOptions(
        specialCommissionData?.ledger?.value
      ).find((x) => x.value == savedValues?.account_id);

      specialCommissionData.currency = currencyOptions.find(
        (x) => x.value == savedValues?.currency_id
      );

      setSpecialCommissionValues(specialCommissionData);
      formikRef.current.setValues(savedValues);
      setIsDisabled(false);
    } else if (lastPage !== 'special-commission') {
      if (hasFormValues('special-commission')) {
        setAddedSpecialCommissionValues(getFormValues('special-commission'));
      }
    }
    // Clear lastVisitedPage so it doesn't persist beyond one use
    clearLastVisitedPage(formId);
  }, []);

  // Update special commission values when receipt voucher data changes
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);
    let rv = receiptVoucherData?.receipt_vouchers;
    if (rv) {
      setOutOfScope(rv?.out_of_scope);
      setSpecialCommissionValues({
        ledger: {
          value: rv.ledger,
          label:
            rv.ledger === 'party'
              ? 'PL'
              : rv.ledger === 'general'
              ? 'GL'
              : 'WIC',
        },
        account: {
          value: rv.account_id,
          label: rv.account_name || '',
        },
        amount: rv.amount,
        currency: {
          value: rv.amount_account_id?.id,
          label: rv.amount_account_id?.currency_code || '',
        },
        commission_type: {
          value: rv.commission_type,
          label: rv.commission_type,
        },
        date: rv.date,
      });

      if (rv?.special_commission && lastPage !== 'special-commission') {
        // Save the SC form values
        saveFormValues('special-commission', {
          ...rv?.special_commission,
          ledger: rv?.special_commission?.account_type,
          distributions: [...rv?.special_commission?.commission_distribution],
        });
      }
    }
  }, [receiptVoucherData]);

  useEffect(() => {
    if (lastVoucherNumbers?.current) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        current: lastVoucherNumbers?.current,
      }));
    }
    if (date) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        date: date,
      }));
      // Set due_date to date if mode is Bank
      if (formikRef.current?.values.mode === 'Bank') {
        formikRef.current.setFieldValue('due_date', date);
      }
    }
  }, [lastVoucherNumbers?.current, date]);

  // Get Benefeciaries from selected Ledger+Account
  const {
    data: beneficiaryAccounts,
    isLoading: isLoadingBeneficiary,
    isError: isErrorBeneficiary,
    error: errorBeneficiary,
  } = useQuery({
    queryKey: ['beneficiaries', selectedLedgerAccount],
    queryFn: () => getBenefeciariesByAccount(selectedLedgerAccount),
    enabled: !!selectedLedgerAccount,
  });

  // Make options array from the benfeciary queries call
  const getBeneficiaryOptions = (account_id) => {
    if (!account_id) {
      return [{ label: 'Select Ledger', value: null, isDisabled: true }];
    }

    const data = beneficiaryAccounts;
    const loading = isLoadingBeneficiary;
    const error = isErrorBeneficiary;
    const errorMessage = errorBeneficiary;

    if (loading) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }

    if (error) {
      console.error('Unable to fetch beneficiaries', errorMessage);
      return [{ label: 'Unable to fetch beneficiaries', value: null }];
    }
    let options =
      data?.map((x) => ({
        value: x?.id,
        label: x?.title,
      })) || [];

    options.push({
      label: `Add New Beneficiary`,
      value: null,
    });

    return options;
  };

  // Show Missing currency rate modal if rate not present
  useEffect(() => {
    if (
      selectedCurrency &&
      currencyRate &&
      !currencyRate?.rate &&
      !hasShownModal
    ) {
      formikRef.current.setFieldValue('currency_id', '');
      setCurrencyToSelect(selectedCurrency);
      setShowMissingCurrencyRateModal(true);
      setHasShownModal(true);
    }
  }, [selectedCurrency, currencyRate?.rate, hasShownModal]);

  const handleCancel = () => {
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    // Clear saved form values when resetting
    clearFormValues(formId);
    clearFormValues('special-commission');
    setAddedSpecialCommissionValues(null);
    setPageState('view');
  };

  const updateReceiptVoucherMutation = useMutation({
    mutationFn: ({ searchTerm, payload }) =>
      updateReceiptVoucher(searchTerm, payload),
    onSuccess: (data) => {
      showToast('Receipt Voucher Updated!', 'success');
      if (getPrintSettings('receipt_voucher')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      queryClient.invalidateQueries(['receiptVoucherListing']);
      queryClient.invalidateQueries(['receiptVoucher', searchTerm]);
      handleCancel();
    },
    onError: (error) => {
      console.error('Error creating Receipt Voucher', error);
      if (
        error.message.toLowerCase() ==
        'receipt voucher limit reached for this branch.'
      ) {
        showModal(
          'Cannot Create',
          'The maximum number of receipt vouchers has been reached. To create new transactions, please increase the transaction number count in the Transaction Number Register.',
          null,
          'error'
        );
      } else {
        showErrorToast(error);
      }
    },
  });

  const handleSubmit = async () => {
    if (!formikRef.current) return;
    // Validate the form
    const errors = await formikRef.current.validateForm();
    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show errors
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return; // Do not submit if there are errors
    }
    const formValues = formikRef.current.values;
    let payload = {
      date,
      ...formValues,
      ...(formValues.vat_terms.startsWith('A small popup') && {
        out_of_scope_reason: outOfScope,
      }),
      vat_amount: vatData.vatType?.vat_percentage
        ? (formValues.amount * vatData.vatType?.vat_percentage) / 100
        : !isNaN(formValues.vat_terms)
        ? (formValues.amount * formValues.vat_terms) / 100
        : '',
      ...(addedSpecialCommissionValues
        ? { special_commission: addedSpecialCommissionValues }
        : {}),
    };
    updateReceiptVoucherMutation.mutate({ searchTerm, payload });
  };
  const handleVatOutOfScope = (values) => {
    setOutOfScope(values.out_of_scope);
    setShowVatOutOfScopeModal(false);
  };
  const handleNavigateToSpecialCommissionPage = () => {
    // Check if required fields are filled
    const requiredFields = [
      'ledger',
      'account_id',
      'amount',
      'currency_id',
      'commission_type',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    if (missingFields.length > 0) {
      // Set touched for all required fields to show errors
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      return;
    }
    // Todo

    // If all required fields are filled, proceed
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, formikRef.current.values);
      setLastVisitedPage(formId, 'special-commission');
    }

    // Check if we already have special commission data
    const hasExistingCommission = !!addedSpecialCommissionValues;

    setPageState('edit');
    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'rv',
        pageState: 'edit',
        searchTerm: searchTerm,
        values: {
          rvValues: specialCommissionValues,
          isEdit: hasExistingCommission,
        },
      },
    });
  };

  const getVATTermsOptions = () => {
    if (vatData.isLoadingVatType)
      return [
        {
          label: 'Loading...',
          value: '',
        },
      ];
    if (vatData.isErrorVatType) {
      console.error('Unable to fetch VAT Terms', vatData.errorMessage);
      return [{ label: 'Unable to fetch VAT Terms', value: null }];
    }
    return vatData?.vatType?.vats?.map((item) => ({
      label: `${item.title}${
        !isNaN(parseFloat(item.percentage)) ? ' - ' + item.percentage + '%' : ''
      }`,
      value: item.percentage,
    }));
  };

  if (isError) {
    console.log(error);
  }

  if (isLoading) {
    return (
      <div className="d-card ">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <div className="row mb-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            ledger: receiptVoucherData?.receipt_vouchers.ledger || '',
            account_id: receiptVoucherData?.receipt_vouchers.account_id || '',
            received_from:
              receiptVoucherData?.receipt_vouchers.received_from?.id || '',
            mode: receiptVoucherData?.receipt_vouchers.mode || '',
            mode_account_id:
              receiptVoucherData?.receipt_vouchers.mode_account_id?.id || '',
            party_bank: receiptVoucherData?.receipt_vouchers.party_bank || '',
            cheque_number:
              receiptVoucherData?.receipt_vouchers.cheque_number || '',
            due_date: receiptVoucherData?.receipt_vouchers.due_date || '',
            narration: receiptVoucherData?.receipt_vouchers.narration || '',

            amount: receiptVoucherData?.receipt_vouchers.amount || '',
            currency_id:
              receiptVoucherData?.receipt_vouchers.amount_account_id?.id || '',
            commission_type:
              receiptVoucherData?.receipt_vouchers.commission_type || '',
            commission: receiptVoucherData?.receipt_vouchers.commission || '',
            vat_terms: receiptVoucherData?.receipt_vouchers.vat_terms || '',
            vat_percentage:
              receiptVoucherData?.receipt_vouchers.vat_percentage || '',
            vat_amount: receiptVoucherData?.receipt_vouchers.vat_amount || '',
            net_total: receiptVoucherData?.receipt_vouchers.net_total || '',
            comment: receiptVoucherData?.receipt_vouchers.comment || '',
          }}
          validate={(values) => {
            const errors = {};

            // Required fields for special commission
            if (!values.ledger) errors.ledger = 'Ledger is required';
            if (!values.account_id) errors.account_id = 'Account is required';
            if (!values.amount) errors.amount = 'Amount is required';
            if (!values.currency_id)
              errors.currency_id = 'Currency is required';
            // if (!values.commission_type)
            //   errors.commission_type = 'Commission Type is required';

            return errors;
          }}
        >
          {({
            values,
            touched,
            errors,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // --- VAT Calculation useEffect inside Formik render ---
            React.useEffect(() => {
              let commissionAmount =
                addedSpecialCommissionValues?.total_commission ??
                values.commission_amount;

              let vatPercentage =
                vatData.vatType?.vat_percentage ||
                (!isNaN(values.vat_terms) ? values.vat_terms : 0);

              let vatAmount =
                commissionAmount && vatPercentage
                  ? (commissionAmount * vatPercentage) / 100
                  : '';

              setFieldValue('vat_amount', vatAmount);
            }, [
              addedSpecialCommissionValues?.total_commission,
              values.commission_amount,
              values.vat_terms,
              vatData.vatType?.vat_percentage,
              setFieldValue,
            ]);
            // --- End VAT Calculation useEffect ---
            return (
              <Form>
                <div className="row">
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                    <div className="row">
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'ledger'}
                          label={'Ledger'}
                          options={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          isDisabled={isDisabled}
                          placeholder={'Select Ledger'}
                          value={values.ledger}
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('ledger', selected.value);
                              setSpecialCommissionValues((prev) => ({
                                ...prev,
                                ledger: selected,
                                account_id: '',
                              }));
                            }
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="ledger"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'account_id'}
                          label={'Account'}
                          options={getAccountsByTypeOptions(values.ledger)}
                          isDisabled={isDisabled}
                          placeholder={'Select Account'}
                          value={values.account_id || newlyCreatedAccount?.id}
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('account_id', selected.value);
                              setSpecialCommissionValues((prev) => ({
                                ...prev,
                                account: selected,
                              }));
                            }
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="account_id"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'received_from'}
                          label={'Received From'}
                          options={getBeneficiaryOptions(values.account_id)}
                          isDisabled={isDisabled}
                          placeholder={'Select Received From'}
                          value={
                            values.received_from || newlyCreatedBeneficiary?.id
                          }
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('received_from', selected.value);
                            }
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      {/* MergeSelect */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'mode'}
                          label={'Mode'}
                          options={[
                            {
                              label: 'Cash',
                              value: 'Cash',
                            },
                            {
                              label: 'Bank',
                              value: 'Bank',
                            },
                            {
                              label: 'PDC',
                              value: 'PDC',
                            },
                            {
                              label: 'Online',
                              value: 'Online',
                            },
                          ]}
                          isDisabled={isDisabled}
                          placeholder={'Select Mode'}
                          value={values.mode}
                          onChange={(selected) => {
                            setFieldValue('mode', selected.value);
                            if (selected.value === 'Online') {
                              setFieldValue('cheque_no', '');
                            } else if (selected.value == 'Cash') {
                              setFieldValue('cheque_no', '');
                              setFieldValue('party_bank', '');
                            } else if (selected.value == 'Bank') {
                              setFieldValue('due_date', date);
                            } else if (selected.value == 'PDC') {
                              setFieldValue(
                                'due_date',
                                new Date(
                                  new Date(date).setDate(
                                    new Date(date).getDate() + 1
                                  )
                                )
                                  .toISOString()
                                  .split('T')[0]
                              );
                            }
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'mode_account_id'}
                          label={'Paid To Account'}
                          options={getCOAAccountsByModeOptions(values.mode)}
                          isDisabled={isDisabled}
                          placeholder={'Select Account'}
                          value={values.mode_account_id}
                          onChange={(selected) => {
                            setFieldValue('mode_account_id', selected.value);
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      {values.mode !== 'Cash' && (
                        <div className="col-12 col-sm-6 mb-3">
                          <CustomInput
                            name={'party_bank'}
                            label={'Party Bank'}
                            disabled={isDisabled}
                            placeholder={'Enter Party Bank'}
                            value={values.party_bank}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.party_bank && errors.party_bank}
                          />
                        </div>
                      )}
                      {(values.mode === 'Bank' || values.mode === 'PDC') && (
                        <div className="col-12 col-sm-6 mb-3">
                          <CustomInput
                            name={'cheque_number'}
                            label={'Cheque Number'}
                            disabled={isDisabled}
                            placeholder={'Enter Cheque Number'}
                            value={values.cheque_number}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                              touched.cheque_number && errors.cheque_number
                            }
                          />
                        </div>
                      )}
                      {!(
                        values.mode === 'Cash' || values.mode === 'Online'
                      ) && (
                        <div className="col-12 col-sm-6 mb-3">
                          {/* If Mode is PDC then due date should be greater than current date */}
                          {/* If Mode is Bank then the date should ONLY be the current (voucher) date */}
                          {/* Otherwise, the date is disabled */}
                          <CustomInput
                            name={'due_date'}
                            label={'Due Date'}
                            type={'date'}
                            min={
                              values.mode == 'PDC'
                                ? new Date(
                                    new Date(date).setDate(
                                      new Date(date).getDate() + 1
                                    )
                                  )
                                    .toISOString()
                                    .split('T')[0]
                                : new Date(date).toISOString().split('T')[0]
                            }
                            {...(values.mode == 'Bank' && {
                              max: new Date(date).toISOString().split('T')[0],
                            })}
                            disabled={
                              isDisabled ||
                              !(values.mode === 'Bank' || values.mode == 'PDC')
                            }
                            value={values.due_date}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.due_date && errors.due_date}
                          />
                        </div>
                      )}
                      <div className="col-12 mb-3">
                        <CustomInput
                          name={'narration'}
                          label={'Narration'}
                          type={'textarea'}
                          rows={1}
                          placeholder={'Enter Narration'}
                          disabled={isDisabled}
                          value={values.narration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.narration && errors.narration}
                        />
                      </div>
                      {/* MergeSelect */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'currency_id'}
                          label={'Currency'}
                          options={currencyOptions}
                          isDisabled={isDisabled}
                          placeholder={'Select Currency'}
                          value={values.currency_id}
                          onChange={(selected) => {
                            setSelectedCurrency(selected.value);
                            setHasShownModal(false);
                            setFieldValue('currency_id', selected.value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              currency: selected,
                            }));
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="currency_id"
                          component="div"
                          className="input-error-message text-danger"
                        />
                        {isLoadingCurrencyRate && (
                          <p className="m-0 position-absolute primary-color-text">
                            Fetching currency rate...
                          </p>
                        )}
                      </div>
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name={'amount'}
                          label={'Amount'}
                          type={'number'}
                          disabled={isDisabled}
                          placeholder={'Enter Amount'}
                          value={values.amount}
                          onChange={(v) => {
                            handleChange(v);
                            let commission = parseFloat(values.commission || 0);
                            let amount = parseFloat(v.target.value || 0);
                            let vat = vatData.vatType?.vat_percentage
                              ? (amount * vatData.vatType?.vat_percentage) / 100
                              : !isNaN(values.vat_terms)
                              ? (amount * values.vat_terms) / 100
                              : 0;

                            let value =
                              Math.round(
                                (amount +
                                  (commission / 100) * amount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000;
                            setFieldValue('net_total', value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              amount,
                            }));
                          }}
                          onBlur={handleBlur}
                          error={touched.amount && errors.amount}
                        />
                      </div>

                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'commission_type'}
                          label={'Commission Type'}
                          options={[
                            {
                              label: 'Income',
                              value: 'Income',
                            },
                            {
                              label: 'Expense',
                              value: 'Expense',
                            },
                          ]}
                          isDisabled={isDisabled}
                          placeholder={'Select Commission Type'}
                          value={values.commission_type}
                          onChange={(selected) => {
                            setFieldValue('commission_type', selected.value);
                            setSpecialCommissionValues((prev) => ({
                              ...prev,
                              commission_type: selected.value,
                            }));
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="commission_type"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                      <div className="col-0 col-sm-6 mb-0 mb-sm-4" />

                      <div className="col-12 col-sm-6 mb-3">
                        {/*<CustomInput*/}
                        {/*  name={'commission'}*/}
                        {/*  label={'Commission Percentage'}*/}
                        {/*  type={'number'}*/}
                        {/*  min={0}*/}
                        {/*  max={100}*/}
                        {/*  disabled={*/}
                        {/*    isDisabled ||*/}
                        {/*    addedSpecialCommissionValues?.total_commission*/}
                        {/*  }*/}
                        {/*  placeholder={'Enter Commission %'}*/}
                        {/*  value={values.commission}*/}
                        {/*  onChange={(v) => {*/}
                        {/*    handleChange(v);*/}
                        {/*    if (v.target.value < 0) {*/}
                        {/*      return;*/}
                        {/*    }*/}
                        {/*    let commission = parseFloat(v.target.value || 0);*/}
                        {/*    let amount = parseFloat(values.amount || 0);*/}
                        {/*    let vat = vatData.vatType?.vat_percentage*/}
                        {/*      ? (amount * vatData.vatType?.vat_percentage) / 100*/}
                        {/*      : !isNaN(values.vat_terms)*/}
                        {/*      ? (amount * values.vat_terms) / 100*/}
                        {/*      : 0;*/}

                        {/*    setFieldValue(*/}
                        {/*      'net_total',*/}
                        {/*      Math.round(*/}
                        {/*        (amount +*/}
                        {/*          (commission / 100) * amount +*/}
                        {/*          vat +*/}
                        {/*          Number.EPSILON) **/}
                        {/*          1000000*/}
                        {/*      ) / 1000000*/}
                        {/*    );*/}
                        {/*    setFieldValue(*/}
                        {/*      'commission_amount',*/}
                        {/*      (commission / 100) * amount*/}
                        {/*    );*/}
                        {/*  }}*/}
                        {/*  onBlur={handleBlur}*/}
                        {/*/>*/}

                        <CombinedInputs
                          label="Commission Percentage"
                          type1="input"
                          type2="input"
                          name1="commission"
                          name2="commission_amount"
                          value1={values.commission}
                          value2={values.commission_amount}
                          isDisabled={
                            isDisabled ||
                            addedSpecialCommissionValues?.total_commission
                          }
                          handleBlur={handleBlur}
                          placeholder1="Enter Commission %"
                          placeholder2="Commission Amount"
                          inputType1="number"
                          inputType2="text"
                          className1="commission"
                          className2="commission-amount"
                          inputProps1={{
                            min: 0,
                            max: 100,
                          }}
                          inputProps2={
                            {
                              // readOnly: true,
                            }
                          }
                          onChange1={(v) => {
                            handleChange(v);
                            if (v.target.value < 0) {
                              return;
                            }
                            let commission = parseFloat(v.target.value || 0);
                            let amount = parseFloat(values.amount || 0);
                            let commissionAmount = (commission / 100) * amount;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue(
                              'commission_amount',
                              commissionAmount
                            );
                          }}
                          onChange2={(e) => {
                            handleChange(e);
                            let commissionAmount = parseFloat(
                              e.target.value || 0
                            );
                            let amount = parseFloat(values.amount || 0);
                            let commission =
                              amount !== 0
                                ? (commissionAmount / amount) * 100
                                : 0;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue('commission', commission);
                          }}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'commission_amount'}
                          label={'Commission Amount'}
                          type={'text'}
                          disabled={
                            isDisabled ||
                            addedSpecialCommissionValues?.total_commission
                          }
                          placeholder={'Commission Amount'}
                          value={values.commission_amount}
                          onChange={(e) => {
                            handleChange(e);
                            let commissionAmount = parseFloat(
                              e.target.value || 0
                            );
                            let amount = parseFloat(values.amount || 0);
                            let commission =
                              amount !== 0
                                ? (commissionAmount / amount) * 100
                                : 0;
                            let vat = vatData.vatType?.vat_percentage
                              ? (commissionAmount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (commissionAmount * values.vat_terms) / 100
                              : 0;

                            setFieldValue(
                              'net_total',
                              Math.round(
                                (amount +
                                  commissionAmount +
                                  vat +
                                  Number.EPSILON) *
                                  1000000
                              ) / 1000000
                            );
                            setFieldValue('commission', commission);
                          }}
                          onBlur={handleBlur}
                        />
                      </div>
                      {vatData?.vatType?.vat_type === 'variable' && (
                        <div className="col-12 col-sm-6 mb-45">
                          <SearchableSelect
                            name={'vat_terms'}
                            label={'VAT %'}
                            options={getVATTermsOptions()}
                            isDisabled={isDisabled}
                            placeholder={'Select VAT %'}
                            value={values.vat_terms}
                            onChange={(selected) => {
                              if (
                                selected.value.startsWith(
                                  'A small popup will appear'
                                )
                              ) {
                                setShowVatOutOfScopeModal(true);
                              } else {
                                let commission = parseFloat(
                                  values.commission || 0
                                );
                                let amount = parseFloat(
                                  values.amount ??
                                    getFormValues('special-commission')
                                      ?.total_commission ??
                                    0
                                );
                                let commissionAmount =
                                  (commission / 100) * amount;
                                let vat = vatData.vatType?.vat_percentage
                                  ? (commissionAmount *
                                      vatData.vatType?.vat_percentage) /
                                    100
                                  : !isNaN(selected.value)
                                  ? (commissionAmount * selected.value) / 100
                                  : '';

                                if (
                                  getFormValues('special-commission')
                                    ?.total_commission
                                ) {
                                  console.log(
                                    'commissionAmount',
                                    commissionAmount
                                  );
                                  console.log('vat', vat);
                                }
                                setFieldValue(
                                  'net_total',
                                  Math.round(
                                    (amount +
                                      commissionAmount +
                                      (vat || 0) +
                                      Number.EPSILON) *
                                      1000000
                                  ) / 1000000
                                );
                              }
                              setFieldValue('vat_terms', selected.value);
                            }}
                            onBlur={handleBlur}
                          />
                        </div>
                      )}
                      {vatData?.vatType?.vat_type === 'fixed' && (
                        <div className="col-12 col-sm-6 mb-3">
                          <CustomInput
                            name={'vat_percentage'}
                            label={'VAT %'}
                            type={'number'}
                            disabled={true}
                            placeholder={'Enter VAT Percentage'}
                            value={
                              vatData.vatType?.vat_percentage
                                ? vatData.vatType?.vat_percentage
                                : !isNaN(values.vat_terms)
                                ? values.vat_terms
                                : 0
                            }
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                      )}
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'vat_amount'}
                          label={'VAT Amount'}
                          type={'text'}
                          disabled={true}
                          placeholder={'Enter VAT Amount'}
                          value={
                            vatData.isLoadingVatType
                              ? 'Loading...'
                              : vatData.vatType?.vat_percentage
                              ? (values.amount *
                                  vatData.vatType?.vat_percentage) /
                                100
                              : !isNaN(values.vat_terms)
                              ? (values.amount * values.vat_terms) / 100
                              : 0
                          }
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <CustomInput
                          name={'net_total'}
                          label={'Net Total'}
                          type={'number'}
                          disabled={true}
                          placeholder={'Enter Net Total'}
                          value={values.net_total}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </div>
                      <div className="col-12 mb-3">
                        <CustomInput
                          name={'comment'}
                          label={'Comment'}
                          type={'textarea'}
                          rows={1}
                          placeholder={'Enter Comment'}
                          disabled={isDisabled}
                          value={values.comment}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.comment && errors.comment}
                        />
                      </div>
                    </div>
                    <div className="d-flex mb-5">
                      <CustomButton
                        type={'button'}
                        onClick={handleNavigateToSpecialCommissionPage}
                        text={`${state ? 'Edit' : 'Add'} Special Commission`}
                        disabled={!!values.commission || isDisabled}
                      />
                    </div>
                    {!isNullOrEmpty(
                      receiptVoucherData?.receipt_vouchers?.special_commission
                    ) ? (
                      <p
                        className={`fs-5 ${
                          receiptVoucherData?.special_commission_text.includes(
                            'receivable'
                          )
                            ? 'text-success'
                            : 'text-danger'
                        }`}
                      >
                        {receiptVoucherData?.special_commission_text}
                      </p>
                    ) : null}
                  </div>
                  <div className="col-0  col-xxl-2" />
                  {!isDisabled && (
                    <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                      <div className="row">
                        {/* Right side cards */}
                        <div
                          className="col-12 mb-5"
                          style={{ maxWidth: '350px' }}
                        >
                          {/* <AccountBalanceCard />
                        <ExchangeRatesCard rates={MOCK_EXCHANGE_RATES} /> */}
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="d-flex flex-wrap justify-content-start mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={() => {}}
                      />
                      <CustomCheckbox
                        label="Print"
                        checked={getPrintSettings('receipt_voucher')}
                        onChange={(e) => {
                          updatePrintSetting(
                            'receipt_voucher',
                            e.target.checked
                          );
                        }}
                        style={{ border: 'none', margin: 0 }}
                      />
                    </div>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Save', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleCancel,
            variant: 'secondaryButton',
          },
        ]}
        loading={updateReceiptVoucherMutation.isPending}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={receiptVoucherData}
          deleteService={deleteReceiptVoucherAttachment}
          uploadService={addReceiptVoucherAttachment}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          queryToInvalidate={['receiptVoucher', searchTerm]}
        />
      </CustomModal>

      {/* VAT Out Of Scope Modal  */}
      <CustomModal
        show={showVatOutOfScopeModal}
        close={() => {
          formikRef.current.values.vat_terms = '';
          setShowVatOutOfScopeModal(false);
        }}
        hideClose={true}
      >
        <div className="text-center mb-3 mt-5">
          <h4 className="modalTitle px-5">Out Of Scope</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ out_of_scope: outOfScope }}
            onSubmit={handleVatOutOfScope}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'out_of_scope'}
                    type={'textarea'}
                    required
                    label={'Reason'}
                    r
                    rows={1}
                    value={values.out_of_scope}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.out_of_scope && errors.out_of_scope}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Submit'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => {
                        setShowVatOutOfScopeModal(false);
                        formikRef.current.values.vat_terms = '';
                      }}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default withModal(EditReceiptVoucher);
