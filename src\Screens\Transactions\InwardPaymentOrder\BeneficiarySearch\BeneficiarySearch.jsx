import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import { beneficiarySearchHeaders } from '../../../../Utils/Constants/TableHeaders';
import { MOCK_BENEFICIARY_DATA } from '../../../../Mocks/MockData';

const BeneficiarySearch = () => {
  usePageTitle('Beneficiary Search');
  const tableData = MOCK_BENEFICIARY_DATA;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <h2 className="screen-title mb-3">Beneficiary Search</h2>
      <Row>
        <Col xs={12}>
          <CustomTable
            hideItemsPerPage
            headers={beneficiarySearchHeaders}
            isLoading={isLoading}
            isPaginated={false}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={beneficiarySearchHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.name}</td>
                    <td>{item.address}</td>
                    <td>{item.telephoneNumber}</td>
                    <td>{item.mobileNumber}</td>
                    <td>{item.idType}</td>
                    <td>{item.idNumber}</td>
                    <td>{item.expiryDate}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};
export default BeneficiarySearch;
