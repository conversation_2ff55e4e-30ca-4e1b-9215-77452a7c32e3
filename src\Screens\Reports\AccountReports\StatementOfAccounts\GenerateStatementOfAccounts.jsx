import { ErrorMessage, Form, Formik } from 'formik';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../../Components/CustomButton';
import CustomInput from '../../../../Components/CustomInput';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';
import CustomSelect from '../../../../Components/CustomSelect';
import { isNullOrEmpty } from '../../../../Utils/Utils';
import { useQuery } from '@tanstack/react-query';
import { getAccountType } from '../../../../Services/Masters/BeneficiaryRegister';
import CustomCheckbox from '../../../../Components/CustomCheckbox/CustomCheckbox';

const GenerateStatementOfAccounts = () => {
  const navigate = useNavigate();
  const [type, setType] = useState('');

  const handleSubmit = (values) => {
    console.log('Form Values:', values);
    navigate('generated');
  };

  // Get Account Types
  const {
    data: accountTypes,
    isLoading: accountTypeLoading,
    isError: accountTypeError,
    error: accountError,
  } = useQuery({
    queryKey: ['accountType', type],
    queryFn: () => getAccountType(type),
    enabled: !!type,
    refetchOnWindowFocus: false,
    retry: 1,
  });
  // Function to fetch accountType and show loading/error if api fails
  const getAccountTypeOptions = () => {
    if (!type && isNullOrEmpty(accountTypes)) {
      return [{ label: 'Select Type First', value: null }];
    } else if (type && accountTypeLoading) {
      return [{ label: `Loading...`, value: null }];
    } else if (!accountTypeLoading && !accountTypeError) {
      if (isNullOrEmpty(accountTypes)) {
        return [{ label: `No Accounts for type ${type}`, value: null }];
      }
      let options = accountTypes?.map((x) => ({
        value: x.id,
        label: x.name,
      }));
      options.unshift({ label: 'Select Account', value: null, disabled: true });
      return options;
    } else {
      if (accountTypeError) {
        console.error('Unable to fetch account Type', accountError);
      }
      return [{ label: 'Unable to fetch account Type', value: null }];
    }
  };

  return (
    <section>
      <div className="d-flex gap-3 justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">Statement of Account</h2>
      </div>
      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <Formik
              initialValues={{
                account: '',
                type: '',
                currency: '',
                period_type: '',
                period_from: '',
                period_to: '',
                number_of_days: '',
                show_base_value: '',
              }}
              onSubmit={handleSubmit}
            >
              {({ values, handleChange, handleBlur, setFieldValue }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-45 d-flex flex-column flex-xl-row">
                      <div className="beneficiary-inline-select flex-shrink-0">
                        <CustomSelect
                          name="type"
                          className={'mainInput'}
                          label={'Account Type'}
                          required
                          defaultValue={'Select Type'}
                          fullWidth={true}
                          options={[
                            {
                              label: 'Select Type',
                              value: null,
                              disabled: true,
                            },
                            {
                              label: 'Party',
                              value: 'party',
                            },
                            {
                              label: 'Walk-in Customer',
                              value: 'walkin',
                            },
                          ]}
                          onChange={(v) => {
                            setFieldValue('type', v.target.value);
                            setType(v.target.value);
                          }}
                        />
                        <ErrorMessage
                          name="type"
                          component="div"
                          className="text-danger position-absolute"
                        />
                      </div>
                      <div className="beneficiary-inline-select align-slef-auto align flex-grow-1">
                        <CustomSelect
                          name="account"
                          className={'mainInput'}
                          label={'Account'}
                          required
                          fullWidth={true}
                          options={getAccountTypeOptions()}
                          onChange={(v) =>
                            setFieldValue('account', v.target.value)
                          }
                        />
                        <ErrorMessage
                          name="account"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        label="Currency"
                        name="currency"
                        options={[{ value: 'all', label: 'All' }]}
                        value={values.currency}
                        onChange={(v) => setFieldValue('currency', v.value)}
                        placeholder="Select Currency"
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        label="Period Type"
                        name="period_type"
                        options={[
                          {
                            value: 'bank_dates',
                            label: 'Bank Dates (Full Reports)',
                          },
                          {
                            value: 'entry_dates',
                            label: 'Entry Dates (Partial Reports)',
                          },
                        ]}
                        value={values.period_type}
                        onChange={(v) => setFieldValue('period_type', v.value)}
                        placeholder="Select Period Type"
                      />
                    </div>
                    <div className="col-12 col-sm-6">
                      <div className="row flex-wrap">
                        <label htmlFor="period_to">Period</label>
                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="period_to"
                            type="date"
                            value={values.period_to}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                        <div className="col-12 col-xl-6 mb-3">
                          <CustomInput
                            name="period_to"
                            type="date"
                            value={values.period_to}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        label="Number of Days"
                        name="number_of_days"
                        type="text"
                        placeholder="From"
                        value={values.number_of_days}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <CustomCheckbox
                        style={{ border: 'none', marginBottom: 0 }}
                        checked={values.show_base_value}
                        label={'Show Base Value'}
                        onChange={() =>
                          setFieldValue(
                            'show_base_value',
                            !values.show_base_value
                          )
                        }
                      />
                    </div>
                  </div>
                  <div className="d-flex mb-4">
                    <CustomButton type="submit" text="Generate" />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GenerateStatementOfAccounts;
