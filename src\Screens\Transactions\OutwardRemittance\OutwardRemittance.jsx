import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import { FaMagnifyingGlass, FaPaperclip } from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import BeneficiaryRegisterForm from '../../../Components/BeneficiaryRegisterForm/BeneficiaryRegisterForm';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import ChequeDetailForm from '../../../Components/ChequeDetailForm/ChequeDetailForm';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import {
  outwardRemittanceData,
  supportLogsData,
} from '../../../Mocks/MockData';
import OutwardRemittanceAdd from './OutwardRemittanceAdd';
import OutwardRemittanceView from './OutwardRemittanceView';
import OutwardRemittanceEdit from './OutwardRemittanceEdit';
import { isNullOrEmpty } from '../../../Utils/Utils';
import OutwardRemittanceTable from './OutwardRemittanceTable';
import OutwardRemittanceAllocation from './OutwardRemittanceAllocation';
import BackButton from '../../../Components/BackButton';

const OutwardRemittance = () => {
  usePageTitle('Outward Remittance');
  const navigate = useNavigate();

  // ['new', 'view', 'edit', 'allOutwardRemittances', 'allocation']
  const [pageState, setPageState] = useState('new');

  const [isDisabled, setIsDisabled] = useState(true);
  const [search, setSearch] = useState('');
  const [valueDate, setValueDate] = useState('');
  const [date, setDate] = useState('');
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [addedAttachment, setAddedAttachment] = useState(null);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState(false);
  const [showOutOfScopeModal, setShowOutOfScopeModal] = useState(false);
  const [showAddChequeDetailModal, setShowAddChequeDetailModal] =
    useState(false);
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new beneficiary':
        return (
          <BeneficiaryRegisterForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  return (
    <div>
      <div
        className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-45"
        style={{ height: 43 }}
      >
        <div>
          {pageState == 'allOutwardRemittances' && (
            <BackButton
              handleBack={() => {
                setPageState('new');
              }}
            />
          )}
          <h2 className="screen-title mb-0">Outward Remittance</h2>
        </div>
        <div className="d-flex align-items-center gap-3 voucher-navigation-wrapper">
          <FaPaperclip
            size={24}
            onClick={() => {
              if (!isDisabled || pageState === 'view') {
                setUploadAttachmentsModal(true);
              }
            }}
          />
          {isDisabled && (
            <CustomButton text={'New'} onClick={() => setIsDisabled(false)} />
          )}
        </div>
      </div>
      {/* Search Bar */}
      <div className="d-flex justify-content-between flex-wrap gap-3">
        <div className="d-flex">
          <CustomInput
            label="Search"
            placeholder="Search"
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
            }}
            onButtonClick={() => {
              if (search) {
                setPageState('view');
              } else {
                // Show all remittances on empty search
                setPageState('allOutwardRemittances');
              }
            }}
            borderRadius={10}
            rightIcon={FaMagnifyingGlass}
            showBorders={false}
            className="w-25"
          />
        </div>
        {pageState !== 'allOutwardRemittances' && (
          <div className="d-flex gap-3">
            <div className="d-flex">
              <CustomInput
                label="Value Date"
                type="date"
                placeholder="Value Date"
                value={valueDate}
                disabled={isDisabled}
                onChange={(e) => setValueDate(e.target.value)}
                borderRadius={10}
                showBorders={isDisabled ? true : false}
              />
            </div>
            <div className="d-flex">
              <CustomInput
                label="Date"
                type="date"
                placeholder="Date"
                value={date}
                disabled={isDisabled}
                onChange={(e) => setDate(e.target.value)}
                borderRadius={10}
                showBorders={isDisabled ? true : false}
              />
            </div>
          </div>
        )}
      </div>

      {pageState === 'allOutwardRemittances' && <OutwardRemittanceTable />}

      {pageState !== 'allOutwardRemittances' && (
        <div className="d-card">
          <div className="">
            {/* Add Outward Remittance Form */}
            {pageState === 'new' ? (
              <OutwardRemittanceAdd
                isDisabled={isDisabled}
                setIsDisabled={setIsDisabled}
                setShowAddChequeDetailModal={setShowAddChequeDetailModal}
                setShowAddLedgerModal={setShowAddLedgerModal}
                setShowMissingCurrencyRateModal={
                  setShowMissingCurrencyRateModal
                }
                setShowOutOfScopeModal={setShowOutOfScopeModal}
                setCurrencyToSelect={setCurrencyToSelect}
                setPageState={setPageState}
              />
            ) : pageState === 'view' ? (
              !isNullOrEmpty(outwardRemittanceData) ? (
                <OutwardRemittanceView
                  outwardRemittanceData={outwardRemittanceData}
                  setPageState={setPageState}
                />
              ) : (
                <p>No data found</p>
              )
            ) : pageState === 'edit' ? (
              <OutwardRemittanceEdit
                outwardRemittanceData={outwardRemittanceData}
                // isDisabled={isDisabled}
                setIsDisabled={setIsDisabled}
                setShowAddChequeDetailModal={setShowAddChequeDetailModal}
                setShowAddLedgerModal={setShowAddLedgerModal}
                setShowMissingCurrencyRateModal={
                  setShowMissingCurrencyRateModal
                }
                setShowOutOfScopeModal={setShowOutOfScopeModal}
                setCurrencyToSelect={setCurrencyToSelect}
                setPageState={setPageState}
              />
            ) : pageState === 'allocation' ? (
              <OutwardRemittanceAllocation
                outwardRemittanceData={outwardRemittanceData}
                isDisabled={isDisabled}
                setShowAddLedgerModal={setShowAddLedgerModal}
                setShowMissingCurrencyRateModal={
                  setShowMissingCurrencyRateModal
                }
                setShowOutOfScopeModal={setShowOutOfScopeModal}
                setCurrencyToSelect={setCurrencyToSelect}
                setPageState={setPageState}
              />
            ) : null}
          </div>
        </div>
      )}
      {/* Upload Attachments Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          {...(pageState === 'new' && { uploadOnly: true })}
          {...(pageState === 'view' && { item: supportLogsData[0] })}
          getUploadedFiles={setAddedAttachment}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>

      {/* Missing Currency Rate Modal */}
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          console.log('Goto rate update screen');
          navigate('/transactions/remittance-rate-of-exchange', {
            state: { currencyToSelect },
          });
        }}
      />

      {/* Out Of Scope Modal */}
      <CustomModal
        show={showOutOfScopeModal}
        close={() => setShowOutOfScopeModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">Out of Scope Reason</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{
              reason: '',
            }}
            // validationSchema={outOfScopeSchema}
            // onSubmit={handleOutOfScopeReasonSubmit}
            onSubmit={() => setShowOutOfScopeModal(false)}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    label="Reason"
                    name="reason"
                    required
                    id="reason"
                    type="textarea"
                    rows={1}
                    placeholder="Enter reason"
                    value={values.reason}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.reason && errors.reason}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addClassificationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Save'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowOutOfScopeModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>

      {/* Add Cheque Detail Modal */}
      <CustomModal
        show={!!showAddChequeDetailModal}
        close={() => setShowAddChequeDetailModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        <ChequeDetailForm
          inPopup
          onSuccess={(newlyCreatedAccount) => {
            setShowAddChequeDetailModal('');
          }}
          onCancel={() => setShowAddChequeDetailModal('')}
        />
      </CustomModal>
    </div>
  );
};

export default OutwardRemittance;
