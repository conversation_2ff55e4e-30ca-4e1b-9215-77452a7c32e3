import axiosInstance from '../../Config/axiosConfig';
import { rateRevaluationData } from '../../Mocks/MockData';

// GET
export const getRateReValuationListing = async (params) => {
  try {
    // const { data } = await axiosInstance.get('/user-api/....', {
    //   params,
    // });
    return rateRevaluationData.detail;
    return data.detail; // Assume this returns the listing object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
