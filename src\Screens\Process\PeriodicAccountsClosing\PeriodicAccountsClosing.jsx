import { Form, Formik } from 'formik';
import React from 'react';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { useNavigate } from 'react-router-dom';

const PeriodicAccountsClosing = () => {
  usePageTitle('Periodic Account Locking');
  const navigate = useNavigate();

  const handleSubmit = (values) => {
    console.log(values);
    // updatePasswordMutation.mutate(values);
  };
  //   const updatePasswordMutation = useMutation({
  //     mutationFn: passwordUpdate,
  //     onSuccess: () => {
  //       showToast('Password Updated Successfully', 'success');
  //     },
  //     onError: (error) => {
  //       console.error('Failed to update password', error);
  //       if (!isNullOrEmpty(error.errors?.current_password)) {
  //         showToast(error.errors.current_password[0], 'error');
  //       }
  //       showToast(error.message, 'error');
  //     },
  //   });

  return (
    <>
      <div className="d-flex justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">Periodic Account Locking</h2>
        <CustomButton
          text={'Unlock Accounting Period Request'}
          onClick={() => {
            navigate('unlock-request');
            console.log('Unlock Accounting Period Request Clicked');
          }}
        />
      </div>
      <div className="d-card py-45 mb-45">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <Formik
              initialValues={{
                last_locking_date: '',
                locking_date: '',
              }}
              //   validationSchema={periodicAccountsClosingValidationSchema}
              onSubmit={handleSubmit}
            >
              {({ values, errors, touched, handleChange, handleBlur }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'last_locking_date'}
                        type={'date'}
                        label={'Last Locking Date'}
                        placeholder={'Enter Last Locking Date'}
                        value={values.last_locking_date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.last_locking_date && errors.last_locking_date
                        }
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'locking_date'}
                        type={'date'}
                        label={'Locking Date'}
                        placeholder={'Enter Locking Date'}
                        value={values.locking_date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.locking_date && errors.locking_date}
                      />
                    </div>

                    <p className="muted-text mb-4">
                      Once you lock your accounting books up to the selected
                      date, the changes are irreversible. You will not be able
                      to move to the back date.
                    </p>
                    <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'submit'}
                        text={'Process'}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </>
  );
};

export default PeriodicAccountsClosing;
