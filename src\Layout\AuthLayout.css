.authformBg {
  background: var(--body-bg-color);
  max-height: 800px;
  height: 100%;
}
.authBg {
  background: var(--primary-color);
}
.auth-wrapper {
  min-height: 100vh;
}
.authBox {
  border-radius: 0.5rem;
}

.authBackground {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  min-height: calc(100vh - 50px);
  height: 100%;
  position: relative;
  /* padding-right: 20px; */
}
.authBackground img {
  position: absolute;
  width: 100%;
  height: 100%;
  max-width: 1100px;
  object-fit: cover;
  object-position: center;
  border-radius: 10px;
}

.authImage img {
  width: 100%;
  max-width: 100%;
  min-height: 90vh;
  object-fit: cover;
  object-position: center;
  border-radius: 10px 0 0 10px;
}

.authForm {
  border-radius: 30px;
  padding: 5rem 7rem;
  width: 100%;
}
.authForm p {
  color: var(--table-td-color);
}
.authLogo {
  margin-bottom: 1rem;
  /* text-align: center; */
}

.authTitle {
  font-weight: bold;
  font-size: 30px;
  font-weight: 600;
  color: var(--input-label-color);
}

.authPara {
  font-weight: 500;
  font-size: 16px;
  color: var(--secondary-text-color);
}

@media screen and (max-width: 1199px) {
  .authForm {
    padding: 3rem;
  }
}
@media screen and (max-width: 767px) {
  .authLogo img {
    width: 200px;
  }
  .authformBg {
    max-height: 940px;
  }
}
@media screen and (max-width: 575px) {
  .authForm {
    padding: 2rem;
  }
  .authTitle {
    font-size: 20px;
  }
}
