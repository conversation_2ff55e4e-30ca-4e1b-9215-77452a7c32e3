import React, { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { FaMagnifyingGlass } from 'react-icons/fa6';
import BackButton from '../../../Components/BackButton';
import BeneficiaryRegisterForm from '../../../Components/BeneficiaryRegisterForm/BeneficiaryRegisterForm';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { getCurrencyOptions } from '../../../Utils/Utils';
import '../transactionStyles.css';
import EditInternalPaymentVoucher from './EditInternalPaymentVoucher';
import NewInternalPaymentVoucher from './NewInternalPaymentVoucher';
import ViewInternalPaymentVoucher from './ViewInternalPaymentVoucher';
import InternalPaymentVouchersTable from './InternalPaymentVouchersTable';
import { useQuery } from '@tanstack/react-query';
import { getAccountsbyType, getPVVoucherNumber } from '../../../Services/Transaction/PaymentVoucher.js';
import { getPaymentVoucherMode } from '../../../Services/Transaction/JournalVoucher.js';
import { getIPVoucherNumber } from '../../../Services/Transaction/InternalPaymentVoucher.js';
import { useLocation, useNavigate } from 'react-router-dom';
import useSettingsStore from '../../../Stores/SettingsStore.js';
  // Add a flag for restoring from store


const InternalPaymentVoucher = () => {
  usePageTitle('Internal Payment Voucher');
  const { state } = useLocation();
  const navigate = useNavigate();
  const { updatePrintSetting } = useSettingsStore();
  // [new, view, edit,  listing]
  // View is for specific Voucher search and view it's detail
  // Edit is for editing the specific Voucher's detail
  // Listing is for Voucher listing
  const [pageState, setPageState] = useState(state?.pageState || 'new');
  // const [pageState, setPageState] = useState('new');
  const [isDisabled, setIsDisabled] = useState(true);
  // const [searchTerm, setSearchTerm] = useState('');
    const [searchTerm, setSearchTerm] = useState(state?.searchTerm || '');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  // Upload Only Modal
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  // Upload And View Modal
  const [attachmentsModal, setAttachmentsModal] = useState(false);
  // Selected files from UploadAttachments Modal
  const [selectedFiles, setSelectedFiles] = useState(null);
  const [writeTerm, setWriteTerm] = useState(state?.searchTerm || ''); // To Make search term only work on ButtonClick (Not passing ref as do not want to change component at this stag
  const currencyOptions = getCurrencyOptions();
  const [restoreValuesFromStore, setRestoreValuesFromStore] = useState(false);
  const [lastVoucherNumbers, setLastVoucherNumbers] = useState({
    heading: 'Last IPV Number: ',
    current: '',
    previous: '',
    next: '',
    isLoadingVoucherNumber: false,
    isErrorVoucherNumber: false,
    errorVoucherNumber: null,
  });

  // Get last voucher number //
  const {
    data: voucherNumber,
    isLoading: isLoadingVoucherNumber,
    isError: isErrorVoucherNumber,
    error: errorVoucherNumber,
  } = useQuery({
    queryKey: ['voucherNumber', searchTerm],
    queryFn: () => getIPVoucherNumber(searchTerm),
    refetchOnWindowFocus: false,
    retry: 1,
  });

  useEffect(() => {
    setLastVoucherNumbers({
      heading: 'Last IPV Number: ',
      last: voucherNumber?.default_voucher_no,
      current: voucherNumber?.current_voucher_no,
      previous: voucherNumber?.previous_voucher_no,
      next: voucherNumber?.next_voucher_no,
      isLoadingVoucherNumber: isLoadingVoucherNumber,
      isErrorVoucherNumber: isErrorVoucherNumber,
      errorVoucherNumber: errorVoucherNumber,
    });
  }, [
    voucherNumber,
    isLoadingVoucherNumber,
    isErrorVoucherNumber,
    errorVoucherNumber,
  ]);

  // Get account options //
  const {
    data: partyAccounts,
    isLoading: isLoadingParty,
    isError: isErrorParty,
    error: errorParty,
  } = useQuery({
    queryKey: ['accounts', 'party'],
    queryFn: () => getAccountsbyType('party'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: generalAccounts,
    isLoading: isLoadingGeneral,
    isError: isErrorGeneral,
    error: errorGeneral,
  } = useQuery({
    queryKey: ['accounts', 'general'],
    queryFn: () => getAccountsbyType('general'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: walkinAccounts,
    isLoading: isLoadingWalkin,
    isError: isErrorWalkin,
    error: errorWalkin,
  } = useQuery({
    queryKey: ['accounts', 'walkin'],
    queryFn: () => getAccountsbyType('walkin'),
    staleTime: 1000 * 60 * 5,
  });

  const accountData = {
    party: {
      data: partyAccounts,
      loading: isLoadingParty,
      error: isErrorParty,
      errorMessage: errorParty,
    },
    general: {
      data: generalAccounts,
      loading: isLoadingGeneral,
      error: isErrorGeneral,
      errorMessage: errorGeneral,
    },
    walkin: {
      data: walkinAccounts,
      loading: isLoadingWalkin,
      error: isErrorWalkin,
      errorMessage: errorWalkin,
    },
  };


  // get modes

  const {
    data: modeBank,
    isLoading: isLoadingBank,
    isError: isErrorBank,
    error: errorBank,
  } = useQuery({
    queryKey: ['type', 'Bank'],
    queryFn: () => getPaymentVoucherMode('Bank'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: modeCash,
    isLoading: isLoadingCash,
    isError: isErrorCash,
    error: errorCash,
  } = useQuery({
    queryKey: ['type', 'Cash'],
    queryFn: () => getPaymentVoucherMode('Cash'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: modePdc,
    isLoading: isLoadingPdc,
    isError: isErrorPdc,
    error: errorPdc,
  } = useQuery({
    queryKey: ['type', 'Pdc'],
    queryFn: () => getPaymentVoucherMode('Pdc'),
    staleTime: 1000 * 60 * 5,
  });

  const {
    data: modeOnline,
    isLoading: isLoadingOnline,
    isError: isErrorOnline,
    error: errorOnline,
  } = useQuery({
    queryKey: ['type', 'Online'],
    queryFn: () => getPaymentVoucherMode('Online'),
    staleTime: 1000 * 60 * 5,
  });

  const modesData = {
    bank: {
      data: modeBank,
      loading: isLoadingBank,
      error: isErrorBank,
      errorMessage: errorBank,
    },
    cash: {
      data: modeCash,
      loading: isLoadingBank,
      error: isErrorBank,
      errorMessage: errorBank,
    },
    pdc: {
      data: modePdc,
      loading: isLoadingPdc,
      error: isErrorPdc,
      errorMessage: errorPdc,
    },
    online: {
      data: modeOnline,
      loading: isLoadingOnline,
      error: isErrorOnline,
      errorMessage: errorOnline,
    },
  };
  

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new beneficiary':
        return (
          <BeneficiaryRegisterForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  const renderPageContent = () => {
    const pageComponents = {
      new: (
        <NewInternalPaymentVoucher
          isDisabled={isDisabled}
          setIsDisabled={setIsDisabled}
          setSearchTerm={setSearchTerm}
           setPageState={setPageState}
          setShowAddLedgerModal={setShowAddLedgerModal}
          newlyCreatedAccount={newlyCreatedAccount}
          uploadAttachmentsModal={uploadAttachmentsModal}
            lastVoucherNumbers={lastVoucherNumbers}
          setUploadAttachmentsModal={setUploadAttachmentsModal}
         restoreValuesFromStore={restoreValuesFromStore}
          selectedFiles={selectedFiles}
          updatePrintSetting={updatePrintSetting}
          setSelectedFiles={setSelectedFiles}
          accountData={ accountData}
          modesData={modesData}
          currencyOptions = {currencyOptions}
          date = {date}
        />
      ),
      view: (
        <ViewInternalPaymentVoucher
        
          searchTerm={searchTerm}
          setDate={setDate}
           setSearchTerm={setSearchTerm}
          setWriteTerm={setWriteTerm}
          setPageState={setPageState}
          attachmentsModal={attachmentsModal}
          setAttachmentsModal={setAttachmentsModal}
          lastVoucherNumbers={lastVoucherNumbers}
        />
      ),
      listing: (
        <InternalPaymentVouchersTable
          setSearchTerm={setSearchTerm}
          setPageState={setPageState}
          date={date}

        />
      ),
      edit: (
        <EditInternalPaymentVoucher
          setPageState={setPageState}
          setShowAddLedgerModal={setShowAddLedgerModal}
          newlyCreatedAccount={newlyCreatedAccount}
          uploadAttachmentsModal={uploadAttachmentsModal}
          setUploadAttachmentsModal={setUploadAttachmentsModal}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
           restoreValuesFromStore={restoreValuesFromStore}
          accountData={ accountData}
          modesData={modesData}
          currencyOptions = {currencyOptions}
          date = {date}
          lastVoucherNumbers={lastVoucherNumbers}
          isDisabled = {false}
          setIsDisabled={setIsDisabled}
          updatePrintSetting={updatePrintSetting}
        />
      ),
    };

    return pageComponents[pageState] || null;
  };

  return (
    <>
      <section className="position-relative">
        <div
          style={{ height: 43 }}
          className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-4"
        >
          <div>
            {(pageState === 'listing' ||
              pageState === 'view' ||
              pageState === 'edit') && (
              // <BackButton
              //   handleBack={() => {
              //     setPageState('new');
              //     setSearchTerm('');
              //     setWriteTerm('');
              //   }}
              // />
                <BackButton
                handleBack={() => {
                  pageState == 'edit'
                    ? setPageState('view')
                    : (() => {
                        setDate(new Date().toISOString().split('T')[0]);
                        setPageState('new');
                        setWriteTerm('');
                        setSearchTerm('');
                      })();
                }}
              />
            )}
            <h2 className="screen-title mb-0">Internal Payment Voucher</h2>
          </div>
          {pageState == 'new' && isDisabled && (
            <div className="d-flex gap-2">
              <CustomButton text={'New'} onClick={() => setIsDisabled(false)} />
            </div>
          )}
        </div>
        <Row>
          <Col xs={12}>
            <div className="d-flex align-items-start justify-content-between flex-wrap-reverse mb-3">
              <div className="d-flex align-items-end mt-3">
                <CustomInput
                  style={{ width: '280px' }}
                  type="text"
                  placeholder="Search IPV"
                  error={false}
                  showBorders={false}
                  borderRadius={10}
                  name="search"
                  rightIcon={FaMagnifyingGlass}
                  // value={searchTerm}
                  // onChange={(e) => {
                  //   setSearchTerm(e.target.value)
                  //
                  // }}
                  // onButtonClick={() => {
                  //   if (searchTerm === '') {
                  //     setPageState('listing');
                  //   } else {
                  //     setPageState('view');
                  //   }
                  //   console.log('search for:', searchTerm);
                  // }}
                  // value={searchTerm}
                  value={writeTerm}
                  onChange={(e) => {
                    //  setSearchTerm(e.target.value);
                    setWriteTerm(e.target.value);
                  }}
                  onButtonClick={() => {

                    setSearchTerm(writeTerm);
                    if (writeTerm === '') {
                      setPageState('listing');
                    } else {
                      setPageState('view');
                    }

                    // if (searchTerm === '') {
                    //   setPageState('listing');
                    // } else {
                    //   setPageState('view');
                    // }
                    console.log('search for:', writeTerm);
                  }}
                />
              </div>
              <div>
                <CustomInput
                  name="Date"
                  label={'Date'}
                  type="date"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={date}
                  onChange={(e) => {
                    setDate(e.target.value);
                  }}
                />
              </div>
            </div>
            {renderPageContent()}
          </Col>
        </Row>
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
    </>
  );
};

export default InternalPaymentVoucher;
