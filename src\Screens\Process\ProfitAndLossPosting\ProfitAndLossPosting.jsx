import { ErrorMessage, Form, Formik } from 'formik';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import withModal from '../../../HOC/withModal';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import './ProfitAndLossPosting.css';

const pnlSteps = [
  {
    text: 'Re-Calculating Rate',
    explanation: `This option will recalculate the average closing rate for each currency. Based on these closing rates, the system will evaluate the value of each individual currency.
    \nBefore using this option, please ensure that no other users are actively using the system.`,
    buttonText: 'Recalculating Closing Rates',
  },
  {
    text: 'Rate Re-Valuation',
    explanation: `This option will calculate the exchange rate differences and transfer the resulting amounts to the Revenue Account for proper accounting treatment.`,
    buttonText: 'Rate Re-Valuation',
  },
  {
    text: 'Profit & Loss Balance Conversion',
    explanation: `This process will convert the profit and loss balances from foreign currencies into the base currency.`,
    buttonText: 'Convert Balance',
  },
  {
    text: 'Profit & Loss Posting',
    explanation: `This option will close your Income and Expense Accounts and transfer the resulting balance to the Retained Earnings or Profit Account under the Capital section.`,
    buttonText: 'Profit & Lost Posting',
  },
];
const ProfitAndLossPosting = ({ showModal }) => {
  usePageTitle('Profit & Loss Posting');
  const navigate = useNavigate();
  const [selectedStep, setSelectedStep] = useState(0);
  const [showBalanceConversionModal, setShowBalanceConversionModal] =
    useState(false);
  const [showPostingModal, setShowPostingModal] = useState(false);

  const handleStepAction = () => {
    const actions = [
      () => alert('Recalculating Closing Rates...'),
      () => navigate('rate-revaluation'),
      () => setShowBalanceConversionModal(true),
      () => setShowPostingModal(true),
    ];
    // showModal(
    //   'Balance',
    //   'Balance has been Written-Off using JV# 17600',
    //   null,
    //   'success'
    // ),

    actions[selectedStep]?.(); // Call the function for the selected step
  };

  const handlePostBalanceConversion = (values) => {
    console.log(values);
    setShowBalanceConversionModal(false);
  };
  const handlePosting = (values) => {
    console.log(values);
    setShowPostingModal(false);
    showModal(
      'Proceed',
      'You have selected closing of income and expense account to Account ABC upto DD/MM/YYYY. Do you want to proceed?',
      () =>
        showModal(
          'Profit And Loss',
          'Profit & Loss closed upto DD/MM/YYYY useing JV# 17600',
          null,
          'success'
        ),
      'question'
    );
  };

  return (
    <>
      <div className="d-flex justify-content-between flex-wrap mb-4">
        <h2 className="screen-title mb-0">Profit And Loss Process</h2>
      </div>
      <div className="d-card p-0">
        <div className="d-flex flex-column flex-lg-row align-items-stretch">
          <div className="coa-box">
            <h2 className="screen-title-body">Steps</h2>
            <div className="mb-4">
              {pnlSteps.map((step, i) => (
                <button
                  key={i}
                  className={`pnl-button ${selectedStep === i ? 'active' : ''}`}
                  onClick={() => setSelectedStep(i)}
                >
                  {i + 1}- {step.text}
                </button>
              ))}
            </div>
            <div className="d-flex">
              <CustomButton
                text={pnlSteps[selectedStep].buttonText}
                onClick={handleStepAction}
              />
            </div>
          </div>
          <div className="coa-box d-flex flex-column">
            <h2 className="screen-title-body">Explanation</h2>
            <p className="pnl-explanation">
              {pnlSteps[selectedStep].explanation
                .split('\n')
                .map((line, index) => (
                  <React.Fragment key={index}>
                    {line}
                    <br />
                  </React.Fragment>
                ))}
            </p>
          </div>
        </div>
      </div>
      {/* Profit & Loss Balance Conversion Modal  */}
      <CustomModal
        show={showBalanceConversionModal}
        close={() => setShowBalanceConversionModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle px-5">Profit & Loss Balance Conversion</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ conversion_date: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handlePostBalanceConversion}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'conversion_date'}
                    type={'date'}
                    required
                    label={'Select Date of Conversion'}
                    value={values.conversion_date}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.conversion_date && errors.conversion_date}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Post'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowBalanceConversionModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
      {/* Profit & Loss Posting Modal  */}
      <CustomModal
        show={showPostingModal}
        close={() => setShowPostingModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle px-5">Profit & Loss Account</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ account: '', profit_loss_date: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            onSubmit={handlePosting}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-3">
                  <SearchableSelect
                    name="account"
                    label="Profit & Loss Account"
                    required
                    placeholder="Select Account"
                    options={[]}
                    value={values.account}
                    onChange={(v) => setFieldValue('account', v.value)}
                  />
                  <ErrorMessage
                    name="account"
                    component="div"
                    className="input-error-message text-danger"
                  />
                </div>
                <div className="mb-45">
                  <CustomInput
                    name={'profit_loss_date'}
                    type={'date'}
                    required
                    label={'Close Profit & Loss Upto'}
                    value={values.profit_loss_date}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.profit_loss_date && errors.profit_loss_date}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Post'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowPostingModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
    </>
  );
};

export default withModal(ProfitAndLossPosting);
