import React, { useState } from 'react';
import { FaCircleXmark } from 'react-icons/fa6';
import {
  HiOutlinePencilSquare,
  HiOutlineTrash,
  HiPrinter,
} from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import { MOCK_TTR_REGISTER_BANK_DETAILS_DATA } from '../../../Mocks/MockData';
import { ttrRegisterBankDetailsHeaders } from '../../../Utils/Constants/TableHeaders';
import CustomModal from '../../../Components/CustomModal';

const TTRRegisterBankDetails = ({ filters, setFilters, pagination }) => {
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const tableData = MOCK_TTR_REGISTER_BANK_DETAILS_DATA;
  const isLoading = false;
  const isError = false;

  return (
    <>
      <CustomTable
        filters={filters}
        setFilters={setFilters}
        headers={ttrRegisterBankDetailsHeaders}
        pagination={pagination}
        isLoading={isLoading}
      >
        {(tableData.length || isError) && (
          <tbody>
            {isError && (
              <tr>
                <td colSpan={ttrRegisterBankDetailsHeaders.length}>
                  <p className="text-danger mb-0">
                    Unable to fetch data at this time
                  </p>
                </td>
              </tr>
            )}
            {tableData?.map((item, index) => (
              <tr key={item.id}>
                <td>{item.date}</td>
                <td>{item.credit_party}</td>
                <td>{item.bank_name}</td>
                <td>{item.bank_account}</td>
                <td>{item.remarks}</td>
                <td>{item.amount}</td>
                <td>{item.account_name}</td>
                <td>{item.allocated}</td>
                <td>{item.unallocated}</td>
                <td>{item.confirmed}</td>
                <td>{item.unconfirmed}</td>
                <td>{item.user_id}</td>
                <td>
                  <TableActionDropDown
                    actions={[
                      {
                        name: 'Edit',
                        icon: HiOutlinePencilSquare,
                        onClick: () => {
                          console.log(item.id, 'edit');
                          navigate(
                            `/transactions/ttr-register/bank-details/${item.id}/edit`
                          );
                        },
                        className: 'edit',
                      },
                      {
                        name: 'Print',
                        icon: HiPrinter,
                        onClick: () => {
                          console.log(item.id, 'print');
                        },
                        className: 'attachments',
                      },
                      {
                        name: 'Delete',
                        icon: HiOutlineTrash,
                        onClick: () => {
                          setShowDeleteModal(true);
                        },
                        className: 'delete',
                      },
                      {
                        name: 'Cancel Unallocated',
                        icon: FaCircleXmark,
                        onClick: () => {
                          console.log(item.id, 'cancel unallocated');
                        },
                        className: 'delete',
                      },
                    ]}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        )}
      </CustomTable>
      <CustomModal
        show={showDeleteModal}
        close={() => {
          setShowDeleteModal(false);
        }}
        title="Delete Bank Details"
        description="Are you sure you want to delete this bank details?"
        action={() => setShowDeleteModal(false)}
        // disableClick={deletePackageMutation.isLoading}o
      />
    </>
  );
};

export default withFilters(TTRRegisterBankDetails);
