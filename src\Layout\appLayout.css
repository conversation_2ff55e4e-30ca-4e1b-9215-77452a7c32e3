.screensSectionContainer {
  margin-left: 270px;
  margin-top: 78px;
  padding: 30px 40px;
  min-height: calc(100vh - 78px);
  transition: 0.3s ease-out;
}
.expanded {
  margin-left: 60px;
}
@media screen and (max-width: 767px) {
  .screensSectionContainer {
    margin-left: 0;
    margin-top: 64px;
    padding: 20px;
  }
}
@media screen and (max-width: 576px) {
  .screensSectionContainer {
    padding: 1rem;
  }
}
