import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ErrorMessage, Form, Formik } from 'formik';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import { showToast } from '../../../Components/Toast/Toast';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  addSuspenseVoucherAttachment,
  deleteSuspenseVoucherAttachment,
  getSuspenseVoucherAttachments,
  getSuspenseVoucherListing,
  updateSuspenseVoucher,
} from '../../../Services/Transaction/SuspenseVoucher';
import useSettingsStore from '../../../Stores/SettingsStore';
import { suspenseVoucherNewHeaders } from '../../../Utils/Constants/TableHeaders';
import { showErrorToast } from '../../../Utils/Utils';
import SuspenseVoucherRow from './SuspenseVoucherRow';

const generateInitialRows = (transactions = []) => {
  const rows = {};
  if (transactions.length > 0) {
    transactions.forEach((transaction) => {
      const id = crypto.randomUUID();
      rows[id] = {
        id,
        narration: transaction.narration || '',
        debit: transaction.debit?.toString() || '',
        credit: transaction.credit?.toString() || '',
        status: transaction.status,
      };
    });
  } else {
    // Fallback to empty rows if no transactions
    Array.from({ length: 5 }).forEach(() => {
      const id = crypto.randomUUID();
      rows[id] = {
        id,
        narration: '',
        debit: '',
        credit: '',
        status: 'open',
      };
    });
  }
  return rows;
};

const EditSuspenseVoucher = ({
  date,
  currencyOptions,
  getAccountsByTypeOptions,
  getOfficeLocationOptions,
  setPageState,
  setShowAddOfficeLocationModal,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  lastVoucherNumbers,
  setSearchTerm,
  searchTerm,
  updatePrintSetting,
  isDisabled = false,
  setIsDisabled,
}) => {
  const queryClient = useQueryClient();
  const [rows, setRows] = useState({});
  const [totalDebit, setTotalDebit] = useState(0);
  const [totalCredit, setTotalCredit] = useState(0);
  const [selectedLedger, setSelectedLedger] = useState(null);
  const formikRef = useRef();

  // For getting print checkbox state from BE
  const { getPrintSettings } = useSettingsStore();

  // State to track original data for change detection
  const [originalFormData, setOriginalFormData] = useState(null);
  const [originalRows, setOriginalRows] = useState({});
  const [hasChanges, setHasChanges] = useState(false);
  const [originalStatus, setOriginalStatus] = useState(null);

  // Attachment modal state - matching Journal Voucher pattern
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);

  // Fetch suspense voucher data directly - matching Journal Voucher pattern
  const {
    data: { data: [suspenseVoucherData] = [] } = {},
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['suspenseVoucher', searchTerm],
    queryFn: () => getSuspenseVoucherListing({ search: searchTerm }),
    
  });



  const suspenseVoucher = suspenseVoucherData?.suspense_vouchers;

  // Initialize rows when suspenseVoucherData changes
  useEffect(() => {
    if (suspenseVoucher) {
      // Transform the API data structure to match the expected format
      const transformedData = {
        id: suspenseVoucher.id,
        ledger: suspenseVoucher.ledger,
        account_id: suspenseVoucher.account_id,
        office_id: suspenseVoucher.office?.id,
        currency_id: suspenseVoucher.currency?.id,
        date: suspenseVoucher.voucher_date,
        transactions:
          suspenseVoucher.suspense_voucher_rows?.map((row) => ({
            narration: row.narration,
            debit: row.debit,
            credit: row.credit,
            status: row.status_detail,
          })) || [],
      };

      const initialRows = generateInitialRows(transformedData.transactions);
      setRows(initialRows);
      setSelectedLedger(transformedData.ledger);

      // Store original data for change detection
      setOriginalFormData({
        ledger: suspenseVoucher.ledger || '',
        account: suspenseVoucher.account_id?.toString() || '',
        office: suspenseVoucher.office?.id?.toString() || '',
        currency: suspenseVoucher.currency?.id?.toString() || '',
      });
      setOriginalRows(initialRows);
      setOriginalStatus(suspenseVoucher.status_detail);
      setHasChanges(false);
    }
  }, [suspenseVoucher]);

  // Refresh form data when suspenseVoucherData changes (e.g., after update)
  useEffect(() => {
    if (suspenseVoucher && formikRef.current) {
      // Reset form with updated data
      formikRef.current.setValues({
        ledger: suspenseVoucher.ledger || '',
        account: suspenseVoucher.account_id?.toString() || '',
        office: suspenseVoucher.office?.id?.toString() || '',
        currency: suspenseVoucher.currency?.id?.toString() || '',
      });

      // Update selected ledger
      setSelectedLedger(suspenseVoucher.ledger);

      // Update original data for change detection
      setOriginalFormData({
        ledger: suspenseVoucher.ledger || '',
        account: suspenseVoucher.account_id?.toString() || '',
        office: suspenseVoucher.office?.id?.toString() || '',
        currency: suspenseVoucher.currency?.id?.toString() || '',
      });
      setOriginalStatus(suspenseVoucher.status_detail);
      setHasChanges(false);
    }
  }, [suspenseVoucher]);

  // Additional useEffect to handle form initialization when data is loaded
  useEffect(() => {
    if (suspenseVoucher && formikRef.current) {
      // Force update form values
      const newValues = {
        ledger: suspenseVoucher.ledger || '',
        account: suspenseVoucher.account_id?.toString() || '',
        office: suspenseVoucher.office?.id?.toString() || '',
        currency: suspenseVoucher.currency?.id?.toString() || '',
      };

      formikRef.current.setValues(newValues);
      setSelectedLedger(suspenseVoucher.ledger);
    }
  }, [suspenseVoucher]);

  // Calculate totals whenever rows change
  React.useEffect(() => {
    const debitTotal = Object.values(rows).reduce((sum, row) => {
      return sum + (parseFloat(row.debit) || 0);
    }, 0);
    const creditTotal = Object.values(rows).reduce((sum, row) => {
      return sum + (parseFloat(row.credit) || 0);
    }, 0);
    setTotalDebit(debitTotal);
    setTotalCredit(creditTotal);
  }, [rows]);

  // Function to check for changes and update status
  const checkForChanges = useCallback(
    (currentFormData, currentRows) => {
      if (!originalFormData || !originalRows) return;

      // Check if form data has changed
      const formDataChanged =
        currentFormData.ledger !== originalFormData.ledger ||
        currentFormData.account !== originalFormData.account ||
        currentFormData.office !== originalFormData.office ||
        currentFormData.currency !== originalFormData.currency;

      // Check if rows have changed
      const currentRowsArray = Object.values(currentRows);
      const originalRowsArray = Object.values(originalRows);

      const rowsChanged =
        currentRowsArray.length !== originalRowsArray.length ||
        currentRowsArray.some((row, index) => {
          const originalRow = originalRowsArray[index];
          if (!originalRow) return true;
          return (
            row.narration !== originalRow.narration ||
            row.debit !== originalRow.debit ||
            row.credit !== originalRow.credit
          );
        });

      const hasAnyChanges = formDataChanged || rowsChanged;
      setHasChanges(hasAnyChanges);

      // If original status was "Approved" and there are changes, update row statuses to "Open"
      if (originalStatus === 'Approved' && hasAnyChanges) {
        setRows((prevRows) => {
          const updatedRows = {};
          Object.keys(prevRows).forEach((key) => {
            updatedRows[key] = {
              ...prevRows[key],
              status: 'open',
            };
          });
          return updatedRows;
        });
      }
    },
    [originalFormData, originalRows, originalStatus]
  );

  // Update suspense voucher mutation
  const updateSuspenseVoucherMutation = useMutation({
    mutationFn: (payload) =>
      updateSuspenseVoucher(payload.id, payload.formData),
    onSuccess: (data) => {
      showToast('Suspense Voucher Updated Successfully!', 'success');

      if (getPrintSettings('suspense_voucher')) {
        window.open(data.detail?.pdf_url, '_blank');
      }

      // Invalidate all related queries to force a fresh fetch
      queryClient.invalidateQueries(['suspenseVoucher', searchTerm]);
      queryClient.invalidateQueries(['suspenseVoucherListing']);

      // Force a refetch to ensure we have the latest data
      queryClient.refetchQueries(['suspenseVoucher', searchTerm]);

      // Reset change detection state since the update was successful
      setHasChanges(false);

      // Add a small delay to ensure data is refetched before navigating
      setTimeout(() => {
        // Navigate back to view mode to see the updated data
        setPageState('view');
      }, 500);
    },
    onError: (error) => {
      showErrorToast(error);
    },
  });

  const handleAddRows = () => {
    let count = 5; // Number of rows to add
    const newRows = {};
    Array.from({ length: count }).forEach(() => {
      const id = crypto.randomUUID();
      newRows[id] = {
        id,
        narration: '',
        debit: '',
        credit: '',
        status: 'open',
      };
    });
    setRows((prevRows) => {
      const updatedRows = { ...prevRows, ...newRows };

      // Check for changes after adding new rows
      if (formikRef.current) {
        const currentFormData = formikRef.current.values;
        checkForChanges(currentFormData, updatedRows);
      }

      return updatedRows;
    });
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;

    // Validate form
    if (
      !formValues.ledger ||
      !formValues.account ||
      !formValues.office ||
      !formValues.currency
    ) {
      showToast('Please fill in all required fields', 'error');
      return;
    }

    // Validate rows - allow rows with only credit or only debit, but require narration
    const validRows = Object.values(rows).filter(
      (row) => row.narration && (row.debit || row.credit)
    );

    if (validRows.length === 0) {
      showToast(
        'Please add at least one row with narration and either debit or credit amount',
        'error'
      );
      return;
    }

    // Transform transactions to match backend expectations
    const transformedTransactions = validRows.reduce((acc, row, index) => {
      acc[`transactions[${index}][narration]`] = row.narration;
      acc[`transactions[${index}][debit]`] = parseFloat(
        row.debit || 0
      ).toString();
      acc[`transactions[${index}][credit]`] = parseFloat(
        row.credit || 0
      ).toString();
      // Map status values to backend format
      let statusValue;
      switch (row.status) {
        case 'open':
          statusValue = '1';
          break;
        case 'closed':
          statusValue = '0';
          break;
        case 'Approved':
          statusValue = '2';
          break;
        case 'Settle':
          statusValue = '3';
          break;
        default:
          statusValue = '1'; // Default to open
      }
      acc[`transactions[${index}][status]`] = statusValue;
      return acc;
    }, {});

    const payload = {
      total_debit: totalDebit.toString(),
      total_credit: totalCredit.toString(),
      ledger: formValues.ledger,
      account_id: formValues.account.toString(),
      office_id: formValues.office.toString(),
      currency_id: formValues.currency.toString(),
      date: date,
      ...transformedTransactions,
    };

    updateSuspenseVoucherMutation.mutate({
      id: suspenseVoucherData.voucher_no,
      formData: payload,
    });
  };

  const handleCancel = () => {
    setPageState('view');
    setSearchTerm(suspenseVoucherData?.voucher_no || '');
  };

  // Handler functions for rows
  const updateField = useCallback(
    (id, field, value) => {
      setRows((prev) => {
        const newRows = {
          ...prev,
          [id]: {
            ...prev[id],
            [field]: value,
          },
        };

        // Check for changes after updating the row
        if (formikRef.current) {
          const currentFormData = formikRef.current.values;
          checkForChanges(currentFormData, newRows);
        }

        return newRows;
      });
    },
    [checkForChanges]
  );

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];

      // Check for changes after deleting the row
      if (formikRef.current) {
        const currentFormData = formikRef.current.values;
        checkForChanges(currentFormData, newState);
      }

      return newState;
    });
  };

  // Get ledger options
  const getLedgerOptions = () => {
    return [
      { label: 'PL', value: 'party' },
      { label: 'GL', value: 'general' },
      { label: 'WIC', value: 'walkin' },
    ];
  };

  if (isLoading) {
    return (
      <div className="d-card">
        <div className="text-center">
          <p className="text-muted mb-0">Loading Suspense Voucher data...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="d-card">
        <div className="text-center">
          <p className="text-danger mb-0">
            Unable to fetch Suspense Voucher data
          </p>
        </div>
      </div>
    );
  }

  if (!suspenseVoucherData || !suspenseVoucher) {
    return (
      <div className="d-card">
        <div className="text-center">
          <p className="text-muted mb-0">No Suspense Voucher data found</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="d-card">
        <Formik
          key={`${suspenseVoucherData?.id || 'loading'}-${
            suspenseVoucher?.ledger || ''
          }-${suspenseVoucher?.account_id || ''}-${
            suspenseVoucher?.office?.id || ''
          }-${suspenseVoucher?.currency?.id || ''}`}
          innerRef={formikRef}
          enableReinitialize={true}
          initialValues={{
            ledger: suspenseVoucher?.ledger || '',
            account: suspenseVoucher?.account_id?.toString() || '',
            office: suspenseVoucher?.office?.id?.toString() || '',
            currency: suspenseVoucher?.currency?.id?.toString() || '',
          }}
          validate={(values) => {
            const errors = {};

            // Required fields validation
            if (!values.ledger) errors.ledger = 'Ledger is required';
            if (!values.account) errors.account = 'Account is required';
            if (!values.office) errors.office = 'Office is required';
            if (!values.currency) errors.currency = 'Currency is required';

            return errors;
          }}
          onSubmit={() => {}}
        >
          {({ values, handleBlur, setFieldValue, touched, errors }) => {
            return (
              <Form>
                {console.log('values', values)}
                <div className="row">
                  <div className="col-12 col-xxl-9">
                    <div className="row mb-4">
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Ledger"
                          type1="select"
                          type2="select"
                          name1="ledger"
                          name2="account"
                          value1={values.ledger}
                          value2={values.account || newlyCreatedAccount?.id}
                          options1={getLedgerOptions()}
                          options2={getAccountsByTypeOptions(
                            selectedLedger || values.ledger
                          )}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Select Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('ledger', selected.value);
                              setSelectedLedger(selected.value);
                              setFieldValue('account', ''); // Reset account when ledger changes
                              // Check for changes after updating form
                              setTimeout(() => {
                                if (formikRef.current) {
                                  checkForChanges(
                                    formikRef.current.values,
                                    rows
                                  );
                                }
                              }, 0);
                            }
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                            } else {
                              setFieldValue('account', selected.value);
                              // Check for changes after updating form
                              setTimeout(() => {
                                if (formikRef.current) {
                                  checkForChanges(
                                    formikRef.current.values,
                                    rows
                                  );
                                }
                              }, 0);
                            }
                          }}
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'office'}
                          label={'Office'}
                          options={[
                            ...getOfficeLocationOptions(),
                            {
                              label: 'Add New Office',
                              value: null,
                            },
                          ]}
                          placeholder={'Select Office'}
                          value={values.office}
                          isDisabled={isDisabled}
                          onChange={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddOfficeLocationModal(true);
                            } else {
                              setFieldValue('office', selected.value);
                              // Check for changes after updating form
                              setTimeout(() => {
                                if (formikRef.current) {
                                  checkForChanges(
                                    formikRef.current.values,
                                    rows
                                  );
                                }
                              }, 0);
                            }
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="office"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name={'currency'}
                          label={'Currency'}
                          options={currencyOptions}
                          placeholder={'Select Currency'}
                          value={values.currency}
                          isDisabled={isDisabled}
                          onChange={(selected) => {
                            setFieldValue('currency', selected.value);
                            // Check for changes after updating form
                            setTimeout(() => {
                              if (formikRef.current) {
                                checkForChanges(formikRef.current.values, rows);
                              }
                            }, 0);
                          }}
                          onBlur={handleBlur}
                        />
                        <ErrorMessage
                          name="currency"
                          component="div"
                          className="input-error-message text-danger"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                    <div className="row">
                      {/* Right side cards */}
                      <div
                        className="col-12 mb-5"
                        style={{ maxWidth: '350px' }}
                      >
                        <AccountBalanceCard />
                      </div>
                    </div>
                  </div>
                  <CustomTable
                    displayCard={false}
                    headers={suspenseVoucherNewHeaders}
                    isPaginated={false}
                    className={'inputTable'}
                    hideSearch
                    hideItemsPerPage
                  >
                    <tbody>
                      {Object.values(rows).map((row, index) => (
                        <SuspenseVoucherRow
                          key={row.id}
                          row={row}
                          index={index}
                          isDisabled={isDisabled}
                          handleDeleteRow={handleDeleteRow}
                          updateField={updateField}
                        />
                      ))}
                    </tbody>
                  </CustomTable>

                  <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        style={{ border: 'none', margin: 0 }}
                        disabled={isDisabled}
                        onChange={() => {}}
                      />
                      <CustomCheckbox
                        label="Print"
                        style={{ border: 'none', margin: 0 }}
                        disabled={isDisabled}
                        checked={getPrintSettings('suspense_voucher')}
                        onChange={() =>
                          updatePrintSetting(
                            'suspense_voucher',
                            !getPrintSettings('suspense_voucher')
                          )
                        }
                      />
                    </div>
                    <div className="d-flex flex-column gap-2 mt-1 debit-credit-inputs">
                      <CustomInput
                        name="totalDebit"
                        label={'Total Debit'}
                        labelClass={'fw-medium'}
                        type="number"
                        error={false}
                        borderRadius={10}
                        value={totalDebit.toFixed(2)}
                        readOnly
                      />
                      <CustomInput
                        name="totalCredit"
                        label={'Total Credit'}
                        labelClass={'fw-medium'}
                        type="number"
                        error={false}
                        borderRadius={10}
                        value={totalCredit.toFixed(2)}
                        readOnly
                      />
                    </div>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        loading={updateSuspenseVoucherMutation.isPending}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />
      {/* Attachements Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >

        <AttachmentsView
          key={`${suspenseVoucherData?.id}-${suspenseVoucherData?.files?.length || 0}`}
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={suspenseVoucherData}
          deleteService={deleteSuspenseVoucherAttachment}
          uploadService={addSuspenseVoucherAttachment}
          getAttachmentsService={getSuspenseVoucherAttachments}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          queryToInvalidate={['suspenseVoucher', searchTerm]}
        />
      </CustomModal>
    </>
  );
};

export default EditSuspenseVoucher;
