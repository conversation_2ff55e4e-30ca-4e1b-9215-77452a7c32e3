import React, { useState } from 'react';
import BackButton from '../../../Components/BackButton';
import { ErrorMessage, Form, Formik } from 'formik';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import CustomInput from '../../../Components/CustomInput';
import CustomButton from '../../../Components/CustomButton';
import UploadAndDisplayFiles from '../../../Components/UploadAndDisplayFiles/UploadAndDisplayFiles';

const UnlockPeriodicAccountRequest = () => {
  const [hasAgreedToTerms, setHasAgreedToTerms] = useState(false);

  const handleSubmit = (values) => {
    console.log(values);
  };
  return (
    <>
      <div className="d-flex align-items-start mb-4 justify-content-between flex-wrap">
        <div className="d-flex flex-column gap-2">
          <BackButton />
          <h2 className="screen-title m-0 d-inline">
            Unlock Accounting Period Request
          </h2>
        </div>
      </div>
      <div className="d-card">
        <h4 className="details-page-header mb-4">Requestor Information</h4>
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <Formik
              initialValues={{
                full_name: '',
                email_address: '',
                role: '',
                start_date: '',
                end_date: '',
                unlocking_reason: '',
                files: '',
              }}
              // validationSchema={passwordResetValidationSchema}
              onSubmit={handleSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'user_id'}
                        type={'text'}
                        required
                        label={'User ID'}
                        placeholder={'Enter User ID'}
                        value={values.user_id}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.user_id && errors.user_id}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'email_address'}
                        type={'text'}
                        required
                        label={'Email Address'}
                        placeholder={'Enter Email Address'}
                        value={values.email_address}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.email_address && errors.email_address}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        name="role"
                        label="R  ole"
                        placeholder="Select Role"
                        options={[]}
                        value={values.role}
                        onChange={(v) => setFieldValue('role', v.value)}
                      />
                      <ErrorMessage
                        name="role"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>
                    <h4 className="details-page-header mt-45 mb-4">
                      Accounting Period to Unlock
                    </h4>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'start_date'}
                        type={'date'}
                        required
                        label={'Start Date'}
                        placeholder={'Enter Start Date'}
                        value={values.start_date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.start_date && errors.start_date}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name={'end_date'}
                        type={'date'}
                        required
                        label={'End Date'}
                        placeholder={'Enter End Date'}
                        value={values.end_date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.end_date && errors.end_date}
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <CustomInput
                        name={'unlocking_reason'}
                        type={'textarea'}
                        rows={1}
                        required
                        label={'Unlocking Reason'}
                        placeholder={'Enter Unlocking Reason'}
                        value={values.unlocking_reason}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.unlocking_reason && errors.unlocking_reason
                        }
                      />
                    </div>
                    <h4 className="details-page-header  mt-45 mb-4">
                      Supporting Documents
                    </h4>
                    <div className="col-12 mb-5">
                      <UploadAndDisplayFiles
                        numberOfFiles={5}
                        onChange={(files) => {
                          setFieldValue('files', files);
                        }}
                        required
                        label={'Supporting Documents'}
                        errorFromParent={touched.files && errors.files}
                      />
                    </div>
                    <div className="d-inline-flex align-items-center mb-5">
                      <div className="checkbox-wrapper">
                        <label className="checkbox-container">
                          <input
                            type="checkbox"
                            defaultChecked={false}
                            onChange={(e) => {
                              setHasAgreedToTerms(e.target.checked);
                            }}
                            name="terms&conditions"
                          />
                          <span className="custom-checkbox"></span>I agree to
                          the terms & conditions
                        </label>
                      </div>
                    </div>
                    <div className="d-flex gap-3 ms-auto mb-3 mb-md-0">
                      <CustomButton
                        // loading={updatePasswordMutation.isPending}
                        // disabled={updatePasswordMutation.isPending}
                        type={'submit'}
                        text={'Submit'}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </>
  );
};

export default UnlockPeriodicAccountRequest;
