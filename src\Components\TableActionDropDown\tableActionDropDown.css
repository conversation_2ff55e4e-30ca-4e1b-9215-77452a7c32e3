.table-action-wrapper button {
  background: transparent !important;
  background-color: transparent !important;
  border-color: none !important;
  color: var(--input-label-color);
}
.table-action-wrapper button:active,
.table-action-wrapper button:hover {
  color: var(--primary-color) !important;
}
.table-action-wrapper button.show {
  color: var(--input-label-color) !important;
}
.table-action-wrapper {
  position: relative !important;
  text-align: center;
}
.table-action-wrapper .dropdown-menu {
  width: max-content;
  background-color: var(--content-bg-color);
  box-shadow: 0px 4px 17px -6px color-mix(in srgb, rgba(0, 0, 0, 0.25) 50%, var(
          --primary-color
        )
        50%) !important;
  border-radius: 8px;
  overflow: hidden;
  border: none !important;
  padding: 0;
  z-index: 500;
}
.table-action-wrapper .dropdown-menu .dropdown-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.table-action-wrapper .dropdown-menu .dropdown-item {
  padding: 10px 14px !important;
  font-size: 14px;
}
.navbar .table-action-wrapper .dropdown-menu .dropdown-item {
  color: var(--input-label-color);
}
.table-action-wrapper .dropdown-item:focus,
.dropdown-item:hover {
  background-color: transparent !important;
}
.navbar .table-action-wrapper .dropdown-item.bg-hover:focus,
.navbar .dropdown-item.bg-hover:hover {
  background-color: color-mix(
    in srgb,
    transparent 90%,
    var(--input-label-color) 10%
  ) !important;
}
.tableAction .disabled {
  background-color: transparent !important;
  cursor: not-allowed !important;
  color: color-mix(
    in srgb,
    transparent 75%,
    var(--input-label-color) 25%
  ) !important;
}
