import axiosInstance from '../../Config/axiosConfig';
import {
  transactionApprovalData,
  transactionLockData,
} from '../../Mocks/MockData';

// GET
export const getPendingTransactionApprovalListing = async (params) => {
  try {
    // const { data } = await axiosInstance.get('/user-api/beneficiary-register', {
    //   params,
    // });
    return transactionApprovalData.detail;
    return data.detail; // Assume this returns the listing object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};

export const getTransactionLockListing = async (params) => {
  try {
    // const { data } = await axiosInstance.get('/user-api/beneficiary-register', {
    //   params,
    // });
    return transactionLockData.detail;
    return data.detail; // Assume this returns the listing object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
