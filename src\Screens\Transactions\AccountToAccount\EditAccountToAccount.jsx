import React, { useRef, useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Formik, Form, ErrorMessage } from 'formik';
import { FaXmark } from 'react-icons/fa6';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomModal from '../../../Components/CustomModal';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  getAccountToAccountListing,
  updateAccountToAccount,
  getChequeNumbersByBank,
  getAccountBalance,
  deleteAccountToAccountAttachment,
  addAccountToAccountAttachment,
  getBanks,
  getCurrencyRate,
} from '../../../Services/Transaction/AccountToAccount';
import { showToast } from '../../../Components/Toast/Toast';
import useSettingsStore from '../../../Stores/SettingsStore';
import useFormStore from '../../../Stores/FormStore';
import useAccountsByType from '../../../Hooks/useAccountsByType';
import { useNavigate } from 'react-router-dom';
import { accountToAccountvalidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import { MOCK_EXCHANGE_RATES } from '../../../Mocks/MockData';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import { formatFileSize, getIcon } from '../../../Utils/Utils';
import Styles from '../Attachments.module.css';
import Skeleton from 'react-loading-skeleton';

const EditAccountToAccount = ({
  date,
  isDisabled,
  setIsDisabled,
  currencyOptions,
  setShowAddLedgerModal,
  setPageState,
  lastVoucherNumbers,
  setSearchTerm,
  searchTerm,
  newlyCreatedAccount,
}) => {
  const formikRef = useRef();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Form store for Special Commission navigation
  const {
    saveFormValues,
    getFormValues,
    hasFormValues,
    setLastVisitedPage,
    getLastVisitedPage,
    clearLastVisitedPage,
    clearFormValues,
  } = useFormStore();
  const formId = 'edit-account_to_account';

  // Attachments state (following Journal Voucher pattern - starts as array, becomes object)
  const [addedAttachments, setAddedAttachments] = useState([]);
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);

  // Cheque numbers and bank logic
  const [selectedBank, setSelectedBank] = useState('');
  const [chequeOptions, setChequeOptions] = useState([]);
  const [bankAccounts, setBankAccounts] = useState([]);
  const [showChequeNumber, setShowChequeNumber] = useState(false);
  // Account selection
  const [selectedDebitAccount, setSelectedDebitAccount] = useState(null);
  const [selectedCreditAccount, setSelectedCreditAccount] = useState(null);
  const [newAccountTriggeredFrom, setNewAccountTriggeredFrom] = useState('');

  // Special Commission state (mirroring Receipt Voucher logic)
  const [specialCommissionValues, setSpecialCommissionValues] = useState({});
  const [addedSpecialCommissionValues, setAddedSpecialCommissionValues] =
    useState(null);

  const [showBalances, setShowBalances] = useState(false);

  // Missing Currency Rate Modal state (following Journal Voucher pattern)
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [restoreValuesFromStore, setRestoreValuesFromStore] = useState(false);

  // Print settings
  const { getPrintSettings, updatePrintSetting } = useSettingsStore();

  // Get account options using custom hook
  const { getAccountsByTypeOptions } = useAccountsByType({
    includeBeneficiary: false,
    staleTime: 1000 * 60 * 5,
  });

  // Handle navigation from Rate of Exchange page (following Journal Voucher pattern)
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);
    if (lastPage === 'rate-of-exchange') {
      const savedFormData = getFormValues('edit-account-to-account');
      if (savedFormData) {
        // Set page state to edit and enable the form
        setPageState('edit');
        setIsDisabled(false);
        setRestoreValuesFromStore(true);
      }
    }
  }, []);

  // Restore special commission and form state if returning from special commission page
  useEffect(() => {
    const lastPage = getLastVisitedPage(formId);
    if (
      lastPage === 'special-commission' &&
      hasFormValues(formId) &&
      formikRef.current
    ) {
      const savedValues = getFormValues(formId);
      setSpecialCommissionValues({
        ledger: savedValues?.debitLedger,
        account: savedValues?.debitAccount,
        amount: savedValues?.fcAmount,
        currency: savedValues?.currency,
        date: date,
        current: lastVoucherNumbers?.current,
      });
      formikRef.current.setValues(savedValues);
      setIsDisabled(false);
      if (hasFormValues('special-commission')) {
        setAddedSpecialCommissionValues(getFormValues('special-commission'));
      }
      clearLastVisitedPage(formId);
    }
  }, []);

  // Restore form data if returning from Rate of Exchange page (following Journal Voucher pattern)
  useEffect(() => {
    if (restoreValuesFromStore) {
      const savedFormData = getFormValues('edit-account-to-account');
      if (savedFormData && formikRef.current) {
        formikRef.current.setValues(savedFormData);
        setAddedAttachments(savedFormData.addedAttachments || []);
        // Clear the saved data after restoring
        clearFormValues('edit-account-to-account');
        clearLastVisitedPage(formId);
        setRestoreValuesFromStore(false);
      }
    }
  }, [restoreValuesFromStore]);

  useEffect(() => {
    if (lastVoucherNumbers?.current) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        current: lastVoucherNumbers?.current,
      }));
    }
    if (date) {
      setSpecialCommissionValues((prev) => ({
        ...prev,
        date: date,
      }));
    }
  }, [lastVoucherNumbers?.current, date]);

  // Fetch bank accounts to determine which accounts are banks
  const { data: bankAccountsData } = useQuery({
    queryKey: ['bankAccounts'],
    queryFn: () => getBanks('bank'),
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Update bank accounts list when data is fetched
  useEffect(() => {
    if (bankAccountsData) {
      setBankAccounts(bankAccountsData);
    }
  }, [bankAccountsData]);

  // Helper function to check if an account is a bank account
  const isBankAccount = (accountId) => {
    return bankAccounts.some((bank) => bank.id === accountId);
  };

  // Logic to show/hide cheque number field and set selected bank
  useEffect(() => {
    let shouldShowCheque = false;
    let bankId = null;

    // Check if debit account is a bank
    if (
      selectedDebitAccount?.value &&
      isBankAccount(selectedDebitAccount.value)
    ) {
      shouldShowCheque = true;
      bankId = selectedDebitAccount.value;
    }
    // Check if credit account is a bank
    else if (
      selectedCreditAccount?.value &&
      isBankAccount(selectedCreditAccount.value)
    ) {
      shouldShowCheque = true;
      bankId = selectedCreditAccount.value;
    }

    setShowChequeNumber(shouldShowCheque);

    if (bankId && bankId !== selectedBank) {
      setSelectedBank(bankId);
    } else if (!shouldShowCheque) {
      setSelectedBank('');
      setChequeOptions([]);
    }
  }, [selectedDebitAccount, selectedCreditAccount, bankAccounts]);

  // Fetch cheque numbers when a bank is selected
  const { data: chequeNumbersData } = useQuery({
    queryKey: ['chequeNumbers', selectedBank],
    queryFn: () => getChequeNumbersByBank(selectedBank),
    enabled: !!selectedBank && showChequeNumber,
  });

  // Update cheque options when cheque numbers data changes
  useEffect(() => {
    if (showChequeNumber) {
      if (chequeNumbersData) {
        setChequeOptions(
          chequeNumbersData.map((cheque) => ({
            label: cheque.cheque_number,
            value: cheque.id,
          }))
        );
      } else if (selectedBank) {
        setChequeOptions([
          { label: 'Loading...', value: null, isDisabled: true },
        ]);
      } else {
        setChequeOptions([
          { label: 'Select Bank Account First', value: null, isDisabled: true },
        ]);
      }
    } else {
      setChequeOptions([]);
    }
  }, [chequeNumbersData, selectedBank, showChequeNumber]);

  // Fetch account balances
  const { data: debitAccountBalance } = useQuery({
    queryKey: ['accountBalance', selectedDebitAccount?.value],
    queryFn: () =>
      getAccountBalance(
        selectedDebitAccount.value,
        selectedDebitAccount.accountType
      ),
    enabled: !!selectedDebitAccount?.value,
    staleTime: 1000 * 60 * 2,
    retry: 1,
  });
  const { data: creditAccountBalance } = useQuery({
    queryKey: ['accountBalance', selectedCreditAccount?.value],
    queryFn: () =>
      getAccountBalance(
        selectedCreditAccount.value,
        selectedCreditAccount.accountType
      ),
    enabled: !!selectedCreditAccount?.value,
    staleTime: 1000 * 60 * 2,
  });

  // Submission logic (fix stack error by not calling setState in render or in a loop)
  const updateAccountToAccountMutation = useMutation({
    mutationFn: (payload) =>
      updateAccountToAccount(accountToAccountData.voucher_no, payload),
    onSuccess: (data) => {
      showToast('Account to Account Updated!', 'success');
      if (getPrintSettings('account_to_account')) {
        if (data?.detail?.pdf_url) {
          window.open(data.detail.pdf_url, '_blank');
        }
      }
      // Invalidate and refetch queries following Receipt Voucher pattern
      queryClient.invalidateQueries(['accountToAccount', searchTerm]);
      queryClient.refetchQueries(['accountToAccount', searchTerm]);
      queryClient.invalidateQueries(['accountToAccountListing']);
      queryClient.refetchQueries(['accountToAccountListing']);

      // Reset form and state
      handleResetForm();
    },
    onError: (error) => {
      showToast(error.message || 'Error updating Account to Account', 'error');
    },
  });

  // Fetch the voucher details
  const {
    data: { data: [accountToAccountData] = [] } = {},
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['accountToAccount', searchTerm],
    queryFn: () => getAccountToAccountListing({ search: searchTerm }),
    enabled: !!searchTerm,
  });

  // Fetch currency rate for the selected Currency (following Journal Voucher pattern)
  const { data: currencyRate, isLoading: isLoadingCurrencyRate } = useQuery({
    queryKey: ['a2a-edit', 'currencyRate', selectedCurrency],
    queryFn: () => getCurrencyRate(selectedCurrency, date),
    enabled: !!selectedCurrency,
    retry: 1,
    staleTime: 0, // Mark data as stale immediately after fetching
    gcTime: 0, // Remove data from cache immediately after becoming unused
    refetchOnMount: true,
  });

  // Handle currency rate response (following Journal Voucher pattern)
  useEffect(() => {
    if (currencyRate?.rate) {
      // Currency rate is available, no action needed
      console.log('Currency rate available:', currencyRate.rate);
    } else if (currencyRate && selectedCurrency) {
      // Currency rate is missing, show modal
      setCurrencyToSelect(selectedCurrency);
      setShowMissingCurrencyRateModal(true);
      // Reset currency selection
      setSelectedCurrency(null);
    }
  }, [currencyRate, selectedCurrency]);

  // Try different possible data structures
  const account_to_account = accountToAccountData?.account_to_account;

  const initialValues = {
    debitLedger: account_to_account?.debit_account_ledger,
    creditLedger: account_to_account?.credit_account_ledger,
    debitAccount: account_to_account?.debit_account_details?.id,
    creditAccount: account_to_account?.credit_account_details?.id,
    accountTitle: account_to_account?.account_title,
    accountTitleDisplay: account_to_account?.account_title,
    chequeNumber: account_to_account?.cheque?.id,
    currency: account_to_account?.currency?.id,
    fcAmount: account_to_account?.fc_amount,
    debitNarration: account_to_account?.debit_account_narration,
    creditNarration: account_to_account?.credit_account_narration,
    comment: account_to_account?.comment,
  };
  useEffect(() => {
    if (account_to_account?.debit_account_details?.id) {
      setSelectedBank(account_to_account?.debit_account_details?.id);
    }
  }, [account_to_account?.debit_account_details?.id]);

  const handleSubmit = async () => {
    if (!formikRef.current) return;
    const errors = await formikRef.current.validateForm();
    if (Object.keys(errors).length > 0) {
      formikRef.current.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
      return;
    }
    const values = formikRef.current.values;

    // Map form fields to API fields (same as New page)
    const payload = {
      debit_account_ledger: values.debitLedger,
      debit_account_id: values.debitAccount,
      credit_account_ledger: values.creditLedger,
      credit_account_id: values.creditAccount,
      account_title: values.accountTitleDisplay,
      currency_id: values.currency,
      fc_amount: values.fcAmount,
      debit_account_narration: values.debitNarration,
      credit_account_narration: values.creditNarration,
      comment: values.comment,
      date,
      // Only include cheque_number_id if selected
      ...(values.chequeNumber ? { cheque_number_id: values.chequeNumber } : {}),
      ...addedAttachments,
    };

    // Special Commission (flattened to bracket notation)
    if (addedSpecialCommissionValues) {
      const sc = addedSpecialCommissionValues;
      payload['special_commission[transaction_no]'] = sc.transaction_no;
      payload['special_commission[date]'] = sc.date;
      payload['special_commission[commission_type]'] = sc.commission_type;
      payload['special_commission[ledger]'] = sc.ledger;
      payload['special_commission[account_id]'] = sc.account_id;
      payload['special_commission[currency_id]'] = sc.currency_id;
      payload['special_commission[amount]'] = sc.amount;
      payload['special_commission[description]'] = sc.description;
      payload['special_commission[commission]'] = sc.commission;
      payload['special_commission[total_commission]'] = sc.total_commission;
      if (Array.isArray(sc.distribution)) {
        sc.distribution.forEach((dist, idx) => {
          payload[`special_commission[distribution][${idx}][ledger]`] =
            dist.ledger;
          payload[
            `special_commission[distribution][${idx}][credit_account_id]`
          ] = dist.credit_account_id;
          payload[`special_commission[distribution][${idx}][narration]`] =
            dist.narration;
          payload[`special_commission[distribution][${idx}][percentage]`] =
            dist.percentage;
          payload[`special_commission[distribution][${idx}][amount]`] =
            dist.amount;
        });
      }
    }

    console.log('Final payload with attachments:', payload);
    console.log('addedAttachments:', addedAttachments);
    updateAccountToAccountMutation.mutate(payload);
  };

  // Reset form function following Receipt Voucher pattern
  const handleResetForm = () => {
    setPageState('view');
    setIsDisabled(true);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
    setAddedAttachments([]);
    clearFormValues(formId);
    clearFormValues('special-commission');
    clearFormValues('edit-account-to-account');
    setAddedSpecialCommissionValues(null);
    // Reset selected accounts
    setSelectedDebitAccount(null);
    setSelectedCreditAccount(null);
    setSelectedBank(null);
    setChequeOptions([]);
    // Reset currency-related state
    setSelectedCurrency(null);
    setCurrencyToSelect(null);
    setShowMissingCurrencyRateModal(false);
    setRestoreValuesFromStore(false);
  };

  // Handle file removal (following SuspenseVoucher pattern)
  const handleRemoveFile = (file) => {
    setAddedAttachments((prevFiles) => {
      const updatedFiles = { ...prevFiles };

      for (const key in updatedFiles) {
        if (
          updatedFiles[key]?.name === file.name &&
          updatedFiles[key]?.size === file.size
        ) {
          delete updatedFiles[key];
          break;
        }
      }

      return updatedFiles;
    });
  };

  const handleCancel = () => {
    handleResetForm();
  };

  // Special Commission navigation (mirroring Receipt Voucher)
  const handleNavigateToSpecialCommissionPage = () => {
    const requiredFields = [
      'debitLedger',
      'debitAccount',
      'fcAmount',
      'currency',
    ];
    const missingFields = requiredFields.filter(
      (field) => !formikRef.current.values[field]
    );
    if (missingFields.length > 0) {
      const touchedFields = {};
      requiredFields.forEach((field) => {
        touchedFields[field] = true;
      });
      formikRef.current.setTouched({
        ...formikRef.current.touched,
        ...touchedFields,
      });
      return;
    }
    if (formikRef.current && !isDisabled) {
      saveFormValues(formId, formikRef.current.values);
      setLastVisitedPage(formId, 'special-commission');
    }
    const hasExistingCommission = !!addedSpecialCommissionValues;
    setPageState('edit');
    navigate('/transactions/special-commission', {
      state: {
        fromPage: 'account_to_account',
        pageState: 'edit',
        searchTerm: searchTerm,
        values: {
          rvValues: {
            ...specialCommissionValues,
            ledger: formikRef.current.values.debitLedger,
            account: formikRef.current.values.debitAccount,
            amount: formikRef.current.values.fcAmount,
            currency: formikRef.current.values.currency,
            date: date,
            current: lastVoucherNumbers?.current,
          },
          isEdit: hasExistingCommission,
        },
      },
    });
  };

  if (isError) {
    console.log(error);
  }

  if (isLoading) {
    return (
      <div className="d-card ">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <div className="row mb-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="col-12 col-sm-6 mb-3 align-items-center"
                  style={{ height: 56 }}
                >
                  <Skeleton
                    style={{ marginTop: 28 }}
                    duration={1}
                    width={'50%'}
                    baseColor="#ddd"
                    height={22}
                  />
                </div>
              ))}
              <div
                className="col-12 mb-3 align-items-center"
                style={{ height: 56 }}
              >
                <Skeleton
                  style={{ marginTop: 28 }}
                  duration={1}
                  width={'100%'}
                  baseColor="#ddd"
                  height={22}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={initialValues}
          validationSchema={accountToAccountvalidationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            setFieldValue,
          }) => {
            // Helper function to handle account loading
            const handleLedgerChange = (
              ledgerType,
              fieldName,
              setAccountField
            ) => {
              setFieldValue(fieldName, ledgerType);
              setFieldValue(setAccountField, ''); // Clear account when ledger changes
            };

            return (
              <Form>
                <div className="row">
                  <div
                    className={
                      isDisabled
                        ? 'col-12 col-lg-10 col-xl-9 col-xxl-10'
                        : 'col-12 col-lg-10 col-xl-9 col-xxl-9'
                    }
                  >
                    <div className="row">
                      {/* Debit Account Section */}
                      <div className="col-12 col-sm-5 mb-45">
                        <CombinedInputs
                          label="Debit Account"
                          type1="select"
                          type2="select"
                          name1="debitLedger"
                          name2="debitAccount"
                          value1={values.debitLedger}
                          value2={
                            values.debitAccount ||
                            (newlyCreatedAccount?.id &&
                            newAccountTriggeredFrom === 'debit'
                              ? newlyCreatedAccount.id
                              : '')
                          }
                          options1={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          options2={getAccountsByTypeOptions(
                            values.debitLedger
                          )}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            handleLedgerChange(
                              selected.value,
                              'debitLedger',
                              'debitAccount'
                            );
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                              setNewAccountTriggeredFrom('debit');
                            } else {
                              setFieldValue('debitAccount', selected.value);
                              // Track selected debit account for balance fetching
                              setSelectedDebitAccount({
                                value: selected.value,
                                label: selected.label,
                                accountType: values.debitLedger,
                              });
                            }
                          }}
                        />
                      </div>

                      {/* Credit Account Section */}
                      <div className="col-12 col-sm-7 mb-45">
                        <div className="d-flex align-items-end gap-2">
                          <div className="flex-grow-1">
                            <CombinedInputs
                              label="Credit Account"
                              type1="select"
                              type2="select"
                              name1="creditLedger"
                              name2="creditAccount"
                              value1={values.creditLedger}
                              value2={
                                values.creditAccount ||
                                (newlyCreatedAccount?.id &&
                                newAccountTriggeredFrom === 'credit'
                                  ? newlyCreatedAccount.id
                                  : '')
                              }
                              options1={[
                                { label: 'PL', value: 'party' },
                                { label: 'GL', value: 'general' },
                                { label: 'WIC', value: 'walkin' },
                              ]}
                              options2={getAccountsByTypeOptions(
                                values.creditLedger
                              )}
                              isDisabled={isDisabled}
                              handleBlur={handleBlur}
                              placeholder1="Ledger"
                              placeholder2="Select Account"
                              className1="ledger"
                              className2="account"
                              onChange1={(selected) => {
                                handleLedgerChange(
                                  selected.value,
                                  'creditLedger',
                                  'creditAccount'
                                );
                              }}
                              onChange2={(selected) => {
                                if (
                                  selected.label
                                    ?.toLowerCase()
                                    ?.startsWith('add new')
                                ) {
                                  setShowAddLedgerModal(
                                    selected.label?.toLowerCase()
                                  );
                                  setNewAccountTriggeredFrom('credit');
                                } else {
                                  setFieldValue(
                                    'creditAccount',
                                    selected.value
                                  );
                                  // Track selected credit account for balance fetching
                                  setSelectedCreditAccount({
                                    value: selected.value,
                                    label: selected.label,
                                    accountType: values.creditLedger,
                                  });
                                }
                              }}
                            />
                          </div>
                          {/* Switch Account Button */}
                          <div className="d-flex justify-content-end mt-2 flex-shrink-0">
                            <CustomButton
                              text="Switch Account"
                              type="button"
                              variant="secondaryButton"
                              size="sm"
                              disabled={
                                isDisabled ||
                                (!values.debitAccount && !values.creditAccount)
                              }
                              onClick={() => {
                                // Switch debit and credit accounts
                                const tempDebitLedger =
                                  values.debitLedger || '';
                                const tempDebitAccount =
                                  values.debitAccount || '';
                                const tempCreditLedger =
                                  values.creditLedger || '';
                                const tempCreditAccount =
                                  values.creditAccount || '';
                                const tempSelectedDebit = selectedDebitAccount;
                                const tempSelectedCredit =
                                  selectedCreditAccount;

                                // Update form values
                                setFieldValue('debitLedger', tempCreditLedger);
                                setFieldValue(
                                  'debitAccount',
                                  tempCreditAccount
                                );
                                setFieldValue('creditLedger', tempDebitLedger);
                                setFieldValue(
                                  'creditAccount',
                                  tempDebitAccount
                                );

                                // Update selected account states
                                setSelectedDebitAccount(tempSelectedCredit);
                                setSelectedCreditAccount(tempSelectedDebit);

                                console.log('Switched accounts:', {
                                  from: {
                                    debit: tempDebitAccount,
                                    credit: tempCreditAccount,
                                  },
                                  to: {
                                    debit: tempCreditAccount,
                                    credit: tempDebitAccount,
                                  },
                                });
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Cheque Number - Only show when a bank account is selected */}
                      {showChequeNumber && (
                        <div className="col-12 col-sm-6 mb-45">
                          <SearchableSelect
                            name="chequeNumber"
                            label="Cheque Number"
                            options={chequeOptions}
                            value={values.chequeNumber}
                            onChange={(selected) =>
                              setFieldValue('chequeNumber', selected.value)
                            }
                            onBlur={handleBlur}
                            placeholder="Select Cheque Number"
                            isDisabled={isDisabled}
                          />
                          <ErrorMessage
                            name="chequeNumber"
                            component="div"
                            className="input-error-message text-danger"
                          />
                        </div>
                      )}

                      {/* Account Title Show/Hide */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name="accountTitleDisplay"
                          label="Account Title"
                          options={[
                            { label: 'Show', value: 'show' },
                            { label: 'Hide', value: 'hide' },
                          ]}
                          value={values.accountTitleDisplay}
                          onChange={(selected) =>
                            setFieldValue('accountTitleDisplay', selected.value)
                          }
                          onBlur={handleBlur}
                          placeholder="Show"
                          isDisabled={isDisabled}
                        />
                      </div>

                      {/* Currency and FC Amount */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Currency"
                          type1="select"
                          type2="input"
                          name1="currency"
                          name2="fcAmount"
                          value1={values.currency}
                          value2={values.fcAmount}
                          options1={currencyOptions}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Currency"
                          placeholder2="Amount"
                          inputType2="number"
                          className1="currency"
                          className2="amount"
                          onChange1={(selected) => {
                            setFieldValue('currency', selected.value);
                            setSelectedCurrency(selected.value);
                          }}
                          onChange2={handleChange}
                        />
                      </div>
                      {!!showChequeNumber && (
                        <div className="col-0 col-sm-6 mb-45" />
                      )}
                      {/* Debit Account Narration */}
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="debitNarration"
                          label="Debit Account Narration"
                          type="textarea"
                          rows={3}
                          value={values.debitNarration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Debit Account Narration"
                          disabled={isDisabled}
                          error={
                            touched.debitNarration && errors.debitNarration
                          }
                        />
                      </div>

                      {/* Credit Account Narration */}
                      <div className="col-12 col-sm-6 mb-3">
                        <CustomInput
                          name="creditNarration"
                          label="Credit Account Narration"
                          type="textarea"
                          rows={3}
                          value={values.creditNarration}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Credit Account Narration"
                          disabled={isDisabled}
                          error={
                            touched.creditNarration && errors.creditNarration
                          }
                        />
                      </div>

                      {/* Comment */}
                      <div className="col-12 mb-3">
                        <CustomInput
                          name="comment"
                          label="Comment"
                          type="textarea"
                          rows={4}
                          value={values.comment}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          placeholder="Enter Comment"
                          disabled={isDisabled}
                          error={touched.comment && errors.comment}
                        />
                      </div>

                      {/* Attachments Display (following Journal Voucher pattern) */}
                      <div className="col-12 mb-3">
                        <div className="d-flex flex-wrap gap-2">
                          {Object.values(addedAttachments)?.map(
                            (file, index) => (
                              <div key={index} style={{ position: 'relative' }}>
                                {console.log(
                                  'File in EditAccountToAccount:',
                                  file
                                )}
                                <div className={Styles.uploadedFiles}>
                                  <div className={Styles.nameIconWrapper}>
                                    <div
                                      className="beechMein"
                                      style={{ minWidth: 28 }}
                                    >
                                      {getIcon(file.type)}
                                    </div>
                                    <div
                                      style={{ width: 126 }}
                                      className="d-flex flex-column flex-1"
                                    >
                                      <p className={Styles.fileName}>
                                        {file.name}
                                      </p>
                                      <p className={Styles.size}>
                                        {formatFileSize(file.size)}
                                      </p>
                                    </div>
                                  </div>
                                  <button
                                    type="button"
                                    className={Styles.fileRemoveButton}
                                    onClick={() => {
                                      handleRemoveFile(file);
                                    }}
                                    disabled={isDisabled}
                                  >
                                    <FaXmark size={16} />
                                  </button>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Add Special Commission Button */}
                    <div className="d-flex mb-5">
                      <CustomButton
                        type="button"
                        onClick={handleNavigateToSpecialCommissionPage}
                        text={`${
                          !!addedSpecialCommissionValues ? 'Edit' : 'Add'
                        } Special Commission`}
                        disabled={isDisabled}
                      />
                    </div>
                    {!!addedSpecialCommissionValues ? (
                      <p
                        style={{
                          color: '#22C55E',
                          fontSize: '14px',
                          marginBottom: '20px',
                        }}
                      >
                        Special Commission has been added successfully.
                      </p>
                    ) : null}

                    {/* Checkboxes */}
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={(e) => setShowBalances(e.target.checked)}
                      />

                      <CustomCheckbox
                        label="Print"
                        checked={getPrintSettings('account_to_account')}
                        onChange={(e) => {
                          updatePrintSetting(
                            'account_to_account',
                            e.target.checked
                          );
                        }}
                        style={{ border: 'none', margin: 0 }}
                        readOnly={isDisabled}
                      />
                    </div>
                  </div>

                  {/* Show balance cards only when !isDisabled */}
                  {!isDisabled && (
                    <div className="col-xxl-3 col-12">
                      {/* Account Balance Cards */}

                      {showBalances && (
                        <div>
                          {/* Debit Account Balance */}
                          {selectedDebitAccount && (
                            <AccountBalanceCard
                              heading="Debit Account Balance"
                              accountName={selectedDebitAccount.label}
                              balances={debitAccountBalance?.balances || []}
                              loading={debitAccountBalance === undefined}
                            />
                          )}
                          {/* Credit Account Balance */}
                          {selectedCreditAccount && (
                            <AccountBalanceCard
                              heading="Credit Account Balance"
                              accountName={selectedCreditAccount.label}
                              balances={creditAccountBalance?.balances || []}
                              loading={creditAccountBalance === undefined}
                            />
                          )}
                        </div>
                      )}
                      {/* Exchange Rates Card */}
                      <h6 className="mb-2">
                        Live Exchange Rates Against Base Currency
                      </h6>
                      <div className="d-card account-balance-card">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <div className="d-flex align-items-center account-name w-100">
                            <span className="me-2" style={{ color: '#6B7280' }}>
                              Inverse
                            </span>
                            <div className="form-check form-switch">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                style={{ cursor: 'pointer' }}
                              />
                            </div>
                          </div>
                        </div>
                        <table className="w-100">
                          <thead>
                            <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                FCy
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Rates
                              </th>
                              <th
                                style={{
                                  padding: '8px 0',
                                  color: '#6B7280',
                                  fontWeight: '500',
                                }}
                              >
                                Change (24h)
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {MOCK_EXCHANGE_RATES.map((rate, index) => (
                              <tr key={index}>
                                <td style={{ padding: '8px 0' }}>
                                  {rate.currency}
                                </td>
                                <td style={{ padding: '8px 0' }}>
                                  {rate.rate}
                                </td>
                                <td
                                  style={{
                                    padding: '8px 0',
                                    color: rate.isPositive
                                      ? '#22C55E'
                                      : '#EF4444',
                                  }}
                                >
                                  {rate.change}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Update', onClick: handleSubmit },
          {
            text: 'Cancel',
            onClick: handleCancel,
            variant: 'secondaryButton',
          },
        ]}
        loading={updateAccountToAccountMutation.isPending}
        onAttachmentClick={() => setShowAttachmentsModal(true)}
        lastVoucherNumbers={lastVoucherNumbers}
        setPageState={setPageState}
        setSearchTerm={setSearchTerm}
      />

      {/* Upload Attachements Modal */}
      <CustomModal
        show={showAttachmentsModal}
        close={() => setShowAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          showModal={showAttachmentsModal}
          closeModal={() => setShowAttachmentsModal(false)}
          item={accountToAccountData}
          deleteService={deleteAccountToAccountAttachment}
          uploadService={addAccountToAccountAttachment}
          closeUploader={() => setShowAttachmentsModal(false)}
          voucherAttachment={true}
          queryToInvalidate={['accountToAccount', searchTerm]}
        />
      </CustomModal>

      {/* Missing Currency Rate Modal (following Journal Voucher pattern) */}
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          // Save form data before navigation (following Journal Voucher pattern)
          if (formikRef.current) {
            saveFormValues('edit-account-to-account', {
              ...formikRef.current.values,
              addedAttachments,
              date,
            });
            setLastVisitedPage(formId, 'rate-of-exchange');
          }
          navigate('/transactions/remittance-rate-of-exchange', {
            state: { currencyToSelect },
          });
        }}
      />
    </>
  );
};

export default EditAccountToAccount;
