{"name": "milestone-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.59.16", "axios": "^1.7.7", "bootstrap": "^5.3.3", "formik": "^2.4.6", "lodash": "^4.17.21", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-loading-skeleton": "^3.5.0", "react-paginate": "^8.2.0", "react-pagination": "^1.0.0", "react-phone-number-input": "^3.4.9", "react-router-dom": "^6.27.0", "react-select": "^5.8.3", "react-signature-canvas": "^1.1.0-alpha.2", "react-spinners": "^0.14.1", "react-toastify": "^10.0.6", "trim-canvas": "^0.1.2", "vite-plugin-svgr": "^4.3.0", "yup": "^1.4.0", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "vite": "^5.4.10"}}