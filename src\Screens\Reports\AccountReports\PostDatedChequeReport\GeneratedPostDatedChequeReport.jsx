import React from 'react';
import { Col, Row } from 'react-bootstrap';
import BackButton from '../../../../Components/BackButton';
import CustomButton from '../../../../Components/CustomButton';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import withFilters from '../../../../HOC/withFilters ';
import { postDatedChequesData } from '../../../../Mocks/MockData';
import { postDatedChequesHeaders } from '../../../../Utils/Constants/TableHeaders';

const GeneratedPostDatedChequeReport = ({
  filters,
  setFilters,
  pagination,
}) => {
  const tableData = postDatedChequesData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">Post Dated Cheque Report</h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={postDatedChequesHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'PDC', options: [{ value: 'All', label: 'All' }] },
              { title: 'Ledger', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'From Account',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'To Account',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'Sort By',
                options: [
                  { value: 'asc', label: 'Ascending' },
                  { value: 'desc', label: 'Descending' },
                ],
              },
              {
                title: 'Cheque Status',
                options: [{ value: 'All', label: 'All' }],
              },
              {
                title: 'Showing',
                options: [{ value: 'All', label: 'All' }],
              },
            ]}
            rangeFilters={[
              { title: 'Cheque No. Range' },
              { title: 'Amount Range' },
            ]}
            dateFilters={[
              { title: 'Due Date Range' },
              { title: 'Posting Date Range' },
            ]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={postDatedChequesHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.title_of_account}</td>
                    <td>{item.cheque_no}</td>
                    <td>{item.base_amount}</td>
                    <td>{item.due_date}</td>
                    <td>{item.drawn_on}</td>
                    <td>{item.posting_date}</td>
                    <td>{item.status}</td>
                    <td>{item.fcy}</td>
                    <td>{item.fc_amount}</td>
                    <td>{item.cost_center}</td>
                    <td>{item.discount_collection_bank}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(GeneratedPostDatedChequeReport);
