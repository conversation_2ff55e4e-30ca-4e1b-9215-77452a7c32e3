import { ErrorMessage } from 'formik';
import React from 'react';
import CustomInput from '../CustomInput';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import './CombinedInputs.css';

const CombinedInputs = ({
  label,
  type1 = 'select',
  type2 = 'select',
  options1 = [],
  options2 = [],
  value1,
  value2,
  onChange1,
  onChange2,
  isDisabled = false,
  isfirstInputDisabled = false,
  isSecondInputDisabled = false,
  handleBlur,
  name1,
  name2,
  placeholder1 = 'Select',
  placeholder2 = 'Select',
  inputType1 = 'text',
  inputType2 = 'text',
  inputProps1 = {},
  inputProps2 = {},
  className1 = 'input1',
  className2 = 'input2',
  min1,
  max1,
  min2,
  max2,
  additionalProps,
}) => {
  // Render the first input based on type
  const renderInput1 = () => {
    if (type1 === 'select') {
      return (
        <>
          <SearchableSelect
            label={null}
            options={options1}
            value={value1}
            onChange={onChange1}
            isDisabled={isDisabled || isfirstInputDisabled}
            placeholder={placeholder1}
            name={name1}
            handleBlur={handleBlur}
            showBorders={false}
            {...inputProps1}
          />
        </>
      );
    } else {
      return (
        <CustomInput
          type={inputType1}
          name={name1}
          value={value1 || ''}
          onChange={onChange1}
          disabled={isDisabled || isfirstInputDisabled}
          placeholder={placeholder1}
          onBlur={handleBlur}
          className={`${className1}-input`}
          showBorders={false}
          error={false}
          min={inputType1 === 'number' ? min1 : undefined}
          max={inputType1 === 'number' ? max1 : undefined}
          {...inputProps1}
        />
      );
    }
  };

  // Render the second input based on type
  const renderInput2 = () => {
    if (type2 === 'select') {
      return (
        <SearchableSelect
          label={null}
          options={options2}
          value={value2}
          onChange={onChange2}
          isDisabled={isDisabled || isSecondInputDisabled}
          placeholder={placeholder2}
          name={name2}
          handleBlur={handleBlur}
          showBorders={false}
          {...inputProps2}
        />
      );
    } else {
      return (
        <CustomInput
          type={inputType2}
          name={name2}
          value={value2 || ''}
          onChange={onChange2}
          disabled={isDisabled || isSecondInputDisabled || inputProps2.readOnly}
          placeholder={placeholder2}
          onBlur={handleBlur}
          className={`${className2}-input`}
          showBorders={false}
          error={false}
          min={inputType2 === 'number' ? min2 : undefined}
          max={inputType2 === 'number' ? max2 : undefined}
          {...inputProps2}
        />
      );
    }
  };

  return (
    <div className="combined-select-container">
      {label && <label className="mainLabel">{label}</label>}

      <div className="combined-select-input">
        <div className={`combined-select-left ${className1}-container`}>
          {renderInput1()}
          <ErrorMessage
            name={name1}
            component="div"
            className="input-error-message text-danger"
          />
        </div>

        <div className="separator-between-selects">|</div>

        <div className={`combined-select-right ${className2}-container`}>
          {renderInput2()}
          <ErrorMessage
            name={name2}
            component="div"
            className="input-error-message text-danger"
          />
        </div>
      </div>
      {additionalProps?.isLoadingCurrencyRate && (
        <p className="m-0 position-absolute primary-color-text">
          Fetching rate...
        </p>
      )}
    </div>
  );
};

export default CombinedInputs;
