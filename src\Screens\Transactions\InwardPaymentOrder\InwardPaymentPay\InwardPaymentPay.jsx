import { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useParams } from 'react-router-dom';
import BackButton from '../../../../Components/BackButton';
import BeneficiaryRegisterForm from '../../../../Components/BeneficiaryRegisterForm/BeneficiaryRegisterForm';
import ChartOfAccountForm from '../../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomModal from '../../../../Components/CustomModal';
import PartyLedgerForm from '../../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import { usePageTitle } from '../../../../Hooks/usePageTitle';
import '../../transactionStyles.css';
import NewInwardPaymentPay from './NewInwardPaymentPay';

const InwardPaymentPay = () => {
  usePageTitle('Inward Payment Pay');
  const { id } = useParams();
  const [pageState, setPageState] = useState('new');
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState(null);

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new beneficiary':
        return (
          <BeneficiaryRegisterForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  return (
    <>
      <section className="position-relative">
        <div
          style={{ height: 43 }}
          className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-4"
        >
          <div>
            {pageState == 'listing' ||
              (pageState == 'view' && (
                <BackButton
                  handleBack={() => {
                    setPageState('new');
                  }}
                />
              ))}
            <h2 className="screen-title mb-0">Inward Payment Pay

            </h2>
          </div>
        </div>
        <Row>
          <Col xs={12}>
            <NewInwardPaymentPay
              setShowAddLedgerModal={setShowAddLedgerModal}
              newlyCreatedAccount={newlyCreatedAccount}
              uploadAttachmentsModal={uploadAttachmentsModal}
              setUploadAttachmentsModal={setUploadAttachmentsModal}
              selectedFiles={selectedFiles}
              setSelectedFiles={setSelectedFiles}
            />
          </Col>
        </Row>
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>
    </>
  );
};

export default InwardPaymentPay;
