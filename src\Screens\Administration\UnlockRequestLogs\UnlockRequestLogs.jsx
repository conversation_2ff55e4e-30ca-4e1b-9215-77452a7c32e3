import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { HiOutlineEye } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import StatusChip from '../../../Components/StatusChip/StatusChip';
import TableActionDropDown from '../../../Components/TableActionDropDown/TableActionDropDown';
import withFilters from '../../../HOC/withFilters ';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { useFetchTableData } from '../../../Hooks/useTable';
import { unlockRequestFilterOptions } from '../../../Utils/Constants/TableFilter';
import { unlockRequestLogsHeaders } from '../../../Utils/Constants/TableHeaders';
import { formatDate, serialNum, showErrorToast } from '../../../Utils/Utils';
import { unlockRequestLogsData } from '../../../Mocks/MockData';

const UnlockRequestLogs = ({
  filters,
  setFilters,
  pagination,
  updatePagination,
}) => {
  usePageTitle('Unlock Request Logs');
  const navigate = useNavigate();

  let data,
    isLoading,
    isError,
    error = {};
  // const { data, isLoading, isError, error } = useFetchTableData(
  //   'unlockRequestLogs',
  //   filters,
  //   updatePagination,
  //   getUnlockRequestLogs
  // );

  // const unlockRequestData = data?.data || [];
  const unlockRequestData = unlockRequestLogsData;

  if (isError) {
    showErrorToast(error);
  }

  return (
    <>
      <section>
        <div className="d-flex justify-content-between flex-wrap mb-3">
          <h2 className="screen-title mb-0">Unlock Request Logs</h2>
        </div>
        <Row>
          <Col xs={12}>
            <CustomTable
              filters={filters}
              setFilters={setFilters}
              headers={unlockRequestLogsHeaders}
              pagination={pagination}
              isLoading={isLoading}
              selectOptions={[
                {
                  title: 'status',
                  options: unlockRequestFilterOptions,
                },
              ]}
            >
              {(unlockRequestLogsData.length || isError) && (
                <tbody>
                  {isError && (
                    <tr>
                      <td colSpan={unlockRequestLogsHeaders.length}>
                        <p className="text-danger mb-0">
                          Unable to fetch data at this time
                        </p>
                      </td>
                    </tr>
                  )}
                  {unlockRequestLogsData?.map((item, index) => (
                    <tr key={item.id}>
                      <td>
                        {formatDate(
                          item?.request_date_time,
                          'DD/MM/YYYY - HH:MM'
                        )}
                      </td>
                      <td>{item?.requestor_name}</td>
                      <td>
                        {formatDate(
                          item?.approval_rejection_date_time,
                          'DD/MM/YYYY - HH:MM'
                        )}
                      </td>
                      <td>
                        <StatusChip status={item.status} />
                      </td>
                      <td>
                        <TableActionDropDown
                          actions={[
                            {
                              name: 'View',
                              icon: HiOutlineEye,
                              onClick: () => {
                                navigate(`${item.id}`);
                              },
                              className: 'view',
                            },
                          ]}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </CustomTable>
          </Col>
        </Row>
      </section>
    </>
  );
};

export default withFilters(UnlockRequestLogs);
