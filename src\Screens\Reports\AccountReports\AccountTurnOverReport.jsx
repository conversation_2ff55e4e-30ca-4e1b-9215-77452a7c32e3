import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CustomButton from '../../../Components/CustomButton';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import withFilters from '../../../HOC/withFilters ';
import { accountTurnoverReportData } from '../../../Mocks/MockData';
import { accountTurnoverReportHeaders } from '../../../Utils/Constants/TableHeaders';

const AccountTurnOverReport = ({ filters, setFilters, pagination }) => {
  const tableData = accountTurnoverReportData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">Account Turnover Report</h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={accountTurnoverReportHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              {
                title: 'Account Group',
                options: [{ value: 'All', label: 'All' }],
              },
              { title: 'FCy', options: [{ value: 'All', label: 'All' }] },
            ]}
            dateFilters={[{ title: 'Date Range' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td colSpan={accountTurnoverReportHeaders.length}>
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.ledger}</td>
                    <td>{item.account}</td>
                    <td>{item.contact_no}</td>
                    <td>{item.fcy}</td>
                    <td>{item.balance_bf}</td>
                    <td>{item.total_debit}</td>
                    <td>{item.total_credit}</td>
                    <td>{item.balance_cf}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(AccountTurnOverReport);
