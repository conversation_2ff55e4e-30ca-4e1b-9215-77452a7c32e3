import React from 'react';
import { Col, Row } from 'react-bootstrap';
import BackButton from '../../../../Components/BackButton';
import CustomButton from '../../../../Components/CustomButton';
import CustomTable from '../../../../Components/CustomTable/CustomTable';
import withFilters from '../../../../HOC/withFilters ';
import { walkInCustomerOutstandingBalanceData } from '../../../../Mocks/MockData';
import { walkInCustomerOutstandingBalanceHeaders } from '../../../../Utils/Constants/TableHeaders';

const GeneratedWalkInCustomerOutstandingBalance = ({
  filters,
  setFilters,
  pagination,
}) => {
  const tableData = walkInCustomerOutstandingBalanceData.detail;
  const isLoading = false;
  const isError = false;

  return (
    <section>
      <div className="d-flex justify-content-between flex-wrap mb-3">
        <h2 className="screen-title m-0 d-inline">
          Walk-In Customer Outstanding Balance
        </h2>
        <div className="d-flex gap-3 flex-wrap">
          <CustomButton
            text={'Export to Excel'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to Excel');
            }}
          />
          <CustomButton
            text={'Export to PDF'}
            variant={'secondaryButton'}
            onClick={() => {
              console.log('Export to PDF');
            }}
          />
          <CustomButton
            text={'Print'}
            onClick={() => {
              console.log('Print');
            }}
          />
        </div>
      </div>
      <Row>
        <Col xs={12}>
          <CustomTable
            filters={filters}
            setFilters={setFilters}
            headers={walkInCustomerOutstandingBalanceHeaders}
            pagination={pagination}
            isLoading={isLoading}
            hideSearch
            selectOptions={[
              { title: 'Currency', options: [{ value: 'All', label: 'All' }] },
              {
                title: 'Sort by',
                options: [
                  { value: 'title', label: 'Title of Account' },
                  { value: 'fcy_amount', label: 'FCy Amount' },
                ],
              },
            ]}
            dateFilters={[{ title: 'Period' }]}
          >
            {(tableData?.length || isError) && (
              <tbody>
                {isError && (
                  <tr>
                    <td
                      colSpan={walkInCustomerOutstandingBalanceHeaders.length}
                    >
                      <p className="text-danger mb-0">
                        Unable to fetch data at this time
                      </p>
                    </td>
                  </tr>
                )}
                {tableData?.map((item) => (
                  <tr key={item.id}>
                    <td>{item.title_of_account}</td>
                    <td>{item.fcy}</td>
                    <td>{item.debit}</td>
                    <td>{item.credit}</td>
                  </tr>
                ))}
              </tbody>
            )}
          </CustomTable>
        </Col>
      </Row>
    </section>
  );
};

export default withFilters(GeneratedWalkInCustomerOutstandingBalance);
