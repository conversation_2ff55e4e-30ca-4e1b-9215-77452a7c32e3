input:checked + .slider:before {
  transform: translateX(20px);
}

.subscription-cards {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.subscription-card {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  width: 47%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.subscription-card h3 {
  font-size: 18px;
  margin-bottom: 10px;
}

.price {
  font-size: 22px;
  font-weight: bold;
}

.price span {
  font-size: 12px;
  color: #777;
}

.buy-btn,
.contact-btn {
  background-color: #fdc770;
  border: none;
  padding: 10px;
  width: 100%;
  margin-top: 10px;
  cursor: pointer;
  font-weight: bold;
  border-radius: 5px;
}

.contact-btn {
  background-color: #ddd;
}

.buy-btn:hover,
.contact-btn:hover {
  opacity: 0.8;
}
.toggle-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin: 20px 0;
}

.toggle-switch span {
  font-weight: bold;
  color: #999; /* Light grey for inactive text */
  transition: color 0.3s ease-in-out;
}

.toggle-switch .active {
  color: black; /* Dark color for active state */
}

.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ddd;
  border-radius: 20px;
  transition: 0.3s;
}

input:checked + .slider {
  background-color: black;
}

.slider:before {
  position: absolute;
  content: '';
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: 0.3s;
}

input:checked + .slider:before {
  transform: translateX(20px);
}
.subscription-card {
  background: var(--body-bg-color);
  display: inline-flex;
  padding-inline: 0.75rem;
  padding-block: 2rem;
  border-radius: 10px;
  border: 1px solid transparent;
  text-align: center;
  max-width: 270px;
  width: 100%;
  height: 375px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: none;
}
.subscription-card:hover {
  border: 1px dotted var(--primary-color);
}
