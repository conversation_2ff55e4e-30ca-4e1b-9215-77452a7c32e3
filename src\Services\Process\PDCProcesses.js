import axiosInstance from '../../Config/axiosConfig';
import {
  pdcProcessReceivablesData,
  pdcProcessPayablesData,
} from '../../Mocks/MockData';

// GET Receivables
export const getPDCProcessReceivables = async (params) => {
  try {
    // const { data } = await axiosInstance.get('/user-api/beneficiary-register', {
    //   params,
    // });
    return pdcProcessReceivablesData.detail;
    return data.detail; // Assume this returns the listing object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
// GET Payables
export const getPDCProcessPayables = async (params) => {
  try {
    // const { data } = await axiosInstance.get('/user-api/beneficiary-register', {
    //   params,
    // });
    return pdcProcessPayablesData.detail;
    return data.detail; // Assume this returns the listing object
  } catch (error) {
    throw error.response
      ? error.response.data
      : { message: 'Unknown error occurred' };
  }
};
