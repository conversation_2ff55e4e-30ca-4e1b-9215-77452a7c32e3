import React from 'react';
import { ErrorMessage, Form, Formik } from 'formik';
import * as Yup from 'yup';
import CustomInput from '../../../../Components/CustomInput';
import CustomButton from '../../../../Components/CustomButton';
import SearchableSelect from '../../../../Components/SearchableSelect/SearchableSelect';
import BackButton from '../../../../Components/BackButton';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '../../../../Hooks/usePageTitle';

const TTRRegisterEditBankDetails = () => {
  usePageTitle('Edit Bank Details');
  const navigate = useNavigate();

  const validationSchema = Yup.object().shape({
    date: Yup.date().required('Date is required'),
    credit_party: Yup.string().required('Credit Party is required'),
    bank_name: Yup.string().required('Bank Name is required'),
    bank_account: Yup.string().required('Bank Account is required'),
    remarks: Yup.string(),
    tmn_amount: Yup.number().required('TMN Amount is required'),
    allocated_amount: Yup.number().required('Allocated Amount is required'),
    confirmed_amount: Yup.number().required('Confirmed Amount is required'),
  });

  const handleSubmit = (values) => {
    console.log('Form values:', values);
  };

  return (
    <>
      <div className="d-flex flex-column gap-2 mb-4">
        <BackButton />
        <h2 className="screen-title m-0 d-inline">Edit Bank Details</h2>
      </div>

      <div className="d-card">
        <div className="row">
          <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
            <Formik
              initialValues={{
                date: '2018-07-22',
                credit_party: 'ABC Trading',
                bank_name: 'ABC Bank',
                bank_account: '**********',
                remarks: 'Lorem ipsum dolor sit amet',
                tmn_amount: '100000',
                allocated_amount: '50000',
                confirmed_amount: '50000',
              }}
              //   validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                setFieldValue,
              }) => (
                <Form>
                  <div className="row mb-4">
                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="date"
                        type="date"
                        label="Date"
                        placeholder="dd/mm/yyyy"
                        value={values.date}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.date && errors.date}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <SearchableSelect
                        label="Credit Party"
                        name="credit_party"
                        options={[]} // Add your credit party options here
                        value={values.credit_party}
                        onChange={(v) => setFieldValue('credit_party', v.value)}
                        placeholder="Select Account"
                      />
                      <ErrorMessage
                        name="credit_party"
                        component="div"
                        className="input-error-message text-danger"
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="bank_name"
                        type="text"
                        label="Bank Name"
                        placeholder="Enter Bank Name"
                        value={values.bank_name}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.bank_name && errors.bank_name}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="bank_account"
                        type="text"
                        label="Bank Account"
                        placeholder="Enter Bank Account"
                        value={values.bank_account}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.bank_account && errors.bank_account}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="remarks"
                        type="text"
                        label="Remarks"
                        placeholder="Enter Remarks"
                        value={values.remarks}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.remarks && errors.remarks}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="tmn_amount"
                        type="number"
                        label="TMN Amount"
                        placeholder="Enter TMN Amount"
                        value={values.tmn_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.tmn_amount && errors.tmn_amount}
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="allocated_amount"
                        type="number"
                        label="Allocated Amount"
                        placeholder="Enter Allocated Amount"
                        value={values.allocated_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.allocated_amount && errors.allocated_amount
                        }
                      />
                    </div>

                    <div className="col-12 col-sm-6 mb-3">
                      <CustomInput
                        name="confirmed_amount"
                        type="number"
                        label="Confirmed Amount"
                        placeholder="Enter Confirmed Amount"
                        value={values.confirmed_amount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.confirmed_amount && errors.confirmed_amount
                        }
                      />
                    </div>
                  </div>

                  <div className="d-flex gap-3 ms-auto">
                    <CustomButton type="submit" text="Save" />
                    <CustomButton
                      variant="secondaryButton"
                      text="Cancel"
                      type="button"
                      onClick={() => {
                        navigate('/transactions/ttr-register');
                      }}
                    />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </>
  );
};

export default TTRRegisterEditBankDetails;
