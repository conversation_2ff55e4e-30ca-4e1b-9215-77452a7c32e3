import { useQuery } from '@tanstack/react-query';
import { getCurrencyRate } from '../Services/General';

/**
 * Custom hook to fetch currency rate for a given currency and date
 * @param {string|number} currencyId - The currency ID to fetch rate for
 * @param {string} date - The date for which to fetch the rate (optional)
 * @param {object} options - Additional React Query options
 * @returns {object} React Query result object with currency rate data
 */
export const useCurrencyRate = (
  currencyId,
  date = new Date().toISOString().split('T')[0],
  options = {}
) => {
  return useQuery({
    queryKey: ['currencyRate', currencyId, date],
    queryFn: () => getCurrencyRate(currencyId, date),
    enabled: !!currencyId, // Only fetch if currencyId is provided
    retry: 1,
    staleTime: 1000 * 20, // Cache data for 20 seconds
    gcTime: 1000 * 20, // Keep in cache for 20 seconds after becoming unused
    refetchOnWindowFocus: false,
    onError: (error) => {
      console.error('Error fetching currency rate:', error);
    },
    ...options, // Allow overriding default options
  });
};

export default useCurrencyRate;
